{"name": "apps", "version": "0.3.1", "workspaces": ["./apps/server", "./apps/client", "./apps/parent", "./apps/shared"], "scripts": {"dev:client": "cd apps/client && bun run dev", "dev:server": "cd apps/server && bun run dev", "dev:parent": "cd apps/parent && bun run dev", "dev:shared": "cd apps/shared && bun run dev", "dev": "concurrently \"bun run dev:shared\" \"bun run dev:server\" \"bun run dev:client\" \"bun run dev:parent\"", "build:client": "cd apps/client && bun run build", "build:shared": "cd apps/shared && bun run build", "build:server": "cd apps/server && bun run build", "build:parent": "cd apps/parent && bun run build", "build": "bun run build:shared && bun run build:server && bun run build:client && bun run build:parent", "build:affected": "bun run scripts/husky/check-changes.ts", "prepare": "husky", "pre-commit": "bun run build:affected", "test": "bun test --env-file=apps/server/.env.test --coverage", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\""}, "keywords": ["bun", "hono", "react", "vite", "monorepo"], "devDependencies": {"bun-types": "latest", "concurrently": "^9.1.2", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "3.5.3"}, "peerDependencies": {"typescript": "^5.0.0"}, "dependencies": {"hono": "^4.7.11"}, "lint-staged": {"apps/**/*.{js,jsx,ts,tsx}": "prettier --write", "apps/**/*.{json,css,md}": "prettier --write"}}