import { execSync } from 'child_process';
import { existsSync } from 'fs';
import { resolve } from 'path';

interface AppConfig {
  name: string;
  path: string;
  dependencies: string[];
}

const APPS: AppConfig[] = [
  {
    name: 'shared',
    path: 'apps/shared',
    dependencies: [],
  },
  {
    name: 'client',
    path: 'apps/client',
    dependencies: ['shared'],
  },
  {
    name: 'server',
    path: 'apps/server',
    dependencies: ['shared'],
  },
  {
    name: 'parent',
    path: 'apps/parent',
    dependencies: ['shared'],
  },
];

const getChangedFiles = (staged: boolean = true) => {
  try {
    const command = staged
      ? 'git diff --cached --name-only'
      : 'git diff --name-only HEAD';
    return execSync(command, { encoding: 'utf-8' }).split('\n').filter(Boolean);
  } catch (error) {
    console.error('Error getting changed files:', error);
    return [];
  }
};

const shouldBuildApp = (app: AppConfig, changedFiles: string[]) => {
  // Check if package.json or lockfile changed
  if (
    changedFiles.some(
      (file) =>
        file === 'package.json' ||
        file === 'bun.lockb' ||
        file === `${app.path}/package.json`
    )
  ) {
    return true;
  }

  // Check if app's own files changed
  if (changedFiles.some((file) => file.startsWith(app.path))) {
    return true;
  }

  // Check if any dependencies changed
  return app.dependencies.some((dep) => {
    const depApp = APPS.find((a) => a.name === dep);
    if (!depApp) return false;
    return changedFiles.some((file) => file.startsWith(depApp.path));
  });
};

const buildApp = async (app: AppConfig) => {
  const buildScript = `build:${app.name}`;
  console.log(`\n🏗️  Building ${app.name}...`);

  try {
    execSync(`bun run ${buildScript}`, {
      stdio: 'inherit',
      cwd: process.cwd(),
    });
    console.log(`✅ Built ${app.name} successfully`);
    return true;
  } catch (error) {
    console.error(`❌ Failed to build ${app.name}:`, error);
    return false;
  }
};

const main = async () => {
  // Get changed files
  const changedFiles = getChangedFiles();

  if (changedFiles.length === 0) {
    console.log('No changes detected!');
    process.exit(0);
  }

  console.log('\nChanged files:');
  console.log(changedFiles.map((f) => `  ${f}`).join('\n'));

  // Determine which apps need building
  const appsToBuild = APPS.filter((app) => shouldBuildApp(app, changedFiles));

  if (appsToBuild.length === 0) {
    console.log('\nNo apps need rebuilding');
    process.exit(0);
  }

  console.log(`\nBuilding apps: ${appsToBuild.map((a) => a.name).join(', ')}`);

  // Build in correct order (dependencies first)
  const results = [];
  for (const app of appsToBuild) {
    const success = await buildApp(app);
    results.push({ app: app.name, success });

    if (!success) {
      console.error('\n❌ Build failed!');
      process.exit(1);
    }
  }

  console.log('\n✅ All builds completed successfully!');
};

main().catch((error) => {
  console.error('Unexpected error:', error);
  process.exit(1);
});
