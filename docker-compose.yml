services:
  # Backend Server (Hono)
  server:
    build:
      context: .
      dockerfile: apps/server/Dockerfile
    ports:
      - "${SERVER_PORT:-3000}:3000"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - PORT=${PORT:-3000}
      - DATABASE_URL=${DATABASE_URL:-postgresql://user:password@localhost:5432/testdb}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS:-http://localhost:5173,http://localhost:5174}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - JWT_SECRET=${JWT_SECRET:-default-jwt-secret-change-me}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Parent Application (React)
  parent:
    build:
      context: .
      dockerfile: apps/parent/Dockerfile
      args:
        - VITE_API_URL=${VITE_API_URL:-http://localhost:3000}
    ports:
      - "${PARENT_PORT:-5174}:80"
    environment:
      - VITE_API_URL=${VITE_API_URL:-http://localhost:3000}
    restart: unless-stopped
    depends_on:
      - server

  # Client Application (React)
  client:
    build:
      context: .
      dockerfile: apps/client/Dockerfile
      args:
        - VITE_API_URL=${VITE_API_URL:-http://localhost:3000}
        - VITE_SUPABASE_URL=${SUPABASE_URL}
        - VITE_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
    ports:
      - "${CLIENT_PORT:-5173}:80"
    environment:
      - VITE_API_URL=${VITE_API_URL:-http://localhost:3000}
    restart: unless-stopped
    depends_on:
      - server

# Usage:
# docker-compose up --build
# docker-compose down
# 
# With custom env file:
# docker-compose --env-file .env.production up --build 

# View all services
##docker-compose ps

# View logs
#docker-compose logs -f

# Stop all services
#docker-compose down

# Restart with fresh build
#docker-compose up --build