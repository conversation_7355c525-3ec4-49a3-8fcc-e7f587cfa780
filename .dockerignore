# Git
.git
.gitignore

# Node
node_modules
apps/*/node_modules
.bun-install

# Build artifacts (we don't need these since Bun runs TS directly)
dist
apps/*/dist
build
apps/*/build
*.tsbuildinfo

# Env files
.env
.env.*
apps/*/.env
apps/*/.env.*

# Docker
Dockerfile
.dockerignore
docker-compose.yml
docker-compose*.yml

# IDE
.vscode
.idea

# OS-specific
.DS_Store

# Tests
**/*.test.ts
**/*.test.tsx
**/*.spec.ts
**/*.spec.tsx
**/tests
**/__tests__

# Documentation
*.md
LICENSE

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Coverage
coverage
.nyc_output

# Development files
.husky
.prettierrc*
.eslintrc*
.gitattributes 