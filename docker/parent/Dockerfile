# Stage 1: Build the React application
FROM oven/bun:1 AS builder

WORKDIR /usr/src/app

# Accept build-time environment variables
ARG VITE_API_URL=http://localhost:3000
ARG VITE_SUPABASE_URL
ARG VITE_SUPABASE_ANON_KEY

# Set environment variables for the build
ENV VITE_API_URL=$VITE_API_URL
ENV VITE_SUPABASE_URL=$VITE_SUPABASE_URL
ENV VITE_SUPABASE_ANON_KEY=$VITE_SUPABASE_ANON_KEY

# Copy the entire monorepo
COPY . .

# Install dependencies for the entire workspace
RUN bun install --frozen-lockfile

# Build dependencies in order: shared, server (for types), then parent
RUN bun run build:shared
RUN bun run build:server
RUN bun run build:parent

# Stage 2: Serve with Caddy
FROM caddy

# Create and change to the app directory
WORKDIR /app

# Copy Caddyfile
COPY docker/parent/Caddyfile ./

# Copy the built static files from the builder stage
COPY --from=builder /usr/src/app/apps/parent/dist ./dist

# Use Caddy to run/serve the app
CMD ["caddy", "run", "--config", "Caddyfile", "--adapter", "caddyfile"] 