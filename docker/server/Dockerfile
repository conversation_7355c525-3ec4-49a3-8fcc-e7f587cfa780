# Use Bun's ability to run TypeScript directly
FROM oven/bun:1 AS production

WORKDIR /usr/src/app

ENV NODE_ENV=production
# Let tools know this is a CI environment to skip dev-only scripts like husky
ENV CI=true

# Copy all source code
COPY . .

# Install only production dependencies
RUN bun install --production --frozen-lockfile --ignore-scripts

# Clean up unnecessary files
RUN rm -rf apps/client apps/parent

EXPOSE 3000

# Bun can run TypeScript directly
CMD ["bun", "run", "apps/server/src/index.ts"] 