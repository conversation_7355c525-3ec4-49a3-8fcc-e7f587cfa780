# Stage 1: Build the React application
FROM oven/bun:1 AS builder

WORKDIR /usr/src/app

# Accept build-time environment variables
ARG VITE_API_URL=http://localhost:3000

# Set environment variables for the build
ENV VITE_API_URL=$VITE_API_URL

# Copy the entire monorepo to ensure workspace integrity
COPY . .

# Install dependencies for the entire workspace
RUN bun install --frozen-lockfile

# Build dependencies in order: shared, server (for types), then client
RUN bun run build:shared
RUN bun run build:server
RUN bun run build:client

# Verify the build output
RUN ls -la apps/client/dist/

# Stage 2: Serve with Caddy
FROM caddy:alpine

# Create and change to the app directory
WORKDIR /app

# Copy Caddyfile
COPY docker/client/Caddyfile ./

# Copy the built static files from the builder stage
COPY --from=builder /usr/src/app/apps/client/dist ./dist

# Verify files are copied correctly
RUN ls -la /app/dist/

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:${PORT:-3000}/health || exit 1

# Use Caddy to run/serve the app
CMD ["caddy", "run", "--config", "Caddyfile", "--adapter", "caddyfile"] 