{
    admin off
    persist_config off
    auto_https off
    log {
        output stdout
        format console
        level DEBUG
    }
}

:{$PORT:3000} {
    log {
        output stdout
        format console
    }
    
    # Respond to health checks
    respond /health 200 {
        body "OK"
    }
    
    # Serve static files
    root * dist
    encode gzip
    file_server {
        hide .git
    }
    
    # Handle SPA routing
    try_files {path} {path}/ /index.html
    
    # Add headers for debugging
    header {
        # CORS headers
        Access-Control-Allow-Origin *
        Access-Control-Allow-Methods "GET, POST, OPTIONS"
        Access-Control-Allow-Headers "Content-Type"
        
        # Caching
        Cache-Control "public, max-age=3600"
    }
} 