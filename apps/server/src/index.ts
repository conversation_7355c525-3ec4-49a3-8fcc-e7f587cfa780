import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { csrf } from 'hono/csrf';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { requestId } from 'hono/request-id';
import { env } from './config/env';
import authRoutes from './apis/auth/auth.routes';
// Lesson and curriculum routes from main branch
import lessonRoutes from './apis/lessons/lessons.routes';
import {
  arenasRoutes,
  curriculumRoutes,
} from './apis/resources/resources.routes';
// Admin and management routes from staging branch
import adminRoutes from './apis/admin/admin.routes';
import studentRoute from './apis/student/student.route';
import horseRoute from './apis/horse/horse.route';
import instructorRoute from './apis/instructor/instructor.route';
import stripeRoutes from './apis/stripe/stripe.routes';
import paymentsRoutes from './apis/payments/payments.routes';

const v1Routes = new Hono()
  .route('/auth', authRoutes)
  // Lesson and curriculum routes (from main)
  .route('/lessons', lessonRoutes)
  .route('/arenas', arenasRoutes)
  .route('/curriculum', curriculumRoutes)
  // Admin and management routes (from staging)
  .route('/admin', adminRoutes)
  .route('/students', studentRoute)
  .route('/horses', horseRoute)
  .route('/instructors', instructorRoute)
  .route('/stripe', stripeRoutes)
  .route('/payments', paymentsRoutes);

// Parse allowed origins from environment or use defaults
const allowedOrigins = env.ALLOWED_ORIGINS
  ? env.ALLOWED_ORIGINS.split(',').map((origin) => origin.trim())
  : ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:5175'];

export const app = new Hono()
  .use(
    '*',
    cors({
      origin: allowedOrigins,
      allowHeaders: [
        'X-Custom-Header',
        'Upgrade-Insecure-Requests',
        'Content-Type',
        'Authorization',
      ],
      allowMethods: ['POST', 'GET', 'DELETE', 'PUT', 'PATCH'],
      exposeHeaders: ['Content-Length', 'X-Kuma-Revision'],
      maxAge: 600,
      credentials: true,
    })
  )

  .use(
    csrf({
      origin: allowedOrigins.slice(0, 5), // Use first two origins for CSRF
    })
  )

  .use(logger())
  .use(prettyJSON())
  .use('*', requestId())

  .get('/', (c) => {
    return c.text('Hello Hono!');
  })

  .get('/health', (c) => {
    return c.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'hono-server',
      environment: env.NODE_ENV,
    });
  })

  .get('/hello', async (c) => {
    const data = {
      message: 'Hello Horse Riding! You are the best!',
      success: true,
    };

    return c.json(data, { status: 200 });
  })

  // Mount all routes
  .route('/api/v1', v1Routes);

// Start the server when this file is run directly
if (import.meta.main) {
  const port = env.PORT || 3000;
  console.log(`🚀 Server is running on port ${port}`);
  console.log(`🏥 Health check available at http://localhost:${port}/health`);
  console.log(`🌍 Environment: ${env.NODE_ENV}`);
}

export default app;

export const server = {
  port: env.PORT || 3000,
  fetch: app.fetch,
};
