export interface SignupTemplateParams {
  name: string;
  password: string;
  studioId: string;
}

export function signupTemplate({
  name,
  password,
  studioId,
}: SignupTemplateParams): string {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome to Horse Riding Studio</title>
  <style>
    body {
      font-family: 'Arial', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
    }
    .email-container {
      max-width: 600px;
      margin: 0 auto;
      background-color: #ffffff;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .header {
      background: linear-gradient(135deg, #8B4513 0%, #D2691E 100%);
      color: white;
      text-align: center;
      padding: 40px 20px;
    }
    .header h1 {
      margin: 0;
      font-size: 28px;
      font-weight: bold;
    }
    .content {
      padding: 40px 30px;
      line-height: 1.6;
      color: #333;
    }
    .greeting {
      font-size: 20px;
      color: #8B4513;
      margin-bottom: 20px;
    }
    .message {
      font-size: 16px;
      margin-bottom: 30px;
    }
    .password-container {
      background-color: #f9f9f9;
      border: 2px dashed #8B4513;
      border-radius: 8px;
      padding: 20px;
      text-align: center;
      margin: 25px 0;
    }
    .password-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 10px;
    }
    .password {
      font-size: 24px;
      font-weight: bold;
      color: #8B4513;
      font-family: 'Courier New', monospace;
      letter-spacing: 2px;
    }
    .login-button {
      display: inline-block;
      background: linear-gradient(135deg, #8B4513 0%, #D2691E 100%);
      color: white;
      padding: 15px 30px;
      text-decoration: none;
      border-radius: 25px;
      font-weight: bold;
      font-size: 16px;
      margin: 20px 0;
      transition: transform 0.2s ease;
    }
    .login-button:hover {
      transform: translateY(-2px);
    }
    .footer {
      background-color: #f8f8f8;
      text-align: center;
      padding: 30px 20px;
      border-top: 1px solid #eee;
    }
    .footer p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
    .note {
      background-color: #fff3cd;
      border-left: 4px solid #ffc107;
      padding: 15px;
      margin: 20px 0;
      font-size: 14px;
      color: #856404;
    }
    .horse-emoji {
      font-size: 24px;
      margin: 0 10px;
    }
  </style>
</head>
<body>
  <div class="email-container">
    <div class="header">
      <h1><span class="horse-emoji">🐎</span>Welcome to Horse Riding Studio<span class="horse-emoji">🐎</span></h1>
    </div>
    
    <div class="content">
      <div class="greeting">Hi ${name}!</div>
      
      <div class="message">
        Thank you for signing up with our Horse Riding Studio! We're excited to have you join our equestrian community.
      </div>
      
      <div class="password-container">
        <div class="password-label">Your One-Time Password:</div>
        <div class="password">${password}</div>
      </div>
      
      <div class="note">
        <strong>Important:</strong> This is a temporary password. Please use it to log in and set up your permanent password.
      </div>
      
      <div style="text-align: center;">
        <a href="http://localhost:5174/login?studio_id=${studioId}" class="login-button">
          Login to Your Account
        </a>
      </div>
      
      <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
      
      <p>Happy riding!</p>
    </div>
    
    <div class="footer">
      <p>© 2025 Horse Riding Studio. All rights reserved.</p>
      <p>This email was sent because you signed up for an account.</p>
    </div>
  </div>
</body>
</html>
  `.trim();
}
