import {
  eq,
  and,
  inArray,
  gte,
  lte,
  desc,
  asc,
  count,
  ilike,
  between,
} from 'drizzle-orm';
import { db } from '../../../db';
import * as schema from '../../../db/schema';
import type {
  CreateCurriculumInput,
  UpdateCurriculumInput,
  GetCurriculumQuery,
  UpdateProgressInput,
} from '../validators/curriculum.validator';

export class CurriculumRepository {
  // Create a new curriculum
  static async createCurriculum(curriculumData: CreateCurriculumInput) {
    const [curriculum] = await db
      .insert(schema.curriculumTable)
      .values({
        name: curriculumData.name,
        description: curriculumData.description || null,
        skill_level: curriculumData.skill_level,
        category: curriculumData.category,
        duration_minutes: curriculumData.duration_minutes || null,
        estimated_lessons: curriculumData.estimated_lessons,
        prerequisites: curriculumData.prerequisites,
        learning_objectives: curriculumData.learning_objectives,
        milestones: curriculumData.milestones,
        assessment_criteria: curriculumData.assessment_criteria,
        resources: curriculumData.resources,
        difficulty_rating: curriculumData.difficulty_rating.toString(),
        version: curriculumData.version,
        is_active: curriculumData.is_active ? 1 : 0,
      })
      .returning();

    return {
      ...curriculum,
      is_active: Boolean(curriculum.is_active),
      difficulty_rating: parseFloat(curriculum.difficulty_rating || '1.0'),
      prerequisites: curriculum.prerequisites || [],
      learning_objectives: curriculum.learning_objectives || [],
      milestones: curriculum.milestones || [],
      assessment_criteria: curriculum.assessment_criteria || [],
      resources: curriculum.resources || [],
    };
  }

  // Get all curriculum with filtering
  static async getCurriculum(filters: GetCurriculumQuery) {
    const {
      skill_level,
      category,
      is_active,
      search,
      difficulty_min,
      difficulty_max,
      page = 1,
      limit = 20,
      sort_by = 'name',
      sort_order = 'asc',
    } = filters;

    // Build where conditions
    const whereConditions = [];

    if (skill_level) {
      whereConditions.push(eq(schema.curriculumTable.skill_level, skill_level));
    }

    if (category) {
      whereConditions.push(eq(schema.curriculumTable.category, category));
    }

    if (is_active !== undefined) {
      whereConditions.push(
        eq(schema.curriculumTable.is_active, is_active ? 1 : 0)
      );
    }

    if (search) {
      whereConditions.push(ilike(schema.curriculumTable.name, `%${search}%`));
    }

    if (difficulty_min !== undefined && difficulty_max !== undefined) {
      whereConditions.push(
        between(
          schema.curriculumTable.difficulty_rating,
          difficulty_min.toString(),
          difficulty_max.toString()
        )
      );
    } else if (difficulty_min !== undefined) {
      whereConditions.push(
        gte(schema.curriculumTable.difficulty_rating, difficulty_min.toString())
      );
    } else if (difficulty_max !== undefined) {
      whereConditions.push(
        lte(schema.curriculumTable.difficulty_rating, difficulty_max.toString())
      );
    }

    const whereClause =
      whereConditions.length > 0 ? and(...whereConditions) : undefined;

    // Get total count
    const [{ total }]: any[] = await db
      .select({ total: count() })
      .from(schema.curriculumTable)
      .where(whereClause);

    // Determine sort order
    const sortColumn =
      sort_by === 'name'
        ? schema.curriculumTable.name
        : sort_by === 'skill_level'
          ? schema.curriculumTable.skill_level
          : sort_by === 'difficulty_rating'
            ? schema.curriculumTable.difficulty_rating
            : schema.curriculumTable.created_at;

    const orderBy = sort_order === 'asc' ? asc(sortColumn) : desc(sortColumn);

    // Get curriculum with progress statistics
    const offset = (page - 1) * limit;
    const curriculumItems = await db.query.curriculumTable.findMany({
      where: whereClause,
      with: {
        progress: {
          columns: {
            id: true,
            status: true,
            progress_percentage: true,
          },
        },
      },
      orderBy: [orderBy],
      limit,
      offset,
    });

    return {
      curriculum: curriculumItems.map((item) => ({
        ...item,
        is_active: Boolean(item.is_active),
        difficulty_rating: parseFloat(item.difficulty_rating || '1.0'),
        prerequisites: item.prerequisites || [],
        learning_objectives: item.learning_objectives || [],
        milestones: item.milestones || [],
        assessment_criteria: item.assessment_criteria || [],
        resources: item.resources || [],
        progress_stats: {
          total_students: (item as any).progress?.length || 0,
          completed:
            (item as any).progress?.filter((p: any) => p.status === 'completed')
              .length || 0,
          in_progress:
            (item as any).progress?.filter(
              (p: any) => p.status === 'in_progress'
            ).length || 0,
          average_progress:
            (item as any).progress?.length > 0
              ? (item as any).progress.reduce(
                  (sum: number, p: any) =>
                    sum + parseFloat(p.progress_percentage || '0'),
                  0
                ) / (item as any).progress.length
              : 0,
        },
      })),
      total: Number(total),
      page,
      limit,
      total_pages: Math.ceil(Number(total) / limit),
    };
  }

  // Get curriculum by ID
  static async getCurriculumById(id: number) {
    const curriculum = await db.query.curriculumTable.findFirst({
      where: eq(schema.curriculumTable.id, id),
      with: {
        progress: {
          with: {
            student: {
              columns: {
                id: true,
                first_name: true,
                last_name: true,
              },
            },
            instructor: {
              columns: {
                id: true,
                first_name: true,
                last_name: true,
              },
            },
          },
        },
      },
    });

    if (!curriculum) {
      return null;
    }

    return {
      ...curriculum,
      is_active: Boolean(curriculum.is_active),
      difficulty_rating: parseFloat(curriculum.difficulty_rating || '1.0'),
      prerequisites: curriculum.prerequisites || [],
      learning_objectives: curriculum.learning_objectives || [],
      milestones: curriculum.milestones || [],
      assessment_criteria: curriculum.assessment_criteria || [],
      resources: curriculum.resources || [],
      progress_records: (curriculum as any).progress || [],
    };
  }

  // Update curriculum
  static async updateCurriculum(id: number, updateData: UpdateCurriculumInput) {
    const updateFields: any = {};

    if (updateData.name !== undefined) updateFields.name = updateData.name;
    if (updateData.description !== undefined)
      updateFields.description = updateData.description;
    if (updateData.skill_level !== undefined)
      updateFields.skill_level = updateData.skill_level;
    if (updateData.category !== undefined)
      updateFields.category = updateData.category;
    if (updateData.duration_minutes !== undefined)
      updateFields.duration_minutes = updateData.duration_minutes;
    if (updateData.estimated_lessons !== undefined)
      updateFields.estimated_lessons = updateData.estimated_lessons;
    if (updateData.prerequisites !== undefined)
      updateFields.prerequisites = updateData.prerequisites;
    if (updateData.learning_objectives !== undefined)
      updateFields.learning_objectives = updateData.learning_objectives;
    if (updateData.milestones !== undefined)
      updateFields.milestones = updateData.milestones;
    if (updateData.assessment_criteria !== undefined)
      updateFields.assessment_criteria = updateData.assessment_criteria;
    if (updateData.resources !== undefined)
      updateFields.resources = updateData.resources;
    if (updateData.difficulty_rating !== undefined)
      updateFields.difficulty_rating = updateData.difficulty_rating.toString();
    if (updateData.version !== undefined)
      updateFields.version = updateData.version;
    if (updateData.is_active !== undefined)
      updateFields.is_active = updateData.is_active ? 1 : 0;

    const [updatedCurriculum] = await db
      .update(schema.curriculumTable)
      .set(updateFields)
      .where(eq(schema.curriculumTable.id, id))
      .returning();

    if (!updatedCurriculum) {
      return null;
    }

    return {
      ...updatedCurriculum,
      is_active: Boolean(updatedCurriculum.is_active),
      difficulty_rating: parseFloat(
        updatedCurriculum.difficulty_rating || '1.0'
      ),
      prerequisites: updatedCurriculum.prerequisites || [],
      learning_objectives: updatedCurriculum.learning_objectives || [],
      milestones: updatedCurriculum.milestones || [],
      assessment_criteria: updatedCurriculum.assessment_criteria || [],
      resources: updatedCurriculum.resources || [],
    };
  }

  // Delete curriculum (soft delete)
  static async deleteCurriculum(id: number) {
    // Check if curriculum has any progress records
    const progressRecords = await db.query.curriculumProgressTable.findMany({
      where: eq(schema.curriculumProgressTable.curriculum_id, id),
      columns: { id: true },
    });

    if (progressRecords.length > 0) {
      throw new Error(
        'Cannot delete curriculum with existing progress records'
      );
    }

    const [deletedCurriculum] = await db
      .update(schema.curriculumTable)
      .set({ deleted_at: new Date() })
      .where(eq(schema.curriculumTable.id, id))
      .returning();

    return deletedCurriculum;
  }

  // Get curriculum progress for a student
  static async getCurriculumProgress(curriculumId: number, studentId: string) {
    const progress = await db.query.curriculumProgressTable.findFirst({
      where: and(
        eq(schema.curriculumProgressTable.curriculum_id, curriculumId),
        eq(schema.curriculumProgressTable.student_id, studentId)
      ),
      with: {
        curriculum: true,
        student: {
          columns: {
            id: true,
            first_name: true,
            last_name: true,
          },
        },
        instructor: {
          columns: {
            id: true,
            first_name: true,
            last_name: true,
          },
        },
      },
    });

    if (!progress) {
      return null;
    }

    return {
      ...progress,
      progress_percentage: parseFloat(progress.progress_percentage || '0'),
      completed_milestones: progress.completed_milestones || [],
      skills_mastered: progress.skills_mastered || [],
      assessment_scores: progress.assessment_scores || {},
    };
  }

  // Update curriculum progress for a student
  static async updateCurriculumProgress(
    curriculumId: number,
    studentId: string,
    instructorId: string,
    updateData: UpdateProgressInput
  ) {
    // Check if progress record exists
    const existingProgress = await db.query.curriculumProgressTable.findFirst({
      where: and(
        eq(schema.curriculumProgressTable.curriculum_id, curriculumId),
        eq(schema.curriculumProgressTable.student_id, studentId)
      ),
    });

    const progressData: any = {
      curriculum_id: curriculumId,
      student_id: studentId,
      instructor_id: instructorId,
      ...updateData,
    };

    if (updateData.progress_percentage !== undefined) {
      progressData.progress_percentage =
        updateData.progress_percentage.toString();
    }

    if (existingProgress) {
      // Update existing progress
      const [updatedProgress] = await db
        .update(schema.curriculumProgressTable)
        .set(progressData)
        .where(
          and(
            eq(schema.curriculumProgressTable.curriculum_id, curriculumId),
            eq(schema.curriculumProgressTable.student_id, studentId)
          )
        )
        .returning();

      return {
        ...updatedProgress,
        progress_percentage: parseFloat(
          updatedProgress.progress_percentage || '0'
        ),
        completed_milestones: updatedProgress.completed_milestones || [],
        skills_mastered: updatedProgress.skills_mastered || [],
        assessment_scores: updatedProgress.assessment_scores || {},
      };
    } else {
      // Create new progress record
      const [newProgress] = await db
        .insert(schema.curriculumProgressTable)
        .values(progressData)
        .returning();

      return {
        ...newProgress,
        progress_percentage: parseFloat(newProgress.progress_percentage || '0'),
        completed_milestones: newProgress.completed_milestones || [],
        skills_mastered: newProgress.skills_mastered || [],
        assessment_scores: newProgress.assessment_scores || {},
      };
    }
  }

  // Validate curriculum exists and is active
  static async validateCurriculum(curriculumId: number) {
    const curriculum = await db.query.curriculumTable.findFirst({
      where: and(
        eq(schema.curriculumTable.id, curriculumId),
        eq(schema.curriculumTable.is_active, 1)
      ),
    });

    return curriculum;
  }

  // Get curriculum categories
  static async getCurriculumCategories() {
    const categories = await db
      .selectDistinct({ category: schema.curriculumTable.category })
      .from(schema.curriculumTable)
      .where(eq(schema.curriculumTable.is_active, 1));

    return categories.map((c) => c.category);
  }
}
