import { <PERSON>o } from 'hono';
import { authValidator } from '../../middlewares/auth/auth.middleware';
import { adminMiddleware } from '../../middlewares/auth/admin.middleware';
import createCurriculumController from './controllers/createCurriculum.controller';
import getCurriculumController from './controllers/getCurriculum.controller';
import getCurriculumByIdController from './controllers/getCurriculumById.controller';
import updateCurriculumController from './controllers/updateCurriculum.controller';
import deleteCurriculumController from './controllers/deleteCurriculum.controller';
import getCurriculumProgressController from './controllers/getCurriculumProgress.controller';
import updateCurriculumProgressController from './controllers/updateCurriculumProgress.controller';

const curriculumRoutes = new Hono()
  // Curriculum CRUD - Admin only for create/update/delete
  .post('/', authValidator, adminMiddleware, createCurriculumController)
  .get('/', authValidator, getCurriculumController)
  .get('/:id', authValidator, getCurriculumByIdController)
  .put('/:id', authValidator, adminMiddleware, updateCurriculumController)
  .delete('/:id', authValidator, adminMiddleware, deleteCurriculumController)

  // Progress tracking - Available to instructors and admins
  .get(
    '/:id/progress/:studentId',
    authValidator,
    getCurriculumProgressController
  )
  .put(
    '/:id/progress/:studentId',
    authValidator,
    updateCurriculumProgressController
  );

export default curriculumRoutes;
