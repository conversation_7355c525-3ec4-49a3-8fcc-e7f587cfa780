import { z } from 'zod';

const milestoneSchema = z.object({
  id: z.number(),
  name: z.string().min(1, 'Milestone name is required'),
  description: z.string().optional(),
  order: z.number().min(1),
  required: z.boolean().default(true),
});

const learningObjectiveSchema = z.object({
  id: z.number(),
  objective: z.string().min(1, 'Learning objective is required'),
  category: z.string().optional(),
});

const assessmentCriteriaSchema = z.object({
  id: z.number(),
  criteria: z.string().min(1, 'Assessment criteria is required'),
  weight: z.number().min(0).max(100).default(1), // Weight in percentage
  passing_score: z.number().min(0).max(100).default(70),
});

const resourceSchema = z.object({
  id: z.number(),
  name: z.string().min(1, 'Resource name is required'),
  type: z.enum(['video', 'document', 'link', 'image']),
  url: z.string().url().optional(),
  description: z.string().optional(),
});

export const createCurriculumSchema = z.object({
  name: z
    .string()
    .min(1, 'Curriculum name is required')
    .max(255, 'Name too long'),
  description: z.string().optional(),
  skill_level: z.enum(['beginner', 'intermediate', 'advanced']),
  category: z
    .string()
    .min(1, 'Category is required')
    .max(100, 'Category too long')
    .default('general'),
  duration_minutes: z.number().min(1, 'Duration must be positive').optional(),
  estimated_lessons: z
    .number()
    .min(1, 'Must have at least 1 lesson')
    .default(1),
  prerequisites: z.array(z.number()).default([]),
  learning_objectives: z.array(learningObjectiveSchema).default([]),
  milestones: z.array(milestoneSchema).default([]),
  assessment_criteria: z.array(assessmentCriteriaSchema).default([]),
  resources: z.array(resourceSchema).default([]),
  difficulty_rating: z.number().min(1).max(10).default(1),
  version: z.string().max(20, 'Version too long').default('1.0'),
  is_active: z.boolean().default(true),
});

export const updateCurriculumSchema = z.object({
  name: z
    .string()
    .min(1, 'Curriculum name is required')
    .max(255, 'Name too long')
    .optional(),
  description: z.string().optional(),
  skill_level: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
  category: z
    .string()
    .min(1, 'Category is required')
    .max(100, 'Category too long')
    .optional(),
  duration_minutes: z.number().min(1, 'Duration must be positive').optional(),
  estimated_lessons: z
    .number()
    .min(1, 'Must have at least 1 lesson')
    .optional(),
  prerequisites: z.array(z.number()).optional(),
  learning_objectives: z.array(learningObjectiveSchema).optional(),
  milestones: z.array(milestoneSchema).optional(),
  assessment_criteria: z.array(assessmentCriteriaSchema).optional(),
  resources: z.array(resourceSchema).optional(),
  difficulty_rating: z.number().min(1).max(10).optional(),
  version: z.string().max(20, 'Version too long').optional(),
  is_active: z.boolean().optional(),
});

export const curriculumParamsSchema = z.object({
  id: z.string().transform(Number),
});

export const getCurriculumQuerySchema = z.object({
  skill_level: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
  category: z.string().optional(),
  is_active: z
    .string()
    .transform((val) => val === 'true')
    .optional(),
  search: z.string().optional(),
  difficulty_min: z.string().transform(Number).optional(),
  difficulty_max: z.string().transform(Number).optional(),
  page: z.string().transform(Number).default('1'),
  limit: z.string().transform(Number).default('20'),
  sort_by: z
    .enum(['name', 'skill_level', 'difficulty_rating', 'created_at'])
    .default('name'),
  sort_order: z.enum(['asc', 'desc']).default('asc'),
});

export const updateProgressSchema = z.object({
  status: z
    .enum(['not_started', 'in_progress', 'completed', 'on_hold'])
    .optional(),
  progress_percentage: z.number().min(0).max(100).optional(),
  completed_milestones: z.array(z.number()).optional(),
  current_milestone: z.number().optional(),
  notes: z.string().optional(),
  skills_mastered: z.array(z.number()).optional(),
  assessment_scores: z.record(z.number()).optional(), // Object with assessment ID as key, score as value
  completion_date: z
    .string()
    .transform((date) => new Date(date))
    .optional(),
  last_lesson_date: z
    .string()
    .transform((date) => new Date(date))
    .optional(),
});

export const progressParamsSchema = z.object({
  id: z.string().transform(Number),
  studentId: z.string(),
});

export type CreateCurriculumInput = z.infer<typeof createCurriculumSchema>;
export type UpdateCurriculumInput = z.infer<typeof updateCurriculumSchema>;
export type CurriculumParams = z.infer<typeof curriculumParamsSchema>;
export type GetCurriculumQuery = z.infer<typeof getCurriculumQuerySchema>;
export type UpdateProgressInput = z.infer<typeof updateProgressSchema>;
export type ProgressParams = z.infer<typeof progressParamsSchema>;
export type Milestone = z.infer<typeof milestoneSchema>;
export type LearningObjective = z.infer<typeof learningObjectiveSchema>;
export type AssessmentCriteria = z.infer<typeof assessmentCriteriaSchema>;
export type Resource = z.infer<typeof resourceSchema>;
