import type { Context } from 'hono';
import createUserHandler from '../handlers/createUser.handler';
import type { CreateUserInput } from '../validators/createUser.validator';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

type roleType = 'super-admin' | 'admin' | 'instructor';

export const createUserController = async (c: Context) => {
  const requestId = c.get('requestId');
  const studioId = c.req.query('studio_id')!;
  const role = c.req.query('role')!;
  console.log(
    `${requestId} [ADMIN] - CREATE_USER - Request received for role: ${role}`
  );

  try {
    const payload = (await c.req.json()) as CreateUserInput;
    const {
      email,
      first_name,
      last_name,
      password,
      address,
      country_code,
      date_of_birth,
      emergency_contact_name,
      emergency_contact_phone,
      phone,
      profile_image,
    } = payload;

    const result = await createUserHandler(
      {
        email,
        first_name,
        last_name,
        password,
        address,
        country_code,
        date_of_birth,
        emergency_contact_name,
        emergency_contact_phone,
        phone,
        profile_image,
      },
      studioId,
      role as roleType,
      requestId
    );

    console.log(
      `${requestId} [ADMIN] - CREATE_USER - Handler result:`,
      JSON.stringify(result, null, 2)
    );
    console.log(
      `${requestId} [ADMIN] - CREATE_USER - Response sent successfully`
    );

    if (result.error) {
      return c.json(
        { data: null, error: result.error },
        (result.error?.statusCode as ContentfulStatusCode) || 500
      );
    }

    return c.json({ data: result.data, error: result.error }, 201);
  } catch (err) {
    console.error(
      `${requestId} [ADMIN] - CREATE_USER - Controller error: ${err}`
    );
    return c.json(
      { data: null, error: { message: 'Internal server error' } },
      500
    );
  }
};
