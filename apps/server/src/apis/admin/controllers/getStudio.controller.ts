import type { Context } from 'hono';
import getStudioHandler from '../handlers/getStudio.handler';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const getStudioController = async (c: Context) => {
  const requestId = c.get('requestId');
  console.log(`${requestId} [ADMIN] - GET_STUDIOS - Request received`);

  try {
    // Get pagination parameters from query
    const pageParam = c.req.query('page');
    const limitParam = c.req.query('limit');

    // Parse and validate pagination parameters
    const page = pageParam ? parseInt(pageParam, 10) : 1;
    const limit = limitParam ? parseInt(limitParam, 10) : 10;

    // Validate pagination parameters
    if (isNaN(page) || page < 1) {
      console.error(
        `${requestId} [ADMIN] - GET_STUDIOS - Invalid page parameter`
      );
      return c.json(
        { data: null, error: { message: 'Page must be a positive integer' } },
        400
      );
    }

    if (isNaN(limit) || limit < 1 || limit > 100) {
      console.error(
        `${requestId} [ADMIN] - GET_STUDIOS - Invalid limit parameter`
      );
      return c.json(
        { data: null, error: { message: 'Limit must be between 1 and 100' } },
        400
      );
    }

    const result = await getStudioHandler(page, limit, requestId);

    if (result.error) {
      console.error(
        `${requestId} [ADMIN] - GET_STUDIOS - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: { message: result.error.message } },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(
      `${requestId} [ADMIN] - GET_STUDIOS - Response sent successfully`
    );
    return c.json({ data: result.data, error: result.error }, 200);
  } catch (err) {
    console.error(
      `${requestId} [ADMIN] - GET_STUDIOS - Controller error: ${err}`
    );
    return c.json(
      { data: null, error: { message: 'Internal server error' } },
      500
    );
  }
};
