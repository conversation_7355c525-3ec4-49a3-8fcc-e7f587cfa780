import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { validator } from 'hono/validator';
import { db } from '../../../db';
import { studioTable } from '../../../db/schema/studio';
import { eq } from 'drizzle-orm';

export type CreateUserInput = z.infer<typeof createUserSchema>;

export const createUserSchema = z.object({
  email: z.string().email().max(100),
  password: z.string().min(6).max(30),
  first_name: z.string().min(1).max(30),
  last_name: z.string().min(1).max(30),
  phone: z.string().min(7).max(15).optional(),
  country_code: z
    .string()
    .regex(/^\+\d{1,4}$/, 'Invalid country code')
    .optional(),
  address: z.string().max(255).optional(),
  date_of_birth: z.coerce.date().optional(),
  profile_image: z.string().url().optional(),
  emergency_contact_phone: z.string().min(7).max(15).optional(),
  emergency_contact_name: z.string().min(1).max(30).optional(),
});

export const validateCreateUser = (
  target: 'json' | 'form' | 'query' | 'param' | 'header' | 'cookie' = 'json'
) => zValidator(target, createUserSchema);

export const validateCreateUserQuery = validator('query', async (value, c) => {
  const studio_id = value['studio_id'];
  const role = value['role'];

  if (!studio_id) {
    return c.json(
      {
        success: false,
        data: null,
        error: {
          message: 'Studio ID is required.',
        },
      },
      400
    );
  }

  if (!role) {
    return c.json(
      {
        success: false,
        data: null,
        error: {
          message: 'Role is required.',
        },
      },
      400
    );
  }

  // Ensure role is a string (query params can be arrays)
  const roleString = Array.isArray(role) ? role[0] : role;

  // Check if we have a valid string
  if (typeof roleString !== 'string') {
    return c.json(
      {
        success: false,
        data: null,
        error: {
          message: 'Invalid role format.',
        },
      },
      400
    );
  }

  // Validate role
  const validRoles = ['admin', 'super-admin', 'instructor'];
  if (!validRoles.includes(roleString)) {
    return c.json(
      {
        success: false,
        data: null,
        error: {
          message:
            'Invalid role. Must be one of: admin, super-admin, instructor',
        },
      },
      400
    );
  }

  if (studio_id) {
    try {
      // Ensure studio_id is a string (query params can be arrays)
      const studioIdString = Array.isArray(studio_id)
        ? studio_id[0]
        : studio_id;

      // Check if we have a valid string
      if (typeof studioIdString !== 'string') {
        return c.json(
          {
            success: false,
            data: null,
            error: {
              message: 'Invalid studio ID format.',
            },
          },
          400
        );
      }

      // Check if studio exists in database
      const studio = await db
        .select()
        .from(studioTable)
        .where(eq(studioTable.id, studioIdString))
        .limit(1);

      if (studio.length === 0) {
        return c.json(
          {
            success: false,
            data: null,
            error: {
              message:
                'Studio not found. The provided studio ID does not exist in our records.',
            },
          },
          400
        );
      }
    } catch (error) {
      return c.json(
        {
          success: false,
          data: null,
          error: {
            message: 'Failed to validate studio information. Please try again.',
          },
        },
        400
      );
    }
  }

  // If trying to create a super-admin, only an owner can do this
  if (roleString === 'super-admin') {
    const user = c.get('user');
    const creatorRole = user?.user_metadata?.role;
    if (creatorRole !== 'owner') {
      return c.json(
        {
          success: false,
          data: null,
          error: {
            message: 'Only an owner can create a super-admin user.',
          },
        },
        403
      );
    }
  }

  return { studio_id, role: roleString };
});
