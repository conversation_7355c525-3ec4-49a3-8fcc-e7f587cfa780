import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';

// Schema for query parameters validation
const getParentsQuerySchema = z.object({
  page: z
    .string()
    .optional()
    .refine((val) => !val || (!isNaN(Number(val)) && Number(val) > 0), {
      message: 'Page must be a positive number',
    }),
  limit: z
    .string()
    .optional()
    .refine(
      (val) =>
        !val || (!isNaN(Number(val)) && Number(val) > 0 && Number(val) <= 100),
      {
        message: 'Limit must be a number between 1 and 100',
      }
    ),
});

export const validateGetParentsQuery = zValidator(
  'query',
  getParentsQuerySchema
);
