import { Hono } from 'hono';
import { createUserController } from './controllers/createUser.controller';
import { getStudioController } from './controllers/getStudio.controller';
import { getParentsController } from './controllers/getParents.controller';
import {
  validateCreateUser,
  validateCreateUserQuery,
} from './validators/createUser.validator';
import { validateGetParentsQuery } from './validators/getParents.validator';
import { adminMiddleware } from '@server/middlewares/admin/admin.middleware';

const adminRoutes = new Hono()
  .post(
    '/users',
    adminMiddleware(['super-admin', 'owner']),
    validateCreateUserQuery,
    validateCreateUser('json'),
    createUserController
  )
  .get('/studios', adminMiddleware(['owner']), getStudioController)
  .get(
    '/parents',
    adminMiddleware(['super-admin', 'owner', 'admin']),
    validateGetParentsQuery,
    getParentsController
  );

export default adminRoutes;
