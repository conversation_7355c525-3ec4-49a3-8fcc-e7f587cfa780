import type {
  typeResult,
  typeResultData,
  typeResultError,
} from '../../../../../shared/src/types/studio/getStudio.types';
import getStudioRepository from '../repository/getStudio.repository';
import type { CustomError } from '@server/utils/customError';

export default async function getStudioHandler(
  page: number = 1,
  limit: number = 10,
  requestId: string
): Promise<typeResult> {
  let data: typeResultData | null = null;
  let error: typeResultError | null = null;

  try {
    console.info(`${requestId} [ADMIN] - GET_STUDIOS handler started`);

    // Validate pagination parameters
    if (page < 1) {
      throw new Error('Page must be greater than 0');
    }
    if (limit < 1 || limit > 100) {
      throw new Error('Limit must be between 1 and 100');
    }

    const { isSuccess, data: studioData } = await getStudioRepository(
      page,
      limit,
      requestId
    );

    if (isSuccess) {
      data = studioData as typeResultData;
    }

    console.info(
      `${requestId} [ADMIN] - GET_STUDIOS handler completed successfully`
    );
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [ADMIN] - GET_STUDIOS handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
