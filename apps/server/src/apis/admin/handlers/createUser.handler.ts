import type { CustomError } from '@server/utils/customError';
import type { CreateUserInput } from '../validators/createUser.validator';
import signUpAuthRepository from '@server/apis/auth/repository/signUp.repository';
import type { AdminCreateUser } from 'shared';

// Use the imported type alias
type typeResult = AdminCreateUser.typeResult;

export default async function createUserHandler(
  payload: CreateUserInput,
  studioId: string,
  role: 'super-admin' | 'admin' | 'instructor',
  requestId: string
): Promise<typeResult> {
  let data: typeResult['data'] = null;
  let error: typeResult['error'] = null;

  try {
    console.info(
      `${requestId} [ADMIN] - CREATE_USER handler started for role: ${role}`
    );

    const { isSuccess } = await signUpAuthRepository(
      payload,
      role,
      studioId,
      requestId
    );

    if (isSuccess) {
      data = {
        message: `${role.charAt(0).toUpperCase() + role.slice(1)} user created successfully. A verification email has been sent to their inbox.`,
      };
    }

    console.info(
      `${requestId} [ADMIN] - CREATE_USER handler completed successfully`
    );
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [ADMIN] - CREATE_USER handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
