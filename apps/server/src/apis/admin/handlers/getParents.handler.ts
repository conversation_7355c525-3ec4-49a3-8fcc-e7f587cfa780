import getParentsRepository from '../repository/getParents.repository';
import type { CustomError } from '@server/utils/customError';

// Types for getParents API
type typeResultData = {
  parents: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
    studio_id: string;
    role: string;
    phone: string | null;
    country_code: string | null;
    address: string | null;
    date_of_birth: string | null;
    profile_image: string | null;
    emergency_contact_phone: string | null;
    emergency_contact_name: string | null;
    created_at: Date;
    updated_at: Date | null;
  }[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
};

type typeResultError = {
  code: string;
  message: string;
  statusCode: number;
};

type typeResult = {
  data: null | typeResultData;
  error: null | typeResultError;
};

export default async function getParentsHandler(
  page: number = 1,
  limit: number = 10,
  studioId: string,
  requestId: string
): Promise<typeResult> {
  let data: typeResultData | null = null;
  let error: typeResultError | null = null;

  try {
    console.info(`${requestId} [ADMIN] - GET_PARENTS handler started`);

    // Validate pagination parameters
    if (page < 1) {
      throw new Error('Page must be greater than 0');
    }
    if (limit < 1 || limit > 100) {
      throw new Error('Limit must be between 1 and 100');
    }

    const { isSuccess, data: parentsData } = await getParentsRepository(
      page,
      limit,
      studioId,
      requestId
    );

    if (isSuccess) {
      data = parentsData as typeResultData;
    }

    console.info(
      `${requestId} [ADMIN] - GET_PARENTS handler completed successfully`
    );
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [ADMIN] - GET_PARENTS handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
