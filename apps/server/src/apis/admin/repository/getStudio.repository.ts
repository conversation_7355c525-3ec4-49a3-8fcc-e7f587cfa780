import { db } from '@server/db';
import { studioTable } from '@server/db/schema/studio';
import { count } from 'drizzle-orm';

export default async function getStudioRepository(
  page: number = 1,
  limit: number = 10,
  requestId: string
) {
  try {
    console.info(
      `${requestId} [ADMIN] - GET_STUDIOS repository operation started`
    );

    // Calculate offset
    const offset = (page - 1) * limit;

    // Get total count
    const totalResult = await db.select({ count: count() }).from(studioTable);
    const total = totalResult[0]?.count ?? 0;

    // Get paginated studios
    const studios = await db
      .select({
        id: studioTable.id,
        name: studioTable.name,
        email: studioTable.email,
        phone: studioTable.phone,
        address: studioTable.address,
        logo_url: studioTable.logo_url,
        timezone: studioTable.timezone,
        currency: studioTable.currency,
        created_at: studioTable.created_at,
        updated_at: studioTable.updated_at,
      })
      .from(studioTable)
      .limit(limit)
      .offset(offset)
      .orderBy(studioTable.created_at);

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    console.info(
      `${requestId} [ADMIN] - GET_STUDIOS repository operation completed`
    );
    return {
      isSuccess: true,
      data: {
        studios,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNextPage,
          hasPrevPage,
        },
      },
    };
  } catch (err) {
    console.error(
      `${requestId} [ADMIN] - GET_STUDIOS repository operation failed ${err}`
    );
    throw err;
  }
}
