import { db } from '@server/db';
import { usersTable } from '@server/db/schema/user';
import { count, eq, and } from 'drizzle-orm';

export default async function getParentsRepository(
  page: number = 1,
  limit: number = 10,
  studioId: string,
  requestId: string
) {
  try {
    console.info(
      `${requestId} [ADMIN] - GET_PARENTS repository operation started`
    );

    // Calculate offset
    const offset = (page - 1) * limit;

    // Get total count of parents for the specific studio
    const totalResult = await db
      .select({ count: count() })
      .from(usersTable)
      .where(
        and(eq(usersTable.role, 'parent'), eq(usersTable.studio_id, studioId))
      );
    const total = totalResult[0]?.count ?? 0;

    // Get paginated parents for the specific studio
    const parents = await db
      .select({
        id: usersTable.id,
        email: usersTable.email,
        first_name: usersTable.first_name,
        last_name: usersTable.last_name,
        studio_id: usersTable.studio_id,
        role: usersTable.role,
        phone: usersTable.phone,
        country_code: usersTable.country_code,
        address: usersTable.address,
        date_of_birth: usersTable.date_of_birth,
        profile_image: usersTable.profile_image,
        emergency_contact_phone: usersTable.emergency_contact_phone,
        emergency_contact_name: usersTable.emergency_contact_name,
        created_at: usersTable.created_at,
        updated_at: usersTable.updated_at,
      })
      .from(usersTable)
      .where(
        and(eq(usersTable.role, 'parent'), eq(usersTable.studio_id, studioId))
      )
      .limit(limit)
      .offset(offset)
      .orderBy(usersTable.created_at);

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    console.info(
      `${requestId} [ADMIN] - GET_PARENTS repository operation completed`
    );
    return {
      isSuccess: true,
      data: {
        parents,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNextPage,
          hasPrevPage,
        },
      },
    };
  } catch (err) {
    console.error(
      `${requestId} [ADMIN] - GET_PARENTS repository operation failed ${err}`
    );
    throw err;
  }
}
