import { Hono } from 'hono';
import { createLessonController } from './controllers/createLesson.controller';
import { getLessonsController } from './controllers/getLessons.controller';
import { getLessonByIdController } from './controllers/getLessonById.controller';
import { updateLessonController } from './controllers/updateLesson.controller';
import { deleteLessonController } from './controllers/deleteLesson.controller';
import { createEnrollmentController } from './controllers/createEnrollment.controller';
import { getEnrollmentsController } from './controllers/getEnrollments.controller';
import { updateEnrollmentController } from './controllers/updateEnrollment.controller';
import { deleteEnrollmentController } from './controllers/deleteEnrollment.controller';
import getLessonHistoryController from './controllers/getLessonHistory.controller';
import { validateCreateLesson } from './validators/createLesson.validator';
import {
  validateGetLessonsQuery,
  validateGetLessonByIdParams,
} from './validators/getLessons.validator';
import {
  validateUpdateLesson,
  validateUpdateLessonParams,
} from './validators/updateLesson.validator';
import {
  validateCreateEnrollment,
  validateUpdateEnrollment,
  validateEnrollmentParams,
  validateLessonParams,
} from './validators/enrollment.validator';
import { authValidator } from '../../middlewares/auth/auth.middleware';

const lessonRoutes = new Hono()
  // Lesson CRUD
  .post(
    '/',
    authValidator,
    validateCreateLesson('json'),
    createLessonController
  )
  .get('/', authValidator, validateGetLessonsQuery(), getLessonsController)
  .get('/history', authValidator, getLessonHistoryController)
  .get(
    '/:id',
    authValidator,
    validateGetLessonByIdParams(),
    getLessonByIdController
  )
  .put(
    '/:id',
    authValidator,
    validateUpdateLessonParams(),
    validateUpdateLesson('json'),
    updateLessonController
  )
  .delete(
    '/:id',
    authValidator,
    validateUpdateLessonParams(),
    deleteLessonController
  )

  // Enrollment management
  .post(
    '/:lessonId/enrollments',
    authValidator,
    validateLessonParams(),
    validateCreateEnrollment('json'),
    createEnrollmentController
  )
  .get(
    '/:lessonId/enrollments',
    authValidator,
    validateLessonParams(),
    getEnrollmentsController
  )
  .put(
    '/:lessonId/enrollments/:studentId',
    authValidator,
    validateEnrollmentParams(),
    validateUpdateEnrollment('json'),
    updateEnrollmentController
  )
  .delete(
    '/:lessonId/enrollments/:studentId',
    authValidator,
    validateEnrollmentParams(),
    deleteEnrollmentController
  );

export default lessonRoutes;
