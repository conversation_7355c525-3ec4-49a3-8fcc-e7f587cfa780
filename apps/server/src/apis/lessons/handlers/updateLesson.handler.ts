import type {
  UpdateLessonInput,
  UpdateLessonParams,
} from '../validators/updateLesson.validator';
import { LessonRepository } from '../repository/lesson.repository';
import type { Lessons } from 'shared';

export default async function updateLessonHandler(
  params: UpdateLessonParams,
  payload: UpdateLessonInput,
  userId: string,
  userRole: string,
  requestId: string
): Promise<Lessons.UpdateLessonResult> {
  try {
    console.log(
      `${requestId} [LESSONS] - UPDATE - Hand<PERSON> started for lesson ${params.id}`
    );

    // Check if lesson exists
    const existingLesson = await LessonRepository.getLessonById(params.id);
    if (!existingLesson) {
      return {
        data: null,
        error: {
          code: 'LESSON_NOT_FOUND',
          message: 'Lesson not found',
          statusCode: 404,
        },
      };
    }

    // Check permissions
    const canModify = await LessonRepository.canUserModifyLesson(
      params.id,
      userId,
      userRole
    );
    if (!canModify) {
      return {
        data: null,
        error: {
          code: 'ACCESS_DENIED',
          message: 'You do not have permission to update this lesson',
          statusCode: 403,
        },
      };
    }

    // Validate arena if provided
    if (payload.arena_id) {
      const arena = await LessonRepository.validateArena(payload.arena_id);
      if (!arena) {
        return {
          data: null,
          error: {
            code: 'ARENA_NOT_FOUND',
            message: 'Arena not found or inactive',
            statusCode: 404,
          },
        };
      }
    }

    // Validate curriculum items if provided
    if (payload.curriculum_items && payload.curriculum_items.length > 0) {
      const curriculumItems = await LessonRepository.validateCurriculumItems(
        payload.curriculum_items
      );
      if (curriculumItems.length !== payload.curriculum_items.length) {
        const foundIds = curriculumItems.map((c: any) => c.id);
        const missingIds = payload.curriculum_items.filter(
          (id) => !foundIds.includes(id)
        );
        return {
          data: null,
          error: {
            code: 'CURRICULUM_NOT_FOUND',
            message: `Curriculum items not found: ${missingIds.join(', ')}`,
            statusCode: 404,
          },
        };
      }
    }

    // Check for scheduling conflicts if time/arena/instructor changes
    if (payload.arena_id || payload.start_time || payload.end_time) {
      const arenaId = payload.arena_id || existingLesson.arena_id;
      const startTime = payload.start_time
        ? new Date(payload.start_time)
        : existingLesson.start_time;
      const endTime = payload.end_time
        ? new Date(payload.end_time)
        : existingLesson.end_time;

      // Get current instructor IDs from existing lesson
      const currentInstructorIds =
        (existingLesson as any).instructors?.map((i: any) => i.instructor_id) ||
        [];

      if (arenaId && currentInstructorIds.length > 0) {
        const conflictCheck = await LessonRepository.checkSchedulingConflicts(
          arenaId,
          currentInstructorIds,
          startTime,
          endTime,
          params.id // Exclude current lesson from conflict check
        );

        if (conflictCheck.hasConflicts) {
          return {
            data: null,
            error: {
              code: 'SCHEDULING_CONFLICT',
              message: 'Scheduling conflict detected',
              statusCode: 409,
              details: {
                arenaConflicts: conflictCheck.arenaConflicts,
                instructorConflicts: conflictCheck.instructorConflicts,
              },
            },
          };
        }
      }
    }

    // Prevent updating completed lessons unless admin
    if (existingLesson.status === 'completed' && userRole !== 'admin') {
      return {
        data: null,
        error: {
          code: 'LESSON_COMPLETED',
          message: 'Cannot update completed lessons',
          statusCode: 400,
        },
      };
    }

    // Update lesson
    const updatedLesson = await LessonRepository.updateLesson(
      params.id,
      payload
    );

    console.log(
      `${requestId} [LESSONS] - UPDATE - Lesson updated: ${updatedLesson?.title}`
    );

    return {
      data: { lesson: updatedLesson as any },
      error: null,
    };
  } catch (error) {
    console.error(`${requestId} [LESSONS] - UPDATE - Handler error:`, error);

    return {
      data: null,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to update lesson',
        statusCode: 500,
      },
    };
  }
}
