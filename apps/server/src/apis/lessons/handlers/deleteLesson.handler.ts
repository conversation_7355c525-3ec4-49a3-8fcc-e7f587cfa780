import type { UpdateLessonParams } from '../validators/updateLesson.validator';
import { LessonRepository } from '../repository/lesson.repository';
import type { Lessons } from 'shared';

export default async function deleteLessonHandler(
  params: UpdateLessonParams,
  userId: string,
  userRole: string,
  requestId: string
): Promise<Lessons.DeleteLessonResult> {
  try {
    console.log(
      `${requestId} [LESSONS] - DELETE - <PERSON><PERSON> started for lesson ${params.id}`
    );

    // Check if lesson exists
    const existingLesson = await LessonRepository.getLessonById(params.id);
    if (!existingLesson) {
      return {
        data: null,
        error: {
          code: 'LESSON_NOT_FOUND',
          message: 'Lesson not found',
          statusCode: 404,
        },
      };
    }

    // Check permissions
    const canModify = await LessonRepository.canUserModifyLesson(
      params.id,
      userId,
      userRole
    );
    if (!canModify) {
      return {
        data: null,
        error: {
          code: 'ACCESS_DENIED',
          message: 'You do not have permission to delete this lesson',
          statusCode: 403,
        },
      };
    }

    // Prevent deleting lessons with enrollments unless admin
    if (
      (existingLesson as any).enrollments &&
      (existingLesson as any).enrollments.length > 0 &&
      userRole !== 'admin'
    ) {
      return {
        data: null,
        error: {
          code: 'LESSON_HAS_ENROLLMENTS',
          message: 'Cannot delete lessons with student enrollments',
          statusCode: 400,
        },
      };
    }

    // Prevent deleting completed lessons unless admin
    if (existingLesson.status === 'completed' && userRole !== 'admin') {
      return {
        data: null,
        error: {
          code: 'LESSON_COMPLETED',
          message: 'Cannot delete completed lessons',
          statusCode: 400,
        },
      };
    }

    // Delete lesson (soft delete)
    await LessonRepository.deleteLesson(params.id);

    console.log(
      `${requestId} [LESSONS] - DELETE - Lesson deleted: ${existingLesson.title}`
    );

    return {
      data: { message: 'Lesson deleted successfully' },
      error: null,
    };
  } catch (error) {
    console.error(`${requestId} [LESSONS] - DELETE - Handler error:`, error);

    return {
      data: null,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to delete lesson',
        statusCode: 500,
      },
    };
  }
}
