import type { EnrollmentParams } from '../validators/enrollment.validator';
import { LessonRepository } from '../repository/lesson.repository';
import type { Lessons } from 'shared';

export default async function deleteEnrollmentHandler(
  params: EnrollmentParams,
  userId: string,
  userRole: string,
  requestId: string
): Promise<Lessons.DeleteEnrollmentResult> {
  try {
    console.log(
      `${requestId} [ENROLLMENTS] - DELETE - <PERSON><PERSON> started for lesson ${params.lessonId}, student ${params.studentId}`
    );

    // Check if enrollment exists
    const existingEnrollment = await LessonRepository.getEnrollmentById(
      params.lessonId,
      params.studentId!
    );
    if (!existingEnrollment) {
      return {
        data: null,
        error: {
          code: 'ENROLLMENT_NOT_FOUND',
          message: 'Enrollment not found',
          statusCode: 404,
        },
      };
    }

    // Check permissions
    const canManage = await LessonRepository.canUserManageEnrollments(
      params.lessonId,
      userId,
      userRole
    );
    if (!canManage) {
      return {
        data: null,
        error: {
          code: 'ACCESS_DENIED',
          message: 'You do not have permission to delete this enrollment',
          statusCode: 403,
        },
      };
    }

    // Prevent unenrolling from completed lessons unless admin
    if (
      (existingEnrollment.lesson as any)?.status === 'completed' &&
      userRole !== 'admin'
    ) {
      return {
        data: null,
        error: {
          code: 'LESSON_COMPLETED',
          message: 'Cannot unenroll from completed lessons',
          statusCode: 400,
        },
      };
    }

    // Delete enrollment
    try {
      await LessonRepository.deleteEnrollment(
        params.lessonId,
        params.studentId!
      );

      console.log(
        `${requestId} [ENROLLMENTS] - DELETE - Enrollment deleted for student ${params.studentId}`
      );

      return {
        data: { message: 'Student unenrolled successfully' },
        error: null,
      };
    } catch (error: any) {
      if (error.message === 'Enrollment not found') {
        return {
          data: null,
          error: {
            code: 'ENROLLMENT_NOT_FOUND',
            message: 'Enrollment not found',
            statusCode: 404,
          },
        };
      }

      throw error;
    }
  } catch (error) {
    console.error(
      `${requestId} [ENROLLMENTS] - DELETE - Handler error:`,
      error
    );

    return {
      data: null,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to delete enrollment',
        statusCode: 500,
      },
    };
  }
}
