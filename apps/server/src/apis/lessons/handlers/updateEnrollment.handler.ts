import type {
  UpdateEnrollmentInput,
  EnrollmentParams,
} from '../validators/enrollment.validator';
import { LessonRepository } from '../repository/lesson.repository';
import type { Lessons } from 'shared';

export default async function updateEnrollmentHandler(
  params: EnrollmentParams,
  payload: UpdateEnrollmentInput,
  userId: string,
  userRole: string,
  requestId: string
): Promise<Lessons.UpdateEnrollmentResult> {
  try {
    console.log(
      `${requestId} [ENROLLMENTS] - UPDATE - Handler started for lesson ${params.lessonId}, student ${params.studentId}`
    );

    // Check if enrollment exists
    const existingEnrollment = await LessonRepository.getEnrollmentById(
      params.lessonId,
      params.studentId!
    );
    if (!existingEnrollment) {
      return {
        data: null,
        error: {
          code: 'ENROLLMENT_NOT_FOUND',
          message: 'Enrollment not found',
          statusCode: 404,
        },
      };
    }

    // Check permissions
    const canManage = await LessonRepository.canUserManageEnrollments(
      params.lessonId,
      userId,
      userRole
    );
    if (!canManage) {
      return {
        data: null,
        error: {
          code: 'ACCESS_DENIED',
          message: 'You do not have permission to update this enrollment',
          statusCode: 403,
        },
      };
    }

    // Update enrollment
    const updatedEnrollment = await LessonRepository.updateEnrollment(
      params.lessonId,
      params.studentId!,
      payload
    );

    console.log(
      `${requestId} [ENROLLMENTS] - UPDATE - Enrollment updated for student ${params.studentId}`
    );

    return {
      data: { enrollment: updatedEnrollment as any },
      error: null,
    };
  } catch (error) {
    console.error(
      `${requestId} [ENROLLMENTS] - UPDATE - Handler error:`,
      error
    );

    return {
      data: null,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to update enrollment',
        statusCode: 500,
      },
    };
  }
}
