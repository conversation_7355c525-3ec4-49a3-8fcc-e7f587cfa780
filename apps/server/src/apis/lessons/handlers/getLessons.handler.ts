import type { GetLessonsQuery } from '../validators/getLessons.validator';
import { LessonRepository } from '../repository/lesson.repository';
import type { Lessons } from 'shared';

export default async function getLessonsHandler(
  query: GetLessonsQuery,
  userId: string,
  userRole: string,
  requestId: string
): Promise<Lessons.GetLessonsResult> {
  try {
    console.log(`${requestId} [LESSONS] - GET_LIST - Handler started`);

    // Role-based filtering
    let filters = { ...query };

    // Students can only see lessons they're enrolled in
    if (userRole === 'student') {
      filters.student_id = userId;
    }
    // Instructors can see lessons they're assigned to or all if they're admin
    else if (userRole === 'instructor') {
      // If no specific instructor filter is set, show lessons for this instructor
      if (!filters.instructor_id) {
        filters.instructor_id = userId;
      }
    }
    // <PERSON><PERSON> can see all lessons (no additional filtering)

    const result = await LessonRepository.getLessons(filters);

    console.log(
      `${requestId} [LESSONS] - GET_LIST - Found ${result.lessons.length} lessons`
    );

    return {
      data: result as any,
      error: null,
    };
  } catch (error) {
    console.error(`${requestId} [LESSONS] - GET_LIST - Handler error:`, error);

    return {
      data: null,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to retrieve lessons',
        statusCode: 500,
      },
    };
  }
}
