import type { LessonParams } from '../validators/enrollment.validator';
import { LessonRepository } from '../repository/lesson.repository';
import type { Lessons } from 'shared';

export default async function getEnrollmentsHandler(
  params: LessonParams,
  userId: string,
  userRole: string,
  requestId: string
): Promise<Lessons.GetEnrollmentsResult> {
  try {
    console.log(
      `${requestId} [ENROLLMENTS] - GET_LIST - <PERSON><PERSON> started for lesson ${params.lessonId}`
    );

    // Check if lesson exists
    const lesson = await LessonRepository.getLessonById(params.lessonId);
    if (!lesson) {
      return {
        data: null,
        error: {
          code: 'LESSON_NOT_FOUND',
          message: 'Lesson not found',
          statusCode: 404,
        },
      };
    }

    // Role-based access control
    if (userRole === 'student') {
      // Students can only see their own enrollment
      const enrollment = await LessonRepository.getEnrollmentById(
        params.lessonId,
        userId
      );
      if (!enrollment) {
        return {
          data: {
            enrollments: [],
            total: 0,
            lesson_id: params.lessonId,
          },
          error: null,
        };
      }

      return {
        data: {
          enrollments: [enrollment as any],
          total: 1,
          lesson_id: params.lessonId,
        },
        error: null,
      };
    }

    // Check permissions for instructors/admins
    const canManage = await LessonRepository.canUserManageEnrollments(
      params.lessonId,
      userId,
      userRole
    );
    if (!canManage) {
      return {
        data: null,
        error: {
          code: 'ACCESS_DENIED',
          message:
            'You do not have permission to view enrollments for this lesson',
          statusCode: 403,
        },
      };
    }

    // Get all enrollments for the lesson
    const enrollments = await LessonRepository.getLessonEnrollments(
      params.lessonId
    );

    console.log(
      `${requestId} [ENROLLMENTS] - GET_LIST - Found ${enrollments.length} enrollments`
    );

    return {
      data: {
        enrollments: enrollments as any,
        total: enrollments.length,
        lesson_id: params.lessonId,
      },
      error: null,
    };
  } catch (error) {
    console.error(
      `${requestId} [ENROLLMENTS] - GET_LIST - Handler error:`,
      error
    );

    return {
      data: null,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to retrieve enrollments',
        statusCode: 500,
      },
    };
  }
}
