import type { GetLessonHistoryQuery } from '../validators/getLessonHistory.validator';
import { LessonRepository } from '../repository/lesson.repository';
import type { Lessons } from 'shared';

export default async function getLessonHistoryHandler(
  query: GetLessonHistoryQuery,
  userId: string,
  userRole: string,
  requestId: string
): Promise<Lessons.GetLessonHistoryResult> {
  try {
    console.log(
      `${requestId} [LESSONS] - GET_HISTORY - Handler started with filters:`,
      query
    );

    // Get lesson history with comprehensive filtering
    const result = await LessonRepository.getLessonHistory(
      query,
      userId,
      userRole
    );

    console.log(
      `${requestId} [LESSONS] - GET_HISTORY - Found ${result.lessons.length} lessons`
    );

    return {
      data: {
        lessons: result.lessons,
        total: result.total,
        page: result.page,
        limit: result.limit,
        total_pages: result.total_pages,
        filters_applied: query,
      },
      error: null,
    };
  } catch (error) {
    console.error(
      `${requestId} [LESSONS] - GET_HISTORY - <PERSON><PERSON> error:`,
      error
    );

    return {
      data: null,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to get lesson history',
        statusCode: 500,
      },
    };
  }
}
