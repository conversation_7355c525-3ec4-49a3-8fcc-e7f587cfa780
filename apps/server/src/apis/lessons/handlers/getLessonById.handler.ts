import type { GetLessonByIdParams } from '../validators/getLessons.validator';
import { LessonRepository } from '../repository/lesson.repository';
import type { Lessons } from 'shared';

export default async function getLessonByIdHandler(
  params: GetLessonByIdParams,
  userId: string,
  userRole: string,
  requestId: string
): Promise<Lessons.GetLessonResult> {
  try {
    console.log(
      `${requestId} [LESSONS] - GET_BY_ID - Handler started for lesson ${params.id}`
    );

    const lesson = await LessonRepository.getLessonById(params.id);

    if (!lesson) {
      return {
        data: null,
        error: {
          code: 'LESSON_NOT_FOUND',
          message: 'Lesson not found',
          statusCode: 404,
        },
      };
    }

    // Role-based access control
    if (userRole === 'student') {
      // Students can only view lessons they're enrolled in
      const isEnrolled = (lesson as any).enrollments?.some(
        (enrollment: any) => enrollment.student_id === userId
      );
      if (!isEnrolled) {
        return {
          data: null,
          error: {
            code: 'ACCESS_DENIED',
            message: 'You do not have permission to view this lesson',
            statusCode: 403,
          },
        };
      }
    } else if (userRole === 'instructor') {
      // Instructors can view lessons they're assigned to
      const isAssigned = (lesson as any).instructors?.some(
        (instructor: any) => instructor.instructor_id === userId
      );
      if (!isAssigned) {
        return {
          data: null,
          error: {
            code: 'ACCESS_DENIED',
            message: 'You do not have permission to view this lesson',
            statusCode: 403,
          },
        };
      }
    }
    // Admins can view all lessons (no additional checks)

    console.log(
      `${requestId} [LESSONS] - GET_BY_ID - Lesson found: ${lesson.title}`
    );

    return {
      data: { lesson: lesson as any },
      error: null,
    };
  } catch (error) {
    console.error(`${requestId} [LESSONS] - GET_BY_ID - Handler error:`, error);

    return {
      data: null,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to retrieve lesson',
        statusCode: 500,
      },
    };
  }
}
