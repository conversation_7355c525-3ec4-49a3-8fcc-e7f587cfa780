import { eq, and, inArray, gte, lte, desc, asc, count, ne } from 'drizzle-orm';
import { db } from '../../../db';
import * as schema from '../../../db/schema';
import type { CreateLessonInput } from '../validators/createLesson.validator';
import type { GetLessonsQuery } from '../validators/getLessons.validator';
import type { UpdateLessonInput } from '../validators/updateLesson.validator';
import type {
  CreateEnrollmentInput,
  UpdateEnrollmentInput,
} from '../validators/enrollment.validator';
import type { GetLessonHistoryQuery } from '../validators/getLessonHistory.validator';

export class LessonRepository {
  // Create a single lesson
  static async createLesson(
    lessonData: Omit<typeof schema.lessonsTable.$inferInsert, 'id'>,
    instructorIds: string[]
  ) {
    return await db.transaction(async (tx) => {
      // Insert lesson
      const [lesson] = await tx
        .insert(schema.lessonsTable)
        .values(lessonData)
        .returning();

      // Insert instructor assignments
      if (instructorIds.length > 0) {
        const instructorAssignments = instructorIds.map((instructorId) => ({
          lesson_id: lesson?.id || 0,
          instructor_id: instructorId,
          role: 'primary' as const,
        }));

        await tx
          .insert(schema.lessonInstructorsTable)
          .values(instructorAssignments);
      }

      return lesson;
    });
  }

  // Create multiple lessons (for recurring lessons)
  static async createRecurringLessons(
    baseLessonData: Omit<typeof schema.lessonsTable.$inferInsert, 'id'>,
    instructorIds: string[],
    lessonDates: Date[]
  ) {
    return await db.transaction(async (tx) => {
      const lessons = [];
      let parentLessonId: number | null = null;

      for (let i = 0; i < lessonDates.length; i++) {
        const date = lessonDates[i];
        if (!date) continue;
        const startTime = new Date(date);
        const endTime = new Date(
          date.getTime() + baseLessonData.duration_minutes * 60 * 1000
        );

        const lessonData: typeof schema.lessonsTable.$inferInsert = {
          ...baseLessonData,
          date,
          start_time: startTime,
          end_time: endTime,
          parent_lesson_id: parentLessonId,
        };

        const [lesson]: any[] = await tx
          .insert(schema.lessonsTable)
          .values(lessonData)
          .returning();

        // Set the first lesson as the parent for all subsequent lessons
        if (i === 0) {
          parentLessonId = lesson.id;
          // Update the first lesson to reference itself as parent
          await tx
            .update(schema.lessonsTable)
            .set({ parent_lesson_id: lesson.id })
            .where(eq(schema.lessonsTable.id, lesson.id));
        }

        // Insert instructor assignments
        if (instructorIds.length > 0) {
          const instructorAssignments = instructorIds.map((instructorId) => ({
            lesson_id: lesson.id,
            instructor_id: instructorId,
            role: 'primary' as const,
          }));

          await tx
            .insert(schema.lessonInstructorsTable)
            .values(instructorAssignments);
        }

        lessons.push(lesson);
      }

      return lessons;
    });
  }

  // Get lesson by ID with related data
  static async getLessonById(id: number) {
    const lesson = await db.query.lessonsTable.findFirst({
      where: eq(schema.lessonsTable.id, id),
      with: {
        arena: true,
        instructors: {
          with: {
            instructor: true,
          },
        },
        enrollments: {
          with: {
            student: true,
          },
        },
      },
    });

    return lesson;
  }

  // Get lessons with filters and pagination
  static async getLessons(filters: GetLessonsQuery) {
    const {
      instructor_id,
      arena_id,
      status,
      lesson_type,
      date_from,
      date_to,
      student_id,
      page = 1,
      limit = 20,
    } = filters;

    // Build where conditions
    const whereConditions = [];

    if (instructor_id) {
      // Join with lesson_instructors table to filter by instructor
      whereConditions.push(
        inArray(
          schema.lessonsTable.id,
          db
            .select({ lesson_id: schema.lessonInstructorsTable.lesson_id })
            .from(schema.lessonInstructorsTable)
            .where(
              eq(schema.lessonInstructorsTable.instructor_id, instructor_id)
            )
        )
      );
    }

    if (arena_id) {
      whereConditions.push(eq(schema.lessonsTable.arena_id, arena_id));
    }

    if (status) {
      whereConditions.push(eq(schema.lessonsTable.status, status));
    }

    if (lesson_type) {
      whereConditions.push(eq(schema.lessonsTable.lesson_type, lesson_type));
    }

    if (date_from) {
      whereConditions.push(gte(schema.lessonsTable.date, new Date(date_from)));
    }

    if (date_to) {
      whereConditions.push(lte(schema.lessonsTable.date, new Date(date_to)));
    }

    if (student_id) {
      // Join with lesson_enrollments table to filter by student
      whereConditions.push(
        inArray(
          schema.lessonsTable.id,
          db
            .select({ lesson_id: schema.lessonEnrollmentsTable.lesson_id })
            .from(schema.lessonEnrollmentsTable)
            .where(eq(schema.lessonEnrollmentsTable.student_id, student_id))
        )
      );
    }

    const whereClause =
      whereConditions.length > 0 ? and(...whereConditions) : undefined;

    // Get total count
    const [{ total }]: any[] = await db
      .select({ total: count() })
      .from(schema.lessonsTable)
      .where(whereClause);

    // Get lessons with pagination
    const offset = (page - 1) * limit;
    const lessons = await db.query.lessonsTable.findMany({
      where: whereClause,
      with: {
        arena: true,
        instructors: {
          with: {
            instructor: true,
          },
        },
        enrollments: {
          with: {
            student: true,
          },
        },
      },
      orderBy: [
        desc(schema.lessonsTable.date),
        asc(schema.lessonsTable.start_time),
      ],
      limit,
      offset,
    });

    return {
      lessons,
      total: Number(total),
      page,
      limit,
      total_pages: Math.ceil(Number(total) / limit),
    };
  }

  // Check if arena exists and is active
  static async validateArena(arenaId: number) {
    const arena = await db.query.arenasTable.findFirst({
      where: and(
        eq(schema.arenasTable.id, arenaId),
        eq(schema.arenasTable.is_active, 1)
      ),
    });

    return arena;
  }

  // Check if instructors exist and have instructor role
  static async validateInstructors(instructorIds: string[]) {
    const instructors = await db.query.usersTable.findMany({
      where: inArray(schema.usersTable.id, instructorIds),
    });

    return instructors;
  }

  // Check if students exist
  static async validateStudents(studentIds: string[]) {
    const students = await db.query.studentsTable.findMany({
      where: inArray(schema.studentsTable.id, studentIds),
    });

    return students;
  }

  // Check if curriculum items exist and are active
  static async validateCurriculumItems(curriculumIds: number[]) {
    if (curriculumIds.length === 0) return [];

    const curriculumItems = await db.query.curriculumTable.findMany({
      where: and(
        inArray(schema.curriculumTable.id, curriculumIds),
        eq(schema.curriculumTable.is_active, 1)
      ),
    });

    return curriculumItems;
  }

  // Generate recurring lesson dates
  static generateRecurringDates(
    startDate: Date,
    recurrencePattern: CreateLessonInput['recurrence_pattern']
  ): Date[] {
    if (!recurrencePattern) return [];

    const dates: Date[] = [];
    const { type, interval, daysOfWeek, endDate, occurrences } =
      recurrencePattern;

    let currentDate = new Date(startDate);
    let count = 0;
    const maxDate = endDate ? new Date(endDate) : null;
    const maxOccurrences = occurrences || 52; // Default to 1 year of weekly lessons

    while (count < maxOccurrences && (!maxDate || currentDate <= maxDate)) {
      // Check if current date matches one of the specified days of week
      if (daysOfWeek.includes(currentDate.getDay())) {
        dates.push(new Date(currentDate));
        count++;
      }

      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);

      // Skip ahead based on interval for efficiency
      if (type === 'weekly' && interval > 1) {
        // Skip weeks if interval > 1
        const weeksToSkip =
          Math.floor(count / daysOfWeek.length) * (interval - 1);
        if (weeksToSkip > 0) {
          currentDate.setDate(currentDate.getDate() + weeksToSkip * 7);
        }
      } else if (type === 'biweekly') {
        // For biweekly, skip every other week
        if (count % daysOfWeek.length === 0 && count > 0) {
          currentDate.setDate(currentDate.getDate() + 7);
        }
      } else if (type === 'monthly') {
        // For monthly, move to next month after completing all days
        if (count % daysOfWeek.length === 0 && count > 0) {
          currentDate.setMonth(currentDate.getMonth() + interval);
          currentDate.setDate(1); // Reset to first day of month
        }
      }

      // Safety check to prevent infinite loops
      if (dates.length > 365) break;
    }

    return dates;
  }

  // Update lesson
  static async updateLesson(id: number, updateData: UpdateLessonInput) {
    return await db.transaction(async (tx) => {
      // Prepare update data
      const lessonUpdateData: any = {};

      if (updateData.title !== undefined)
        lessonUpdateData.title = updateData.title;
      if (updateData.arena_id !== undefined)
        lessonUpdateData.arena_id = updateData.arena_id;
      if (updateData.date !== undefined)
        lessonUpdateData.date = new Date(updateData.date);
      if (updateData.start_time !== undefined)
        lessonUpdateData.start_time = new Date(updateData.start_time);
      if (updateData.end_time !== undefined)
        lessonUpdateData.end_time = new Date(updateData.end_time);
      if (updateData.duration_minutes !== undefined)
        lessonUpdateData.duration_minutes = updateData.duration_minutes;
      if (updateData.max_students !== undefined)
        lessonUpdateData.max_students = updateData.max_students;
      if (updateData.status !== undefined)
        lessonUpdateData.status = updateData.status;
      if (updateData.notes !== undefined)
        lessonUpdateData.notes = updateData.notes;
      if (updateData.require_form !== undefined)
        lessonUpdateData.require_form = updateData.require_form ? 1 : 0;
      if (updateData.require_payment !== undefined)
        lessonUpdateData.require_payment = updateData.require_payment ? 1 : 0;
      if (updateData.price !== undefined)
        lessonUpdateData.price = updateData.price.toString();
      if (updateData.curriculum_items !== undefined)
        lessonUpdateData.curriculum_items = JSON.stringify(
          updateData.curriculum_items
        );

      // Update lesson
      const [updatedLesson] = await tx
        .update(schema.lessonsTable)
        .set(lessonUpdateData)
        .where(eq(schema.lessonsTable.id, id))
        .returning();

      return updatedLesson;
    });
  }

  // Delete lesson (soft delete by setting deleted_at)
  static async deleteLesson(id: number) {
    return await db.transaction(async (tx) => {
      // Soft delete the lesson
      const [deletedLesson] = await tx
        .update(schema.lessonsTable)
        .set({ deleted_at: new Date() })
        .where(eq(schema.lessonsTable.id, id))
        .returning();

      // Also soft delete related enrollments and instructor assignments
      await tx
        .update(schema.lessonEnrollmentsTable)
        .set({ deleted_at: new Date() })
        .where(eq(schema.lessonEnrollmentsTable.lesson_id, id));

      await tx
        .update(schema.lessonInstructorsTable)
        .set({ deleted_at: new Date() })
        .where(eq(schema.lessonInstructorsTable.lesson_id, id));

      return deletedLesson;
    });
  }

  // Get lesson history with comprehensive filtering and details
  static async getLessonHistory(
    filters: GetLessonHistoryQuery,
    userId: string,
    userRole: string
  ) {
    const {
      instructor_id,
      student_id,
      arena_id,
      date_from,
      date_to,
      status,
      lesson_type,
      page = 1,
      limit = 20,
      sort_by = 'date',
      sort_order = 'desc',
    } = filters;

    // Build where conditions
    const whereConditions = [];

    // Role-based filtering
    if (userRole === 'instructor') {
      // Instructors can only see lessons they're assigned to
      whereConditions.push(
        inArray(
          schema.lessonsTable.id,
          db
            .select({ lesson_id: schema.lessonInstructorsTable.lesson_id })
            .from(schema.lessonInstructorsTable)
            .where(eq(schema.lessonInstructorsTable.instructor_id, userId))
        )
      );
    } else if (userRole === 'parent') {
      // Parents can only see lessons their students are enrolled in
      whereConditions.push(
        inArray(
          schema.lessonsTable.id,
          db
            .select({ lesson_id: schema.lessonEnrollmentsTable.lesson_id })
            .from(schema.lessonEnrollmentsTable)
            .innerJoin(
              schema.studentsTable,
              eq(
                schema.lessonEnrollmentsTable.student_id,
                schema.studentsTable.id
              )
            )
            .where(eq(schema.studentsTable.parent_id, userId))
        )
      );
    }

    // Apply filters
    if (instructor_id) {
      whereConditions.push(
        inArray(
          schema.lessonsTable.id,
          db
            .select({ lesson_id: schema.lessonInstructorsTable.lesson_id })
            .from(schema.lessonInstructorsTable)
            .where(
              eq(schema.lessonInstructorsTable.instructor_id, instructor_id)
            )
        )
      );
    }

    if (student_id) {
      whereConditions.push(
        inArray(
          schema.lessonsTable.id,
          db
            .select({ lesson_id: schema.lessonEnrollmentsTable.lesson_id })
            .from(schema.lessonEnrollmentsTable)
            .where(eq(schema.lessonEnrollmentsTable.student_id, student_id))
        )
      );
    }

    if (arena_id) {
      whereConditions.push(eq(schema.lessonsTable.arena_id, arena_id));
    }

    if (status) {
      whereConditions.push(eq(schema.lessonsTable.status, status));
    }

    if (lesson_type) {
      whereConditions.push(eq(schema.lessonsTable.lesson_type, lesson_type));
    }

    if (date_from) {
      whereConditions.push(gte(schema.lessonsTable.date, new Date(date_from)));
    }

    if (date_to) {
      whereConditions.push(lte(schema.lessonsTable.date, new Date(date_to)));
    }

    const whereClause =
      whereConditions.length > 0 ? and(...whereConditions) : undefined;

    // Get total count
    const [{ total }]: any[] = await db
      .select({ total: count() })
      .from(schema.lessonsTable)
      .where(whereClause);

    // Determine sort order
    const sortColumn =
      sort_by === 'date'
        ? schema.lessonsTable.date
        : sort_by === 'title'
          ? schema.lessonsTable.title
          : sort_by === 'status'
            ? schema.lessonsTable.status
            : schema.lessonsTable.created_at;

    const orderBy = sort_order === 'asc' ? asc(sortColumn) : desc(sortColumn);

    // Get lessons with comprehensive details
    const offset = (page - 1) * limit;
    const lessons = await db.query.lessonsTable.findMany({
      where: whereClause,
      with: {
        arena: {
          columns: {
            id: true,
            name: true,
            location: true,
            capacity: true,
          },
        },
        instructors: {
          with: {
            instructor: {
              columns: {
                id: true,
                first_name: true,
                last_name: true,
                email: true,
                role: true,
              },
            },
          },
        },
        enrollments: {
          with: {
            student: {
              columns: {
                id: true,
                first_name: true,
                last_name: true,
                date_of_birth: true,
                gender: true,
              },
            },
          },
        },
        creator: {
          columns: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            role: true,
          },
        },
      },
      orderBy: [orderBy],
      limit,
      offset,
    });

    return {
      lessons: lessons.map((lesson) => ({
        ...lesson,
        curriculum_items: lesson.curriculum_items
          ? JSON.parse(lesson.curriculum_items)
          : [],
        recurrence_pattern: lesson.recurrence_pattern
          ? JSON.parse(lesson.recurrence_pattern)
          : null,
        attachments: lesson.attachments ? JSON.parse(lesson.attachments) : [],
        // Calculate attendance statistics
        attendance_stats: {
          total_enrolled: (lesson as any).enrollments?.length || 0,
          present:
            (lesson as any).enrollments?.filter(
              (e: any) => e.attendance_status === 'present'
            ).length || 0,
          absent:
            (lesson as any).enrollments?.filter(
              (e: any) => e.attendance_status === 'absent'
            ).length || 0,
          excused:
            (lesson as any).enrollments?.filter(
              (e: any) => e.attendance_status === 'excused'
            ).length || 0,
          pending:
            (lesson as any).enrollments?.filter(
              (e: any) => e.attendance_status === 'pending'
            ).length || 0,
        },
        // Calculate payment statistics
        payment_stats: {
          total_enrolled: (lesson as any).enrollments?.length || 0,
          paid:
            (lesson as any).enrollments?.filter(
              (e: any) => e.payment_status === 'paid'
            ).length || 0,
          unpaid:
            (lesson as any).enrollments?.filter(
              (e: any) => e.payment_status === 'unpaid'
            ).length || 0,
          partial:
            (lesson as any).enrollments?.filter(
              (e: any) => e.payment_status === 'partial'
            ).length || 0,
          refunded:
            (lesson as any).enrollments?.filter(
              (e: any) => e.payment_status === 'refunded'
            ).length || 0,
        },
      })),
      total: Number(total),
      page,
      limit,
      total_pages: Math.ceil(Number(total) / limit),
    };
  }

  // Check for scheduling conflicts with instructors and arenas
  static async checkSchedulingConflicts(
    arenaId: number,
    instructorIds: string[],
    startTime: Date,
    endTime: Date,
    excludeLessonId?: number
  ) {
    const whereConditions = [];

    // Check for time overlap
    whereConditions.push(
      and(
        lte(schema.lessonsTable.start_time, endTime),
        gte(schema.lessonsTable.end_time, startTime),
        eq(schema.lessonsTable.status, 'scheduled')
      )
    );

    // Exclude current lesson if updating
    if (excludeLessonId) {
      whereConditions.push(ne(schema.lessonsTable.id, excludeLessonId));
    }

    const whereClause = and(...whereConditions);

    // Check arena conflicts
    const arenaConflicts = await db.query.lessonsTable.findMany({
      where: and(whereClause, eq(schema.lessonsTable.arena_id, arenaId)),
      columns: { id: true, title: true, start_time: true, end_time: true },
    });

    // Check instructor conflicts
    const instructorConflicts = await db.query.lessonsTable.findMany({
      where: whereClause,
      with: {
        instructors: {
          where: inArray(
            schema.lessonInstructorsTable.instructor_id,
            instructorIds
          ),
        },
      },
      columns: { id: true, title: true, start_time: true, end_time: true },
    });

    return {
      hasConflicts: arenaConflicts.length > 0 || instructorConflicts.length > 0,
      arenaConflicts,
      instructorConflicts: instructorConflicts.filter(
        (lesson) => lesson.instructors && lesson.instructors.length > 0
      ),
    };
  }

  // Check if user can modify lesson (instructor assigned to lesson or admin)
  static async canUserModifyLesson(
    lessonId: number,
    userId: string,
    userRole: string
  ): Promise<boolean> {
    if (userRole === 'admin') return true;

    const lesson = await db.query.lessonsTable.findFirst({
      where: eq(schema.lessonsTable.id, lessonId),
      with: {
        instructors: true,
      },
    });

    if (!lesson) return false;

    // Check if user created the lesson
    if (lesson.created_by === userId) return true;

    // Check if user is assigned as instructor
    if (userRole === 'instructor') {
      return (
        (lesson as any).instructors?.some(
          (instructor: any) => instructor.instructor_id === userId
        ) || false
      );
    }

    return false;
  }

  // Enrollment management methods

  // Create enrollment
  static async createEnrollment(
    lessonId: number,
    enrollmentData: CreateEnrollmentInput
  ) {
    return await db.transaction(async (tx) => {
      // Check if lesson exists and has capacity
      const lesson = await tx.query.lessonsTable.findFirst({
        where: eq(schema.lessonsTable.id, lessonId),
        with: {
          enrollments: true,
        },
      });

      if (!lesson) {
        throw new Error('Lesson not found');
      }

      if (lesson.current_students >= lesson.max_students) {
        throw new Error('Lesson is at full capacity');
      }

      // Check if student is already enrolled
      const existingEnrollment = (lesson as any).enrollments?.find(
        (enrollment: any) => enrollment.student_id === enrollmentData.student_id
      );

      if (existingEnrollment) {
        throw new Error('Student is already enrolled in this lesson');
      }

      // Create enrollment
      const [enrollment] = await tx
        .insert(schema.lessonEnrollmentsTable)
        .values({
          lesson_id: lessonId,
          student_id: enrollmentData.student_id,
          payment_amount: enrollmentData.payment_amount?.toString() || null,
          assigned_horse: enrollmentData.assigned_horse || null,
          special_requirements: enrollmentData.special_requirements || null,
          notes: enrollmentData.notes || null,
          attendance_status: 'pending',
          payment_status: 'unpaid',
        })
        .returning();

      // Update lesson current_students count
      await tx
        .update(schema.lessonsTable)
        .set({ current_students: lesson.current_students + 1 })
        .where(eq(schema.lessonsTable.id, lessonId));

      return enrollment;
    });
  }

  // Get enrollments for a lesson
  static async getLessonEnrollments(lessonId: number) {
    const enrollments = await db.query.lessonEnrollmentsTable.findMany({
      where: eq(schema.lessonEnrollmentsTable.lesson_id, lessonId),
      // Removed student relation for now due to type mismatch
    });

    return enrollments;
  }

  // Get enrollment by ID
  static async getEnrollmentById(lessonId: number, studentId: string) {
    const enrollment = await db.query.lessonEnrollmentsTable.findFirst({
      where: and(
        eq(schema.lessonEnrollmentsTable.lesson_id, lessonId),
        eq(schema.lessonEnrollmentsTable.student_id, studentId)
      ),
      with: {
        lesson: true, // Only include lesson relation, removed student for now
      },
    });

    return enrollment;
  }

  // Update enrollment
  static async updateEnrollment(
    lessonId: number,
    studentId: string,
    updateData: UpdateEnrollmentInput
  ) {
    const enrollmentUpdateData: any = {};

    if (updateData.attendance_status !== undefined)
      enrollmentUpdateData.attendance_status = updateData.attendance_status;
    if (updateData.payment_status !== undefined)
      enrollmentUpdateData.payment_status = updateData.payment_status;
    if (updateData.payment_amount !== undefined)
      enrollmentUpdateData.payment_amount =
        updateData.payment_amount.toString();
    if (updateData.assigned_horse !== undefined)
      enrollmentUpdateData.assigned_horse = updateData.assigned_horse;
    if (updateData.special_requirements !== undefined)
      enrollmentUpdateData.special_requirements =
        updateData.special_requirements;
    if (updateData.notes !== undefined)
      enrollmentUpdateData.notes = updateData.notes;

    const [updatedEnrollment] = await db
      .update(schema.lessonEnrollmentsTable)
      .set(enrollmentUpdateData)
      .where(
        and(
          eq(schema.lessonEnrollmentsTable.lesson_id, lessonId),
          eq(schema.lessonEnrollmentsTable.student_id, studentId)
        )
      )
      .returning();

    return updatedEnrollment;
  }

  // Delete enrollment (unenroll student)
  static async deleteEnrollment(lessonId: number, studentId: string) {
    return await db.transaction(async (tx) => {
      // Get lesson to update student count
      const lesson = await tx.query.lessonsTable.findFirst({
        where: eq(schema.lessonsTable.id, lessonId),
      });

      if (!lesson) {
        throw new Error('Lesson not found');
      }

      // Soft delete enrollment
      const [deletedEnrollment] = await tx
        .update(schema.lessonEnrollmentsTable)
        .set({ deleted_at: new Date() })
        .where(
          and(
            eq(schema.lessonEnrollmentsTable.lesson_id, lessonId),
            eq(schema.lessonEnrollmentsTable.student_id, studentId)
          )
        )
        .returning();

      if (!deletedEnrollment) {
        throw new Error('Enrollment not found');
      }

      // Update lesson current_students count
      await tx
        .update(schema.lessonsTable)
        .set({ current_students: Math.max(0, lesson.current_students - 1) })
        .where(eq(schema.lessonsTable.id, lessonId));

      return deletedEnrollment;
    });
  }

  // Check if user can manage enrollments for a lesson
  static async canUserManageEnrollments(
    lessonId: number,
    userId: string,
    userRole: string
  ): Promise<boolean> {
    if (userRole === 'admin') return true;

    const lesson = await db.query.lessonsTable.findFirst({
      where: eq(schema.lessonsTable.id, lessonId),
      with: {
        instructors: true,
      },
    });

    if (!lesson) return false;

    // Check if user created the lesson
    if (lesson.created_by === userId) return true;

    // Check if user is assigned as instructor
    if (userRole === 'instructor') {
      return (
        (lesson as any).instructors?.some(
          (instructor: any) => instructor.instructor_id === userId
        ) || false
      );
    }

    return false;
  }
}
