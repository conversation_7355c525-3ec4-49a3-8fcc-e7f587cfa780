import type { Context } from 'hono';
import getLessonByIdHandler from '../handlers/getLessonById.handler';
// import type { GetLessonByIdParams } from '../validators/getLessons.validator';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const getLessonByIdController = async (c: Context) => {
  const requestId = c.get('requestId');
  console.log(`${requestId} [LESSONS] - GET_BY_ID - Request received`);

  try {
    const params = { id: parseInt(c.req.param('id') || '0') };
    const user = c.get('user');
    const userRole = user.user_metadata?.role || 'student';

    const result = await getLessonByIdHandler(
      params,
      user.id,
      userRole,
      requestId
    );

    if (result.error) {
      console.error(
        `${requestId} [LESSONS] - GET_BY_ID - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: result.error },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(
      `${requestId} [LESSONS] - GET_BY_ID - Response sent successfully`
    );

    return c.json({ data: result.data, error: result.error }, 200);
  } catch (err) {
    console.error(
      `${requestId} [LESSONS] - GET_BY_ID - Controller error: ${err}`
    );
    return c.json(
      {
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          statusCode: 500,
        },
      },
      500
    );
  }
};
