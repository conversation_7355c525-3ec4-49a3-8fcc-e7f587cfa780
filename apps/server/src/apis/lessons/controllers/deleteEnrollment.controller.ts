import type { Context } from 'hono';
import deleteEnrollmentHandler from '../handlers/deleteEnrollment.handler';
// import type { EnrollmentParams } from '../validators/enrollment.validator';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const deleteEnrollmentController = async (c: Context) => {
  const requestId = c.get('requestId');
  console.log(`${requestId} [ENROLLMENTS] - DELETE - Request received`);

  try {
    const params = {
      lessonId: parseInt(c.req.param('lessonId') || '0'),
      studentId: c.req.param('studentId') || '',
    };
    const user = c.get('user');
    const userRole = user.user_metadata?.role || 'student';

    // Students cannot delete enrollments
    if (userRole === 'student') {
      console.log(
        `${requestId} [ENROLLMENTS] - DELETE - Insufficient permissions for role: ${userRole}`
      );
      return c.json(
        {
          data: null,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'Students cannot delete enrollments',
            statusCode: 403,
          },
        },
        403
      );
    }

    const result = await deleteEnrollmentHandler(
      params,
      user.id,
      userRole,
      requestId
    );

    if (result.error) {
      console.error(
        `${requestId} [ENROLLMENTS] - DELETE - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: result.error },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(
      `${requestId} [ENROLLMENTS] - DELETE - Response sent successfully`
    );

    return c.json({ data: result.data, error: result.error }, 200);
  } catch (err) {
    console.error(
      `${requestId} [ENROLLMENTS] - DELETE - Controller error: ${err}`
    );
    return c.json(
      {
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          statusCode: 500,
        },
      },
      500
    );
  }
};
