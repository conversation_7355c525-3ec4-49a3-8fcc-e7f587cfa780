import type { Context } from 'hono';
import updateLessonHandler from '../handlers/updateLesson.handler';
import type { UpdateLessonInput } from '../validators/updateLesson.validator';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const updateLessonController = async (c: Context) => {
  const requestId = c.get('requestId');
  console.log(`${requestId} [LESSONS] - UPDATE - Request received`);

  try {
    const params = { id: parseInt(c.req.param('id') || '0') };
    const payload = (await c.req.json()) as UpdateLessonInput;
    const user = c.get('user');
    const userRole = user.user_metadata?.role || 'student';

    // Students cannot update lessons
    if (userRole === 'student') {
      console.log(
        `${requestId} [LESSONS] - UPDATE - Insufficient permissions for role: ${userRole}`
      );
      return c.json(
        {
          data: null,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'Students cannot update lessons',
            statusCode: 403,
          },
        },
        403
      );
    }

    const result = await updateLessonHandler(
      params,
      payload,
      user.id,
      userRole,
      requestId
    );

    if (result.error) {
      console.error(
        `${requestId} [LESSONS] - UPDATE - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: result.error },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(`${requestId} [LESSONS] - UPDATE - Response sent successfully`);

    return c.json({ data: result.data, error: result.error }, 200);
  } catch (err) {
    console.error(`${requestId} [LESSONS] - UPDATE - Controller error: ${err}`);
    return c.json(
      {
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          statusCode: 500,
        },
      },
      500
    );
  }
};
