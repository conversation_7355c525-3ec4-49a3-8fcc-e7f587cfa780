import type { Context } from 'hono';
import deleteLesson<PERSON><PERSON><PERSON> from '../handlers/deleteLesson.handler';
// import type { UpdateLessonParams } from '../validators/updateLesson.validator';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const deleteLessonController = async (c: Context) => {
  const requestId = c.get('requestId');
  console.log(`${requestId} [LESSONS] - DELETE - Request received`);

  try {
    const params = { id: parseInt(c.req.param('id') || '0') };
    const user = c.get('user');
    const userRole = user.user_metadata?.role || 'student';

    // Students cannot delete lessons
    if (userRole === 'student') {
      console.log(
        `${requestId} [LESSONS] - DELETE - Insufficient permissions for role: ${userRole}`
      );
      return c.json(
        {
          data: null,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'Students cannot delete lessons',
            statusCode: 403,
          },
        },
        403
      );
    }

    const result = await deleteLessonHandler(
      params,
      user.id,
      userRole,
      requestId
    );

    if (result.error) {
      console.error(
        `${requestId} [LESSONS] - DELETE - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: result.error },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(`${requestId} [LESSONS] - DELETE - Response sent successfully`);

    return c.json({ data: result.data, error: result.error }, 200);
  } catch (err) {
    console.error(`${requestId} [LESSONS] - DELETE - Controller error: ${err}`);
    return c.json(
      {
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          statusCode: 500,
        },
      },
      500
    );
  }
};
