import type { Context } from 'hono';
import updateEnrollmentHandler from '../handlers/updateEnrollment.handler';
import type { UpdateEnrollmentInput } from '../validators/enrollment.validator';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const updateEnrollmentController = async (c: Context) => {
  const requestId = c.get('requestId');
  console.log(`${requestId} [ENROLLMENTS] - UPDATE - Request received`);

  try {
    const params = {
      lessonId: parseInt(c.req.param('lessonId') || '0'),
      studentId: c.req.param('studentId') || '',
    };
    const payload = (await c.req.json()) as UpdateEnrollmentInput;
    const user = c.get('user');
    const userRole = user.user_metadata?.role || 'student';

    // Students cannot update enrollments
    if (userRole === 'student') {
      console.log(
        `${requestId} [ENROLLMENTS] - UPDATE - Insufficient permissions for role: ${userRole}`
      );
      return c.json(
        {
          data: null,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'Students cannot update enrollments',
            statusCode: 403,
          },
        },
        403
      );
    }

    const result = await updateEnrollmentHandler(
      params,
      payload,
      user.id,
      userRole,
      requestId
    );

    if (result.error) {
      console.error(
        `${requestId} [ENROLLMENTS] - UPDATE - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: result.error },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(
      `${requestId} [ENROLLMENTS] - UPDATE - Response sent successfully`
    );

    return c.json({ data: result.data, error: result.error }, 200);
  } catch (err) {
    console.error(
      `${requestId} [ENROLLMENTS] - UPDATE - Controller error: ${err}`
    );
    return c.json(
      {
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          statusCode: 500,
        },
      },
      500
    );
  }
};
