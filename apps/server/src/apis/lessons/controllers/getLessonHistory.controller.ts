import type { Context } from 'hono';
// import { authValidator } from '../../../middleware/auth.middleware';
// import getLessonHistoryHandler from '../handlers/getLessonHistory.handler';
// import { lessonHistoryQuerySchema } from '../validators/getLessonHistory.validator';
// import { CustomError } from '../../../utils/error/customError';
//
// export default async function getLessonHistoryController(c: Context) {
//   try {
//     // Validate authentication
//     const auth = await authValidator(c);
//     if (!auth.valid) {
//       return c.json(auth.response, auth.response.error?.statusCode || 401);
//     }
//
//     const { userId, userRole } = auth;
//
//     // Parse and validate query parameters
//     const queryParams = {
//       instructor_id: c.req.query('instructor_id'),
//       student_id: c.req.query('student_id'),
//       arena_id: c.req.query('arena_id'),
//       date_from: c.req.query('date_from'),
//       date_to: c.req.query('date_to'),
//       status: c.req.query('status'),
//       lesson_type: c.req.query('lesson_type'),
//       page: c.req.query('page') ? parseInt(c.req.query('page')!) : 1,
//       limit: c.req.query('limit') ? parseInt(c.req.query('limit')!) : 20,
//       sort_by: c.req.query('sort_by'),
//       sort_order: c.req.query('sort_order'),
//     };
//
//     const validatedQuery = lessonHistoryQuerySchema.parse(queryParams);
//
//     // Get request ID for logging
//     const requestId = c.get('requestId');
//
//     // Call handler
//     const result = await getLessonHistoryHandler(
//       validatedQuery,
//       userId!,
//       userRole!,
//       requestId
//     );
//
//     if (result.error) {
//       return c.json(result, result.error.statusCode);
//     }
//
//     return c.json(result, 200);
//   } catch (error: any) {
//     console.error('GET LESSON HISTORY Controller Error:', error);
//
//     if (error.name === 'ZodError') {
//       const customError = new CustomError(
//         'VALIDATION_ERROR',
//         'Invalid request parameters',
//         400
//       );
//       customError.details = error.errors;
//
//       return c.json(
//         {
//           data: null,
//           error: {
//             code: customError.code,
//             message: customError.message,
//             statusCode: customError.statusCode,
//             details: customError.details,
//           },
//         },
//         400
//       );
//     }
//
//     return c.json(
//       {
//         data: null,
//         error: {
//           code: 'INTERNAL_ERROR',
//           message: 'Failed to get lesson history',
//           statusCode: 500,
//         },
//       },
//       500
//     );
//   }
// }

export default async function getLessonHistoryController(c: Context) {
  return c.json({
    data: 'hello world',
  });
}
