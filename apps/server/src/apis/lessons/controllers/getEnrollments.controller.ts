import type { Context } from 'hono';
import getEnrollmentsHandler from '../handlers/getEnrollments.handler';
// import type { LessonParams } from '../validators/enrollment.validator';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const getEnrollmentsController = async (c: Context) => {
  const requestId = c.get('requestId');
  console.log(`${requestId} [ENROLLMENTS] - GET_LIST - Request received`);

  try {
    const params = { lessonId: parseInt(c.req.param('lessonId') || '0') };
    const user = c.get('user');
    const userRole = user.user_metadata?.role || 'student';

    const result = await getEnrollmentsHandler(
      params,
      user.id,
      userRole,
      requestId
    );

    if (result.error) {
      console.error(
        `${requestId} [ENROLLMENTS] - GET_LIST - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: result.error },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(
      `${requestId} [ENROLLMENTS] - GET_LIST - Response sent successfully`
    );

    return c.json({ data: result.data, error: result.error }, 200);
  } catch (err) {
    console.error(
      `${requestId} [ENROLLMENTS] - GET_LIST - Controller error: ${err}`
    );
    return c.json(
      {
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          statusCode: 500,
        },
      },
      500
    );
  }
};
