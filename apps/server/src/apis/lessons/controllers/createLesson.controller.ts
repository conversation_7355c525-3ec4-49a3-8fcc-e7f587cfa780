import type { Context } from 'hono';
import create<PERSON><PERSON>on<PERSON><PERSON><PERSON> from '../handlers/createLesson.handler';
import type { CreateLessonInput } from '../validators/createLesson.validator';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const createLessonController = async (c: Context) => {
  const requestId = c.get('requestId');
  console.log(`${requestId} [LESSONS] - CREATE - Request received`);

  try {
    const payload = (await c.req.json()) as CreateLessonInput;
    const user = c.get('user');

    // Check user permissions (only instructors and admins can create lessons)
    const userRole = user.user_metadata?.role || 'student';
    if (!['instructor', 'admin'].includes(userRole)) {
      console.log(
        `${requestId} [LESSONS] - CREATE - Insufficient permissions for role: ${userRole}`
      );
      return c.json(
        {
          data: null,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'Insufficient permissions to create lessons',
            statusCode: 403,
          },
        },
        403
      );
    }

    const result = await createL<PERSON>onHand<PERSON>(payload, user.id, requestId);

    if (result.error) {
      console.error(
        `${requestId} [LESSONS] - CREATE - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: result.error },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(`${requestId} [LESSONS] - CREATE - Response sent successfully`);

    // Return 201 for successful creation
    return c.json({ data: result.data, error: result.error }, 201);
  } catch (err) {
    console.error(`${requestId} [LESSONS] - CREATE - Controller error: ${err}`);
    return c.json(
      {
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          statusCode: 500,
        },
      },
      500
    );
  }
};
