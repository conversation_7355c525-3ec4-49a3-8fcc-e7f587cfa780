import type { Context } from 'hono';
import getLessonsHandler from '../handlers/getLessons.handler';
// import type { GetLessonsQuery } from '../validators/getLessons.validator';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const getLessonsController = async (c: Context) => {
  const requestId = c.get('requestId');
  console.log(`${requestId} [LESSONS] - GET_LIST - Request received`);

  try {
    const query = c.req.query() as any;
    const user = c.get('user');
    const userRole = user.user_metadata?.role || 'student';

    const result = await getLessonsHandler(query, user.id, userRole, requestId);

    if (result.error) {
      console.error(
        `${requestId} [LESSONS] - GET_LIST - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: result.error },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(
      `${requestId} [LESSONS] - GET_LIST - Response sent successfully`
    );

    return c.json({ data: result.data, error: result.error }, 200);
  } catch (err) {
    console.error(
      `${requestId} [LESSONS] - GET_LIST - Controller error: ${err}`
    );
    return c.json(
      {
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          statusCode: 500,
        },
      },
      500
    );
  }
};
