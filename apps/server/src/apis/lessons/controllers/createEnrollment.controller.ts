import type { Context } from 'hono';
import createEnrollmentHandler from '../handlers/createEnrollment.handler';
import type { CreateEnrollmentInput } from '../validators/enrollment.validator';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const createEnrollmentController = async (c: Context) => {
  const requestId = c.get('requestId');
  console.log(`${requestId} [ENROLLMENTS] - CREATE - Request received`);

  try {
    const params = { lessonId: parseInt(c.req.param('lessonId') || '0') };
    const payload = (await c.req.json()) as CreateEnrollmentInput;
    const user = c.get('user');
    const userRole = user.user_metadata?.role || 'student';

    // Students cannot enroll other students
    if (userRole === 'student') {
      console.log(
        `${requestId} [ENROLLMENTS] - CREATE - Insufficient permissions for role: ${userRole}`
      );
      return c.json(
        {
          data: null,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'Students cannot enroll other students',
            statusCode: 403,
          },
        },
        403
      );
    }

    const result = await createEnrollmentHandler(
      params,
      payload,
      user.id,
      userRole,
      requestId
    );

    if (result.error) {
      console.error(
        `${requestId} [ENROLLMENTS] - CREATE - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: result.error },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(
      `${requestId} [ENROLLMENTS] - CREATE - Response sent successfully`
    );

    return c.json({ data: result.data, error: result.error }, 201);
  } catch (err) {
    console.error(
      `${requestId} [ENROLLMENTS] - CREATE - Controller error: ${err}`
    );
    return c.json(
      {
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          statusCode: 500,
        },
      },
      500
    );
  }
};
