import { z } from 'zod';

export const lessonHistoryQuerySchema = z.object({
  instructor_id: z.string().optional(),
  student_id: z.string().optional(),
  arena_id: z.string().transform(Number).optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  status: z
    .enum(['scheduled', 'completed', 'cancelled', 'in_progress'])
    .optional(),
  lesson_type: z.enum(['single-lesson', 'recurring-lesson', 'camp']).optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  sort_by: z.enum(['date', 'title', 'status', 'created_at']).default('date'),
  sort_order: z.enum(['asc', 'desc']).default('desc'),
});

export type GetLessonHistoryQuery = z.infer<typeof lessonHistoryQuerySchema>;
