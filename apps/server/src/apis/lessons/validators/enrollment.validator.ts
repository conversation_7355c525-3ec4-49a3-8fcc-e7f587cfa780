import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';

// Create enrollment schema
const createEnrollmentSchema = z.object({
  student_id: z.string().uuid('Invalid student_id format'),
  payment_amount: z
    .number()
    .min(0, 'Payment amount cannot be negative')
    .max(9999.99, 'Payment amount cannot exceed $9999.99')
    .optional(),
  assigned_horse: z
    .string()
    .max(255, 'Horse name must be less than 255 characters')
    .optional(),
  special_requirements: z
    .string()
    .max(1000, 'Special requirements must be less than 1000 characters')
    .optional(),
  notes: z
    .string()
    .max(1000, 'Notes must be less than 1000 characters')
    .optional(),
});

// Update enrollment schema
const updateEnrollmentSchema = z.object({
  attendance_status: z
    .enum(['pending', 'present', 'absent', 'excused'])
    .optional(),
  payment_status: z.enum(['paid', 'unpaid', 'partial', 'refunded']).optional(),
  payment_amount: z
    .number()
    .min(0, 'Payment amount cannot be negative')
    .max(9999.99, 'Payment amount cannot exceed $9999.99')
    .optional(),
  assigned_horse: z
    .string()
    .max(255, 'Horse name must be less than 255 characters')
    .optional(),
  special_requirements: z
    .string()
    .max(1000, 'Special requirements must be less than 1000 characters')
    .optional(),
  notes: z
    .string()
    .max(1000, 'Notes must be less than 1000 characters')
    .optional(),
});

// Path parameters schema
const enrollmentParamsSchema = z.object({
  lessonId: z
    .string()
    .transform((val) => parseInt(val))
    .pipe(z.number().int().positive()),
  studentId: z.string().uuid('Invalid studentId format').optional(),
});

const lessonParamsSchema = z.object({
  lessonId: z
    .string()
    .transform((val) => parseInt(val))
    .pipe(z.number().int().positive()),
});

export type CreateEnrollmentInput = z.infer<typeof createEnrollmentSchema>;
export type UpdateEnrollmentInput = z.infer<typeof updateEnrollmentSchema>;
export type EnrollmentParams = z.infer<typeof enrollmentParamsSchema>;
export type LessonParams = z.infer<typeof lessonParamsSchema>;

export const validateCreateEnrollment = (target: 'json' | 'form' = 'json') =>
  zValidator(target, createEnrollmentSchema, (result, c) => {
    if (!result.success) {
      const errors = result.error.errors.map((err) => ({
        field: err.path.join('.'),
        message: err.message,
      }));

      return c.json(
        {
          data: null,
          error: {
            code: 'VALIDATION_ERROR',
            message: `Validation failed: ${errors.map((e) => `${e.field}: ${e.message}`).join(', ')}`,
            statusCode: 400,
            details: errors,
          },
        },
        400
      );
    }
  });

export const validateUpdateEnrollment = (target: 'json' | 'form' = 'json') =>
  zValidator(target, updateEnrollmentSchema, (result, c) => {
    if (!result.success) {
      const errors = result.error.errors.map((err) => ({
        field: err.path.join('.'),
        message: err.message,
      }));

      return c.json(
        {
          data: null,
          error: {
            code: 'VALIDATION_ERROR',
            message: `Validation failed: ${errors.map((e) => `${e.field}: ${e.message}`).join(', ')}`,
            statusCode: 400,
            details: errors,
          },
        },
        400
      );
    }
  });

export const validateEnrollmentParams = () =>
  zValidator('param', enrollmentParamsSchema, (result, c) => {
    if (!result.success) {
      const errors = result.error.errors.map((err) => ({
        field: err.path.join('.'),
        message: err.message,
      }));

      return c.json(
        {
          data: null,
          error: {
            code: 'VALIDATION_ERROR',
            message: `Parameter validation failed: ${errors.map((e) => `${e.field}: ${e.message}`).join(', ')}`,
            statusCode: 400,
            details: errors,
          },
        },
        400
      );
    }
  });

export const validateLessonParams = () =>
  zValidator('param', lessonParamsSchema, (result, c) => {
    if (!result.success) {
      const errors = result.error.errors.map((err) => ({
        field: err.path.join('.'),
        message: err.message,
      }));

      return c.json(
        {
          data: null,
          error: {
            code: 'VALIDATION_ERROR',
            message: `Parameter validation failed: ${errors.map((e) => `${e.field}: ${e.message}`).join(', ')}`,
            statusCode: 400,
            details: errors,
          },
        },
        400
      );
    }
  });
