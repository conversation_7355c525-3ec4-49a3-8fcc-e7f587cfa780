import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';

// Query parameters schema for listing lessons
const getLessonsQuerySchema = z
  .object({
    instructor_id: z.string().uuid('Invalid instructor_id format').optional(),
    arena_id: z
      .string()
      .transform((val) => parseInt(val))
      .pipe(z.number().int().positive())
      .optional(),
    status: z
      .enum(['scheduled', 'completed', 'cancelled', 'in_progress'])
      .optional(),
    lesson_type: z
      .enum(['single-lesson', 'recurring-lesson', 'camp'])
      .optional(),
    date_from: z
      .string()
      .refine((date) => {
        return !isNaN(Date.parse(date));
      }, 'Invalid date_from format')
      .optional(),
    date_to: z
      .string()
      .refine((date) => {
        return !isNaN(Date.parse(date));
      }, 'Invalid date_to format')
      .optional(),
    student_id: z.string().uuid('Invalid student_id format').optional(),
    page: z
      .string()
      .transform((val) => parseInt(val))
      .pipe(z.number().int().min(1))
      .optional()
      .default('1'),
    limit: z
      .string()
      .transform((val) => parseInt(val))
      .pipe(z.number().int().min(1).max(100))
      .optional()
      .default('20'),
  })
  .refine(
    (data) => {
      if (data.date_from && data.date_to) {
        const fromDate = new Date(data.date_from);
        const toDate = new Date(data.date_to);
        return fromDate <= toDate;
      }
      return true;
    },
    {
      message: 'date_from must be before or equal to date_to',
      path: ['date_to'],
    }
  );

// Path parameters schema for getting lesson by ID
const getLessonByIdParamsSchema = z.object({
  id: z
    .string()
    .transform((val) => parseInt(val))
    .pipe(z.number().int().positive()),
});

export type GetLessonsQuery = z.infer<typeof getLessonsQuerySchema>;
export type GetLessonByIdParams = z.infer<typeof getLessonByIdParamsSchema>;

export const validateGetLessonsQuery = () =>
  zValidator('query', getLessonsQuerySchema, (result, c) => {
    if (!result.success) {
      const errors = result.error.errors.map((err) => ({
        field: err.path.join('.'),
        message: err.message,
      }));

      return c.json(
        {
          data: null,
          error: {
            code: 'VALIDATION_ERROR',
            message: `Query validation failed: ${errors.map((e) => `${e.field}: ${e.message}`).join(', ')}`,
            statusCode: 400,
            details: errors,
          },
        },
        400
      );
    }
  });

export const validateGetLessonByIdParams = () =>
  zValidator('param', getLessonByIdParamsSchema, (result, c) => {
    if (!result.success) {
      const errors = result.error.errors.map((err) => ({
        field: err.path.join('.'),
        message: err.message,
      }));

      return c.json(
        {
          data: null,
          error: {
            code: 'VALIDATION_ERROR',
            message: `Parameter validation failed: ${errors.map((e) => `${e.field}: ${e.message}`).join(', ')}`,
            statusCode: 400,
            details: errors,
          },
        },
        400
      );
    }
  });
