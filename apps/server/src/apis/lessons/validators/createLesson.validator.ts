import { z<PERSON>alidator } from '@hono/zod-validator';
import { z } from 'zod';

// Recurrence pattern schema
const recurrencePatternSchema = z
  .object({
    type: z.enum(['weekly', 'biweekly', 'monthly']),
    interval: z.number().min(1).max(12),
    daysOfWeek: z.array(z.number().min(0).max(6)).min(1),
    endDate: z.string().optional(),
    occurrences: z.number().min(1).max(100).optional(),
  })
  .refine((data) => data.endDate || data.occurrences, {
    message: 'Either endDate or occurrences must be provided',
    path: ['recurrence_pattern'],
  });

// Main lesson creation schema
const createLessonSchema = z
  .object({
    title: z
      .string()
      .min(1, 'Title is required')
      .max(255, 'Title must be less than 255 characters'),
    lesson_type: z.enum(['single-lesson', 'recurring-lesson', 'camp']),
    arena_id: z.number().int().positive().optional(),
    date: z.string().refine((date) => {
      const parsedDate = new Date(date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return parsedDate >= today;
    }, 'Date cannot be in the past'),
    start_time: z.string().refine((time) => {
      return !isNaN(Date.parse(time));
    }, 'Invalid start_time format'),
    end_time: z.string().refine((time) => {
      return !isNaN(Date.parse(time));
    }, 'Invalid end_time format'),
    duration_minutes: z
      .number()
      .int()
      .min(15, 'Duration must be at least 15 minutes')
      .max(480, 'Duration cannot exceed 8 hours'),
    max_students: z
      .number()
      .int()
      .min(1, 'At least 1 student slot required')
      .max(50, 'Maximum 50 students allowed'),
    notes: z
      .string()
      .max(2000, 'Notes must be less than 2000 characters')
      .optional(),
    require_form: z.boolean().optional().default(false),
    require_payment: z.boolean().optional().default(true),
    price: z
      .number()
      .min(0, 'Price cannot be negative')
      .max(9999.99, 'Price cannot exceed $9999.99')
      .optional(),
    curriculum_items: z
      .array(z.number().int().positive())
      .optional()
      .default([]),
    instructor_ids: z
      .array(z.string().uuid('Invalid instructor_id format'))
      .min(1, 'At least one instructor is required'),
    recurrence_pattern: recurrencePatternSchema.optional(),
  })
  .refine(
    (data) => {
      const startTime = new Date(data.start_time);
      const endTime = new Date(data.end_time);
      return endTime > startTime;
    },
    {
      message: 'end_time must be after start_time',
      path: ['end_time'],
    }
  )
  .refine(
    (data) => {
      if (data.lesson_type === 'recurring-lesson' && !data.recurrence_pattern) {
        return false;
      }
      return true;
    },
    {
      message: 'recurrence_pattern is required for recurring lessons',
      path: ['recurrence_pattern'],
    }
  )
  .refine(
    (data) => {
      const startTime = new Date(data.start_time);
      const endTime = new Date(data.end_time);
      const actualDuration = Math.round(
        (endTime.getTime() - startTime.getTime()) / (1000 * 60)
      );
      return Math.abs(actualDuration - data.duration_minutes) <= 5; // Allow 5 minute tolerance
    },
    {
      message:
        'duration_minutes must match the difference between start_time and end_time',
      path: ['duration_minutes'],
    }
  );

export type CreateLessonInput = z.infer<typeof createLessonSchema>;

export const validateCreateLesson = (target: 'json' | 'form' = 'json') =>
  zValidator(target, createLessonSchema, (result, c) => {
    if (!result.success) {
      const errors = result.error.errors.map((err) => ({
        field: err.path.join('.'),
        message: err.message,
      }));

      return c.json(
        {
          data: null,
          error: {
            code: 'VALIDATION_ERROR',
            message: `Validation failed: ${errors.map((e) => `${e.field}: ${e.message}`).join(', ')}`,
            statusCode: 400,
            details: errors,
          },
        },
        400
      );
    }
  });
