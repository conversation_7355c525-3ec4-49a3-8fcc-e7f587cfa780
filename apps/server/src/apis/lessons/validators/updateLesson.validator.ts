import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';

// Update lesson schema - all fields are optional
const updateLessonSchema = z
  .object({
    title: z
      .string()
      .min(1, 'Title cannot be empty')
      .max(255, 'Title must be less than 255 characters')
      .optional(),
    arena_id: z.number().int().positive().optional(),
    date: z
      .string()
      .refine((date) => {
        const parsedDate = new Date(date);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return parsedDate >= today;
      }, 'Date cannot be in the past')
      .optional(),
    start_time: z
      .string()
      .refine((time) => {
        return !isNaN(Date.parse(time));
      }, 'Invalid start_time format')
      .optional(),
    end_time: z
      .string()
      .refine((time) => {
        return !isNaN(Date.parse(time));
      }, 'Invalid end_time format')
      .optional(),
    duration_minutes: z
      .number()
      .int()
      .min(15, 'Duration must be at least 15 minutes')
      .max(480, 'Duration cannot exceed 8 hours')
      .optional(),
    max_students: z
      .number()
      .int()
      .min(1, 'At least 1 student slot required')
      .max(50, 'Maximum 50 students allowed')
      .optional(),
    status: z
      .enum(['scheduled', 'completed', 'cancelled', 'in_progress'])
      .optional(),
    notes: z
      .string()
      .max(2000, 'Notes must be less than 2000 characters')
      .optional(),
    require_form: z.boolean().optional(),
    require_payment: z.boolean().optional(),
    price: z
      .number()
      .min(0, 'Price cannot be negative')
      .max(9999.99, 'Price cannot exceed $9999.99')
      .optional(),
    curriculum_items: z.array(z.number().int().positive()).optional(),
  })
  .refine(
    (data) => {
      // If both start_time and end_time are provided, validate they make sense
      if (data.start_time && data.end_time) {
        const startTime = new Date(data.start_time);
        const endTime = new Date(data.end_time);
        return endTime > startTime;
      }
      return true;
    },
    {
      message: 'end_time must be after start_time',
      path: ['end_time'],
    }
  )
  .refine(
    (data) => {
      // If duration_minutes is provided with start_time and end_time, validate consistency
      if (data.duration_minutes && data.start_time && data.end_time) {
        const startTime = new Date(data.start_time);
        const endTime = new Date(data.end_time);
        const actualDuration = Math.round(
          (endTime.getTime() - startTime.getTime()) / (1000 * 60)
        );
        return Math.abs(actualDuration - data.duration_minutes) <= 5; // Allow 5 minute tolerance
      }
      return true;
    },
    {
      message:
        'duration_minutes must match the difference between start_time and end_time',
      path: ['duration_minutes'],
    }
  );

// Path parameters schema for lesson ID
const updateLessonParamsSchema = z.object({
  id: z
    .string()
    .transform((val) => parseInt(val))
    .pipe(z.number().int().positive()),
});

export type UpdateLessonInput = z.infer<typeof updateLessonSchema>;
export type UpdateLessonParams = z.infer<typeof updateLessonParamsSchema>;

export const validateUpdateLesson = (target: 'json' | 'form' = 'json') =>
  zValidator(target, updateLessonSchema, (result, c) => {
    if (!result.success) {
      const errors = result.error.errors.map((err) => ({
        field: err.path.join('.'),
        message: err.message,
      }));

      return c.json(
        {
          data: null,
          error: {
            code: 'VALIDATION_ERROR',
            message: `Validation failed: ${errors.map((e) => `${e.field}: ${e.message}`).join(', ')}`,
            statusCode: 400,
            details: errors,
          },
        },
        400
      );
    }
  });

export const validateUpdateLessonParams = () =>
  zValidator('param', updateLessonParamsSchema, (result, c) => {
    if (!result.success) {
      const errors = result.error.errors.map((err) => ({
        field: err.path.join('.'),
        message: err.message,
      }));

      return c.json(
        {
          data: null,
          error: {
            code: 'VALIDATION_ERROR',
            message: `Parameter validation failed: ${errors.map((e) => `${e.field}: ${e.message}`).join(', ')}`,
            statusCode: 400,
            details: errors,
          },
        },
        400
      );
    }
  });
