import { Hono } from 'hono';
import { createHorseController } from './controllers/createHorse.controller';
import { getHorseController } from './controllers/getHorse.controller';
import { updateHorseController } from './controllers/updateHorse.controller';
import { deleteHorseController } from './controllers/deleteHorse.controller';
import { validateCreateHorse } from './validators/createHorse.validator';
import { validateUpdateHorse } from './validators/updateHorse.validator';
import { adminMiddleware } from '@server/middlewares/admin/admin.middleware';

const horseRoute = new Hono()
  .use('*', adminMiddleware(['admin', 'super-admin']))
  .get('/', getHorseController)
  .post('/', validateCreateHorse, createHorseController)
  .put('/:id', validateUpdateHorse, updateHorseController)
  .delete('/:id', deleteHorseController);

export default horseRoute;
