import { CustomError } from '@server/utils/customError';
import { db } from '@server/db';
import { horsesTable } from '@server/db/schema/horse';
import { eq, and, isNull, ne } from 'drizzle-orm';
import type { typePayload } from '../../../../../shared/src/types/horse/updateHorse.type';

export default async function updateHorseRepository(
  horseId: string,
  payload: typePayload,
  requestId: string
) {
  console.info(
    `${requestId} [HORSE] - UPDATE_HORSE repository operation started for horse: ${horseId}`
  );

  try {
    // Step 1: Check if horse exists
    await checkHorseExists(horseId, payload.studio_id, requestId);

    // Step 2: Check for duplicate names if name is being updated
    if (payload.name) {
      await checkDuplicateName(
        horseId,
        payload.name,
        payload.studio_id,
        requestId
      );
    }

    // Step 3: Update horse record
    const updatedHorse = await updateHorseRecord(horseId, payload, requestId);

    console.info(
      `${requestId} [HORSE] - UPDATE_HORSE repository operation completed`
    );
    return {
      isSuccess: true,
      data: {
        message: 'Horse updated successfully',
        horse: updatedHorse,
      },
    };
  } catch (error) {
    console.error(
      `${requestId} [HORSE] - UPDATE_HORSE repository operation failed:`,
      error
    );
    throw error;
  }
}

async function checkHorseExists(
  horseId: string,
  studioId: string,
  requestId: string
): Promise<void> {
  console.info(`${requestId} [HORSE] - Checking if horse exists: ${horseId}`);

  try {
    const horse = await db
      .select({
        id: horsesTable.id,
      })
      .from(horsesTable)
      .where(
        and(
          eq(horsesTable.id, horseId),
          eq(horsesTable.studio_id, studioId),
          isNull(horsesTable.deleted_at)
        )
      )
      .limit(1);

    if (!horse.length) {
      throw new CustomError(
        'HORSE_NOT_FOUND',
        'Horse not found or you do not have permission to update this horse',
        404
      );
    }
  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }
    console.error(`${requestId} [HORSE] - Database query failed:`, error);
    throw new CustomError(
      'DB_ERROR',
      `Failed to check horse existence: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}

async function checkDuplicateName(
  horseId: string,
  name: string,
  studioId: string,
  requestId: string
): Promise<void> {
  const nameToCheck = name.toLowerCase();

  console.info(
    `${requestId} [HORSE] - Checking duplicate name: ${nameToCheck}`
  );

  try {
    const existingHorse = await db
      .select({
        id: horsesTable.id,
      })
      .from(horsesTable)
      .where(
        and(
          eq(horsesTable.name, nameToCheck),
          eq(horsesTable.studio_id, studioId),
          ne(horsesTable.id, horseId),
          isNull(horsesTable.deleted_at)
        )
      )
      .limit(1);

    if (existingHorse.length > 0) {
      throw new CustomError(
        'DUPLICATE_HORSE',
        'Horse with same name already exists in this studio',
        409
      );
    }
  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }
    console.error(`${requestId} [HORSE] - Database query failed:`, error);
    throw new CustomError(
      'DB_ERROR',
      `Failed to check duplicate name: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}

async function updateHorseRecord(
  horseId: string,
  payload: typePayload,
  requestId: string
) {
  console.info(`${requestId} [HORSE] - Updating horse record: ${horseId}`);

  try {
    // Prepare update data with lowercase names if provided
    const updateData: any = {};

    if (payload.name) updateData.name = payload.name.toLowerCase();
    if (payload.breed) updateData.breed = payload.breed;
    if (payload.status) updateData.status = payload.status;
    if (payload.age !== undefined) updateData.age = payload.age;
    if (payload.training_level)
      updateData.training_level = payload.training_level;
    if (payload.specialties !== undefined)
      updateData.specialties = payload.specialties;
    if (payload.suitable_for !== undefined)
      updateData.suitable_for = payload.suitable_for;
    if (payload.notes_id !== undefined)
      updateData.notes_id = payload.notes_id || null;

    const updatedHorses = await db
      .update(horsesTable)
      .set(updateData)
      .where(eq(horsesTable.id, horseId))
      .returning();

    if (!updatedHorses.length) {
      throw new CustomError(
        'DB_ERROR',
        'Failed to update horse record - no data returned',
        500
      );
    }

    const updatedHorse = updatedHorses[0]!;
    console.info(
      `${requestId} [HORSE] - Horse updated successfully: ${updatedHorse.id}`
    );
    return updatedHorse;
  } catch (error) {
    console.error(`${requestId} [HORSE] - Database update failed:`, error);
    throw new CustomError(
      'DB_ERROR',
      `Failed to update horse record: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}
