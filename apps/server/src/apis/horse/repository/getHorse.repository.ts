import { CustomError } from '@server/utils/customError';
import { db } from '@server/db';
import { horsesTable } from '@server/db/schema/horse';
import { eq, and, isNull, ilike, sql } from 'drizzle-orm';
import type { typeQueryParams } from '../../../../../shared/src/types/horse/getHorse.type';

export default async function getHorseRepository(
  queryParams: typeQueryParams,
  requestId: string
) {
  console.info(`${requestId} [HORSE] - GET_HORSE repository operation started`);

  try {
    const page = parseInt(queryParams.page || '1', 10);
    const limit = parseInt(queryParams.limit || '10', 10);
    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [isNull(horsesTable.deleted_at)];

    if (queryParams.studio_id) {
      whereConditions.push(eq(horsesTable.studio_id, queryParams.studio_id));
    }

    if (queryParams.status) {
      whereConditions.push(eq(horsesTable.status, queryParams.status));
    }

    if (queryParams.training_level) {
      whereConditions.push(
        ilike(horsesTable.training_level, `%${queryParams.training_level}%`)
      );
    }

    // Get horses with pagination
    const horses = await db
      .select()
      .from(horsesTable)
      .where(and(...whereConditions))
      .orderBy(horsesTable.created_at)
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(horsesTable)
      .where(and(...whereConditions));

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    console.info(
      `${requestId} [HORSE] - GET_HORSE repository operation completed`
    );
    return {
      isSuccess: true,
      data: {
        message: 'Horses retrieved successfully',
        horses: horses,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      },
    };
  } catch (error) {
    console.error(
      `${requestId} [HORSE] - GET_HORSE repository operation failed:`,
      error
    );
    throw new CustomError(
      'DB_ERROR',
      `Failed to retrieve horses: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}
