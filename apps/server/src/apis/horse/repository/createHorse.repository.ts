import { CustomError } from '@server/utils/customError';
import { db } from '@server/db';
import { horsesTable } from '@server/db/schema/horse';
import { eq, and, isNull } from 'drizzle-orm';
import type { typePayload } from '../../../../../shared/src/types/horse/createHorse.type';

export default async function createHorseRepository(
  payload: typePayload,
  requestId: string
) {
  console.info(
    `${requestId} [HORSE] - CREATE_HORSE repository operation started`
  );

  try {
    // Step 1: Check for duplicate horse name in the same studio
    await checkDuplicateName(payload.name, payload.studio_id, requestId);

    // Step 2: Create horse record
    const newHorse = await createHorseRecord(payload, requestId);

    console.info(
      `${requestId} [HORSE] - CREATE_HORSE repository operation completed`
    );
    return {
      isSuccess: true,
      data: {
        message: 'Horse created successfully',
        horse: newHorse,
      },
    };
  } catch (error) {
    console.error(
      `${requestId} [HORSE] - CREATE_HORSE repository operation failed:`,
      error
    );
    throw error;
  }
}

async function checkDuplicateName(
  name: string,
  studioId: string,
  requestId: string
): Promise<void> {
  const nameToCheck = name.toLowerCase();

  console.info(
    `${requestId} [HORSE] - Checking duplicate name: ${nameToCheck}`
  );

  try {
    const existingHorse = await db
      .select({
        id: horsesTable.id,
      })
      .from(horsesTable)
      .where(
        and(
          eq(horsesTable.name, nameToCheck),
          eq(horsesTable.studio_id, studioId),
          isNull(horsesTable.deleted_at)
        )
      )
      .limit(1);

    if (existingHorse.length > 0) {
      throw new CustomError(
        'DUPLICATE_HORSE',
        'Horse with same name already exists in this studio',
        409
      );
    }
  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }
    console.error(`${requestId} [HORSE] - Database query failed:`, error);
    throw new CustomError(
      'DB_ERROR',
      `Failed to check duplicate name: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}

async function createHorseRecord(payload: typePayload, requestId: string) {
  console.info(`${requestId} [HORSE] - Creating horse record`);

  try {
    const newHorses = await db
      .insert(horsesTable)
      .values({
        name: payload.name.toLowerCase(),
        breed: payload.breed,
        status: payload.status,
        age: payload.age,
        training_level: payload.training_level,
        specialties: payload.specialties,
        suitable_for: payload.suitable_for,
        notes: payload.notes,
        studio_id: payload.studio_id,
      })
      .returning();

    if (!newHorses.length) {
      throw new CustomError(
        'DB_ERROR',
        'Failed to create horse record - no data returned',
        500
      );
    }

    const newHorse = newHorses[0]!;
    console.info(
      `${requestId} [HORSE] - Horse created successfully: ${newHorse.id}`
    );
    return newHorse;
  } catch (error) {
    console.error(`${requestId} [HORSE] - Database insertion failed:`, error);
    throw new CustomError(
      'DB_ERROR',
      `Failed to create horse record: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}
