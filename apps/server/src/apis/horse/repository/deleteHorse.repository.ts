import { CustomError } from '@server/utils/customError';
import { db } from '@server/db';
import { horsesTable } from '@server/db/schema/horse';
import { eq, and, isNull } from 'drizzle-orm';

export default async function deleteHorseRepository(
  horseId: string,
  studioId: string,
  requestId: string
) {
  console.info(
    `${requestId} [HORSE] - DELETE_HORSE repository operation started for horse: ${horseId}`
  );

  try {
    // Step 1: Check if horse exists and belongs to the studio
    await checkHorseExists(horseId, studioId, requestId);

    // Step 2: Soft delete the horse record
    const deletedHorse = await softDeleteHorseRecord(horseId, requestId);

    console.info(
      `${requestId} [HORSE] - DELETE_HORSE repository operation completed`
    );
    return {
      isSuccess: true,
      data: {
        message: 'Horse deleted successfully',
        horse: {
          id: deletedHorse.id,
          name: deletedHorse.name,
          breed: deletedHorse.breed,
          deleted_at: deletedHorse.deleted_at!,
        },
      },
    };
  } catch (error) {
    console.error(
      `${requestId} [HORSE] - DELETE_HORSE repository operation failed:`,
      error
    );
    throw error;
  }
}

async function checkHorseExists(
  horseId: string,
  studioId: string,
  requestId: string
): Promise<void> {
  console.info(`${requestId} [HORSE] - Checking if horse exists: ${horseId}`);

  try {
    const horse = await db
      .select({
        id: horsesTable.id,
      })
      .from(horsesTable)
      .where(
        and(
          eq(horsesTable.id, horseId),
          eq(horsesTable.studio_id, studioId),
          isNull(horsesTable.deleted_at)
        )
      )
      .limit(1);

    if (!horse.length) {
      throw new CustomError(
        'HORSE_NOT_FOUND',
        'Horse not found or you do not have permission to delete this horse',
        404
      );
    }
  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }
    console.error(`${requestId} [HORSE] - Database query failed:`, error);
    throw new CustomError(
      'DB_ERROR',
      `Failed to check horse existence: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}

async function softDeleteHorseRecord(horseId: string, requestId: string) {
  console.info(`${requestId} [HORSE] - Soft deleting horse record: ${horseId}`);

  try {
    const deletedHorses = await db
      .update(horsesTable)
      .set({
        deleted_at: new Date(),
      })
      .where(eq(horsesTable.id, horseId))
      .returning({
        id: horsesTable.id,
        name: horsesTable.name,
        breed: horsesTable.breed,
        deleted_at: horsesTable.deleted_at,
      });

    if (!deletedHorses.length) {
      throw new CustomError(
        'DB_ERROR',
        'Failed to delete horse record - no data returned',
        500
      );
    }

    const deletedHorse = deletedHorses[0]!;
    console.info(
      `${requestId} [HORSE] - Horse soft deleted successfully: ${deletedHorse.id}`
    );
    return deletedHorse;
  } catch (error) {
    console.error(`${requestId} [HORSE] - Database delete failed:`, error);
    throw new CustomError(
      'DB_ERROR',
      `Failed to delete horse record: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}
