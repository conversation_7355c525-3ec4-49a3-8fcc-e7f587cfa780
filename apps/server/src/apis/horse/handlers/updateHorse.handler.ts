import type { CustomError } from '@server/utils/customError';
import type {
  typePayload,
  typeResult,
} from '../../../../../shared/src/types/horse/updateHorse.type';
import updateHorseRepository from '../repository/updateHorse.repository';

export default async function updateHorseHandler(
  horseId: string,
  payload: typePayload,
  requestId: string
): Promise<typeResult> {
  let data: typeResult['data'] = null;
  let error: typeResult['error'] = null;

  try {
    console.info(
      `${requestId} [HORSE] - UPDATE_HORSE handler started for horse: ${horseId}`
    );

    const { isSuccess, data: horseData } = await updateHorseRepository(
      horseId,
      payload,
      requestId
    );

    if (isSuccess) {
      data = horseData;
    }

    console.info(
      `${requestId} [HORSE] - UPDATE_HORSE handler completed successfully`
    );
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [HORSE] - UPDATE_HORSE handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
