import type { CustomError } from '@server/utils/customError';
import type {
  typeQueryParams,
  typeResult,
} from '../../../../../shared/src/types/horse/getHorse.type';
import getHorseRepository from '../repository/getHorse.repository';

export default async function getHorseHandler(
  queryParams: typeQueryParams,
  requestId: string
): Promise<typeResult> {
  let data: typeResult['data'] = null;
  let error: typeResult['error'] = null;

  try {
    console.info(`${requestId} [HORSE] - GET_HORSE handler started`);

    const { isSuccess, data: horseData } = await getHorseRepository(
      queryParams,
      requestId
    );

    if (isSuccess) {
      data = horseData;
    }

    console.info(
      `${requestId} [HORSE] - GET_HORSE handler completed successfully`
    );
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [HORSE] - GET_HORSE handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
