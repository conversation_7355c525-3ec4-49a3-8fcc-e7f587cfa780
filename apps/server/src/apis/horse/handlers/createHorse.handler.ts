import type { CustomError } from '@server/utils/customError';
import type {
  typePayload,
  typeResult,
} from '../../../../../shared/src/types/horse/createHorse.type';
import createHorseRepository from '../repository/createHorse.repository';

export default async function createHorseHandler(
  payload: typePayload,
  requestId: string
): Promise<typeResult> {
  let data: typeResult['data'] = null;
  let error: typeResult['error'] = null;

  try {
    console.info(`${requestId} [HORSE] - CREATE_HORSE handler started`);

    const { isSuccess, data: horseData } = await createHorseRepository(
      payload,
      requestId
    );

    if (isSuccess) {
      data = horseData;
    }

    console.info(
      `${requestId} [HORSE] - CREATE_HORSE handler completed successfully`
    );
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [HORSE] - CREATE_HORSE handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
