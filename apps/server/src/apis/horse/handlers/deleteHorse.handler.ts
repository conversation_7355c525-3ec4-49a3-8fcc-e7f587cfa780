import type { CustomError } from '@server/utils/customError';
import type { typeResult } from '../../../../../shared/src/types/horse/deleteHorse.type';
import deleteHorseRepository from '../repository/deleteHorse.repository';

export default async function deleteHorseHandler(
  horseId: string,
  studioId: string,
  requestId: string
): Promise<typeResult> {
  let data: typeResult['data'] = null;
  let error: typeResult['error'] = null;

  try {
    console.info(
      `${requestId} [HORSE] - DELETE_HORSE handler started for horse: ${horseId}, studio: ${studioId}`
    );

    const { isSuccess, data: horseData } = await deleteHorseRepository(
      horseId,
      studioId,
      requestId
    );

    if (isSuccess) {
      data = horseData;
    }

    console.info(
      `${requestId} [HORSE] - DELETE_HORSE handler completed successfully`
    );
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [HORSE] - DELETE_HORSE handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
