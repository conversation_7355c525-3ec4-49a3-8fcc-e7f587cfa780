import type { Context } from 'hono';
import getHorseHandler from '../handlers/getHorse.handler';
import type { typeQueryParams } from '../../../../../shared/src/types/horse/getHorse.type';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const getHorseController = async (c: Context) => {
  const requestId = c.get('requestId');
  const user = c.get('user');

  console.log(`${requestId} [HORSE] - GET_HORSE - Request received`);

  try {
    const studioId = user?.user_metadata?.studio_id;

    if (!studioId) {
      console.error(`${requestId} [HORSE] - GET_HORSE - Missing studio ID`);
      return c.json(
        { data: null, error: { message: 'Studio ID not found' } },
        400
      );
    }

    // Get query parameters
    const queryParams: typeQueryParams = {
      page: c.req.query('page'),
      limit: c.req.query('limit'),
      studio_id: studioId, // Always filter by user's studio
      status: c.req.query('status') as
        | 'available'
        | 'resting'
        | 'injured'
        | undefined,
      training_level: c.req.query('training_level'),
    };

    const result = await getHorseHandler(queryParams, requestId);

    if (result.error) {
      console.error(
        `${requestId} [HORSE] - GET_HORSE - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: { message: result.error.message } },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(
      `${requestId} [HORSE] - GET_HORSE - Response sent successfully`
    );
    return c.json({ data: result.data, error: result.error }, 200);
  } catch (err) {
    console.error(
      `${requestId} [HORSE] - GET_HORSE - Controller error: ${err}`
    );
    return c.json(
      { data: null, error: { message: 'Internal server error' } },
      500
    );
  }
};
