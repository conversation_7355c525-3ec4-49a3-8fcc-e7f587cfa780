import type { Context } from 'hono';
import updateHorseHandler from '../handlers/updateHorse.handler';
import type { typePayloadInput } from '../../../../../shared/src/types/horse/updateHorse.type';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const updateHorseController = async (c: Context) => {
  const requestId = c.get('requestId');
  const user = c.get('user');

  console.log(`${requestId} [HORSE] - UPDATE_HORSE - Request received`);

  try {
    // Get studio_id from authenticated user metadata
    const studioId = user?.user_metadata?.studio_id;

    if (!studioId) {
      console.error(`${requestId} [HORSE] - UPDATE_HORSE - Missing studio ID`);
      return c.json(
        { data: null, error: { message: 'Studio ID not found' } },
        400
      );
    }

    // Get horse ID from URL parameters
    const horseId = c.req.param('id');

    if (!horseId) {
      console.error(`${requestId} [HORSE] - UPDATE_HORSE - Missing horse ID`);
      return c.json(
        { data: null, error: { message: 'Horse ID is required' } },
        400
      );
    }

    const payload = (await c.req.json()) as typePayloadInput;

    // Validate that at least one field is provided for update
    if (Object.keys(payload).length === 0) {
      console.error(
        `${requestId} [HORSE] - UPDATE_HORSE - No fields provided for update`
      );
      return c.json(
        {
          data: null,
          error: { message: 'At least one field must be provided for update' },
        },
        400
      );
    }

    // Add studio_id to payload
    const payloadWithStudio = {
      ...payload,
      studio_id: studioId,
    };

    const result = await updateHorseHandler(
      horseId,
      payloadWithStudio,
      requestId
    );

    if (result.error) {
      console.error(
        `${requestId} [HORSE] - UPDATE_HORSE - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: { message: result.error.message } },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(
      `${requestId} [HORSE] - UPDATE_HORSE - Response sent successfully`
    );
    return c.json({ data: result.data, error: result.error }, 200);
  } catch (err) {
    console.error(
      `${requestId} [HORSE] - UPDATE_HORSE - Controller error: ${err}`
    );
    return c.json(
      { data: null, error: { message: 'Internal server error' } },
      500
    );
  }
};
