import type { Context } from 'hono';
import createHorseHandler from '../handlers/createHorse.handler';
import type { typePayloadInput } from '../../../../../shared/src/types/horse/createHorse.type';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const createHorseController = async (c: Context) => {
  const requestId = c.get('requestId');
  const user = c.get('user');

  console.log(`${requestId} [HORSE] - CREATE_HORSE - Request received`);

  try {
    // Get studio_id from authenticated user metadata
    const studioId = user?.user_metadata?.studio_id;

    if (!studioId) {
      console.error(`${requestId} [HORSE] - CREATE_HORSE - Missing studio ID`);
      return c.json(
        { data: null, error: { message: 'Studio ID not found' } },
        400
      );
    }

    const payload = (await c.req.json()) as typePayloadInput;

    // Add studio_id to payload and ensure status has a default value
    const payloadWithStudio = {
      ...payload,
      studio_id: studioId,
      status: payload.status || ('available' as const),
    };

    const result = await createHorseHandler(payloadWithStudio, requestId);

    if (result.error) {
      console.error(
        `${requestId} [HORSE] - CREATE_HORSE - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: { message: result.error.message } },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(
      `${requestId} [HORSE] - CREATE_HORSE - Response sent successfully`
    );
    return c.json({ data: result.data, error: result.error }, 201);
  } catch (err) {
    console.error(
      `${requestId} [HORSE] - CREATE_HORSE - Controller error: ${err}`
    );
    return c.json(
      { data: null, error: { message: 'Internal server error' } },
      500
    );
  }
};
