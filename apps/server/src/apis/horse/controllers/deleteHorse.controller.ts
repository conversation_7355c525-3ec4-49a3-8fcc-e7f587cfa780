import type { Context } from 'hono';
import deleteHorseHandler from '../handlers/deleteHorse.handler';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const deleteHorseController = async (c: Context) => {
  const requestId = c.get('requestId');
  const user = c.get('user');

  console.log(`${requestId} [HORSE] - DELETE_HORSE - Request received`);

  try {
    // Get studio_id from authenticated user metadata
    const studioId = user?.user_metadata?.studio_id;

    if (!studioId) {
      console.error(`${requestId} [HORSE] - DELETE_HORSE - Missing studio ID`);
      return c.json(
        { data: null, error: { message: 'Studio ID not found' } },
        400
      );
    }

    // Get horse ID from URL parameters
    const horseId = c.req.param('id');

    if (!horseId) {
      console.error(`${requestId} [HORSE] - DELETE_HORSE - Missing horse ID`);
      return c.json(
        { data: null, error: { message: 'Horse ID is required' } },
        400
      );
    }

    const result = await deleteHorseHandler(horseId, studioId, requestId);

    if (result.error) {
      console.error(
        `${requestId} [HORSE] - DELETE_HORSE - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: { message: result.error.message } },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(
      `${requestId} [HORSE] - DELETE_HORSE - Response sent successfully`
    );
    return c.json({ data: result.data, error: result.error }, 200);
  } catch (err) {
    console.error(
      `${requestId} [HORSE] - DELETE_HORSE - Controller error: ${err}`
    );
    return c.json(
      { data: null, error: { message: 'Internal server error' } },
      500
    );
  }
};
