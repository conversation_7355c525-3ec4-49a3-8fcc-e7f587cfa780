import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';

const updateHorseSchema = z
  .object({
    name: z
      .string()
      .min(1, 'Name is required')
      .max(100, 'Name must be less than 100 characters')
      .optional(),
    breed: z
      .string()
      .min(1, 'Breed is required')
      .max(100, 'Breed must be less than 100 characters')
      .optional(),
    status: z.enum(['available', 'resting', 'injured']).optional(),
    age: z
      .number()
      .int()
      .min(1, 'Age must be at least 1')
      .max(50, 'Age must be less than 50')
      .optional(),
    training_level: z
      .string()
      .min(1, 'Training level is required')
      .max(50, 'Training level must be less than 50 characters')
      .optional(),
    specialties: z.array(z.string()).optional(),
    suitable_for: z.array(z.string()).optional(),
    notes_id: z.string().uuid('Notes ID must be a valid UUID').optional(),
  })
  .refine((data) => Object.keys(data).length > 0, {
    message: 'At least one field must be provided for update',
  });

export const validateUpdateHorse = zValidator('json', updateHorseSchema);
