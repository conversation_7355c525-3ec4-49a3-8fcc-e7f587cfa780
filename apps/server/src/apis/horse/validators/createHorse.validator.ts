import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';

const createHorseSchema = z.object({
  name: z
    .string()
    .min(1, 'Name is required')
    .max(100, 'Name must be less than 100 characters'),
  breed: z
    .string()
    .min(1, 'Breed is required')
    .max(100, 'Breed must be less than 100 characters'),
  status: z
    .enum(['available', 'resting', 'injured'])
    .optional()
    .default('available'),
  age: z
    .number()
    .int()
    .min(1, 'Age must be at least 1')
    .max(50, 'Age must be less than 50'),
  training_level: z
    .string()
    .min(1, 'Training level is required')
    .max(50, 'Training level must be less than 50 characters'),
  specialties: z.array(z.string()).optional(),
  suitable_for: z.array(z.string()).optional(),
  notes: z.string().optional(),
  // studio_id is automatically added by the controller
});

export const validateCreateHorse = zValidator('json', createHorseSchema);
