import { Hono } from 'hono';
import { createStudentController } from './controllers/createStudent.controller';
import { getStudentController } from './controllers/getStudent.controller';
import { updateStudentController } from './controllers/updateStudent.controller';
import { deleteStudentController } from './controllers/deleteStudent.controller';
import { validateCreateStudent } from './validators/createStudent.validator';
import { validateUpdateStudent } from './validators/updateStudent.validator';
import { authValidator } from '@server/middlewares/auth/auth.middleware';

const studentRoute = new Hono()
  .use('*', authValidator)
  .get('/', getStudentController)
  .post('/', validateCreateStudent('json'), createStudentController)
  .put('/:id', validateUpdateStudent('json'), updateStudentController)
  .delete('/:id', deleteStudentController);

export default studentRoute;
