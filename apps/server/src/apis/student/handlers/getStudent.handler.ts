import type { CustomError } from '@server/utils/customError';
import type { typeResult } from '../../../../../shared/src/types/student/getStudent.type';
import getStudentRepository from '../repository/getStudent.repository';

export default async function getStudentHandler(
  userId: string,
  userRole: string,
  userStudioId: string,
  page: number,
  limit: number,
  requestId: string
): Promise<typeResult> {
  let data: typeResult['data'] = null;
  let error: typeResult['error'] = null;

  try {
    console.info(
      `${requestId} [STUDENT] - GET_STUDENT handler started for user: ${userId}, role: ${userRole}, studio: ${userStudioId}, page: ${page}, limit: ${limit}`
    );

    const { isSuccess, data: studentData } = await getStudentRepository(
      userId,
      userRole,
      userStudioId,
      page,
      limit,
      requestId
    );

    if (isSuccess) {
      data = studentData;
    }

    console.info(
      `${requestId} [STUDENT] - GET_STUDENT handler completed successfully`
    );
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [STUDENT] - GET_STUDENT handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
