import type { CustomError } from '@server/utils/customError';
import type {
  typePayloadInput,
  typeResult,
} from '../../../../../shared/src/types/student/updateStudent.type';
import updateStudentRepository from '../repository/updateStudent.repository';

export default async function updateStudentHandler(
  studentId: string,
  payload: typePayloadInput,
  parentId: string,
  requestId: string
): Promise<typeResult> {
  let data: typeResult['data'] = null;
  let error: typeResult['error'] = null;

  try {
    console.info(
      `${requestId} [STUDENT] - UPDATE_STUDENT handler started for student: ${studentId}, parent: ${parentId}`
    );

    // Add parent_id to payload
    const studentPayload = {
      ...payload,
      parent_id: parentId,
    };

    const { isSuccess, data: studentData } = await updateStudentRepository(
      studentId,
      studentPayload,
      requestId
    );

    if (isSuccess) {
      data = studentData;
    }

    console.info(
      `${requestId} [STUDENT] - UPDATE_STUDENT handler completed successfully`
    );
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [STUDENT] - UPDATE_STUDENT handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
