import type { CustomError } from '@server/utils/customError';
import type { typeResult } from '../../../../../shared/src/types/student/deleteStudent.type';
import deleteStudentRepository from '../repository/deleteStudent.repository';

export default async function deleteStudentHandler(
  studentId: string,
  parentId: string,
  requestId: string
): Promise<typeResult> {
  let data: typeResult['data'] = null;
  let error: typeResult['error'] = null;

  try {
    console.info(
      `${requestId} [STUDENT] - DELETE_STUDENT handler started for student: ${studentId}, parent: ${parentId}`
    );

    const { isSuccess, data: studentData } = await deleteStudentRepository(
      studentId,
      parentId,
      requestId
    );

    if (isSuccess) {
      data = studentData;
    }

    console.info(
      `${requestId} [STUDENT] - DELETE_STUDENT handler completed successfully`
    );
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [STUDENT] - DELETE_STUDENT handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
