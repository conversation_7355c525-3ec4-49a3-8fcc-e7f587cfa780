import type { CustomError } from '@server/utils/customError';
import type {
  typePayloadInput,
  typeResult,
} from '../../../../../shared/src/types/student/createStudent.type';
import createStudentRepository from '../repository/createStudent.repository';

export default async function createStudentHandler(
  payload: typePayloadInput,
  parentId: string,
  requestId: string
): Promise<typeResult> {
  let data: typeResult['data'] = null;
  let error: typeResult['error'] = null;

  try {
    console.info(
      `${requestId} [STUDENT] - CREATE_STUDENT handler started for parent: ${parentId}`
    );

    // Add parent_id to payload
    const studentPayload = {
      ...payload,
      parent_id: parentId,
    };

    const { isSuccess, data: studentData } = await createStudentRepository(
      studentPayload,
      requestId
    );

    if (isSuccess) {
      data = studentData;
    }

    console.info(
      `${requestId} [STUDENT] - CREATE_STUDENT handler completed successfully`
    );
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [STUDENT] - CREATE_STUDENT handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
