import type { Context } from 'hono';
import getStudentHandler from '../handlers/getStudent.handler';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const getStudentController = async (c: Context) => {
  const requestId = c.get('requestId');
  const user = c.get('user');

  console.log(`${requestId} [STUDENT] - GET_STUDENT - Request received`);

  try {
    // Get user information
    const userId = user?.id;
    const userRole = user?.user_metadata?.role;
    console.log('THIS IS USER ROLE', userRole);
    const userStudioId = user?.user_metadata?.studio_id;

    if (!userId) {
      console.error(`${requestId} [STUDENT] - GET_STUDENT - Missing user ID`);
      return c.json(
        { data: null, error: { message: 'User ID not found' } },
        400
      );
    }

    if (!userStudioId) {
      console.error(`${requestId} [STUDENT] - GET_STUDENT - Missing studio ID`);
      return c.json(
        { data: null, error: { message: 'Studio ID not found' } },
        400
      );
    }

    // Get pagination parameters from query
    const pageParam = c.req.query('page');
    const limitParam = c.req.query('limit');

    // Parse and validate pagination parameters
    const page = pageParam ? parseInt(pageParam, 10) : 1;
    const limit = limitParam ? parseInt(limitParam, 10) : 10;

    // Validate pagination parameters
    if (isNaN(page) || page < 1) {
      console.error(
        `${requestId} [STUDENT] - GET_STUDENT - Invalid page parameter`
      );
      return c.json(
        { data: null, error: { message: 'Page must be a positive integer' } },
        400
      );
    }

    if (isNaN(limit) || limit < 1 || limit > 100) {
      console.error(
        `${requestId} [STUDENT] - GET_STUDENT - Invalid limit parameter`
      );
      return c.json(
        { data: null, error: { message: 'Limit must be between 1 and 100' } },
        400
      );
    }

    const result = await getStudentHandler(
      userId,
      userRole,
      userStudioId,
      page,
      limit,
      requestId
    );

    if (result.error) {
      console.error(
        `${requestId} [STUDENT] - GET_STUDENT - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: { message: result.error.message } },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(
      `${requestId} [STUDENT] - GET_STUDENT - Response sent successfully`
    );
    return c.json({ data: result.data, error: result.error }, 200);
  } catch (err) {
    console.error(
      `${requestId} [STUDENT] - GET_STUDENT - Controller error: ${err}`
    );
    return c.json(
      { data: null, error: { message: 'Internal server error' } },
      500
    );
  }
};
