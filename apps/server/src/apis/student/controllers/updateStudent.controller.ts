import type { Context } from 'hono';
import updateStudentHandler from '../handlers/updateStudent.handler';
import type { typePayloadInput } from '../../../../../shared/src/types/student/updateStudent.type';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const updateStudentController = async (c: Context) => {
  const requestId = c.get('requestId');
  const user = c.get('user');

  console.log(`${requestId} [STUDENT] - UPDATE_STUDENT - Request received`);

  try {
    // Get parent_id from authenticated user
    const parentId = user?.id;

    if (!parentId) {
      console.error(
        `${requestId} [STUDENT] - UPDATE_STUDENT - Missing user ID`
      );
      return c.json(
        { data: null, error: { message: 'User ID not found' } },
        400
      );
    }

    // Get student ID from URL parameters
    const studentId = c.req.param('id');

    if (!studentId) {
      console.error(
        `${requestId} [STUDENT] - UPDATE_STUDENT - Missing student ID`
      );
      return c.json(
        { data: null, error: { message: 'Student ID is required' } },
        400
      );
    }

    const payload = (await c.req.json()) as typePayloadInput;

    // Validate that at least one field is provided for update
    if (Object.keys(payload).length === 0) {
      console.error(
        `${requestId} [STUDENT] - UPDATE_STUDENT - No fields provided for update`
      );
      return c.json(
        {
          data: null,
          error: { message: 'At least one field must be provided for update' },
        },
        400
      );
    }

    const result = await updateStudentHandler(
      studentId,
      payload,
      parentId,
      requestId
    );

    if (result.error) {
      console.error(
        `${requestId} [STUDENT] - UPDATE_STUDENT - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: { message: result.error.message } },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(
      `${requestId} [STUDENT] - UPDATE_STUDENT - Response sent successfully`
    );
    return c.json({ data: result.data, error: result.error }, 200);
  } catch (err) {
    console.error(
      `${requestId} [STUDENT] - UPDATE_STUDENT - Controller error: ${err}`
    );
    return c.json(
      { data: null, error: { message: 'Internal server error' } },
      500
    );
  }
};
