import type { Context } from 'hono';
import deleteStudentHand<PERSON> from '../handlers/deleteStudent.handler';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const deleteStudentController = async (c: Context) => {
  const requestId = c.get('requestId');
  const user = c.get('user');

  console.log(`${requestId} [STUDENT] - DELETE_STUDENT - Request received`);

  try {
    // Get parent_id from authenticated user
    const parentId = user?.id;

    if (!parentId) {
      console.error(
        `${requestId} [STUDENT] - DELETE_STUDENT - Missing user ID`
      );
      return c.json(
        { data: null, error: { message: 'User ID not found' } },
        400
      );
    }

    // Get student ID from URL parameters
    const studentId = c.req.param('id');

    if (!studentId) {
      console.error(
        `${requestId} [STUDENT] - DELETE_STUDENT - Missing student ID`
      );
      return c.json(
        { data: null, error: { message: 'Student ID is required' } },
        400
      );
    }

    const result = await deleteStudentHandler(studentId, parentId, requestId);

    if (result.error) {
      console.error(
        `${requestId} [STUDENT] - DELETE_STUDENT - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: { message: result.error.message } },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(
      `${requestId} [STUDENT] - DELETE_STUDENT - Response sent successfully`
    );
    return c.json({ data: result.data, error: result.error }, 200);
  } catch (err) {
    console.error(
      `${requestId} [STUDENT] - DELETE_STUDENT - Controller error: ${err}`
    );
    return c.json(
      { data: null, error: { message: 'Internal server error' } },
      500
    );
  }
};
