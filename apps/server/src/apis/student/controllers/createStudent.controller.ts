import type { Context } from 'hono';
import createStudentHandler from '../handlers/createStudent.handler';
import type { typePayloadInput } from '../../../../../shared/src/types/student/createStudent.type';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const createStudentController = async (c: Context) => {
  const requestId = c.get('requestId');
  const user = c.get('user');

  console.log(`${requestId} [STUDENT] - CREATE_STUDENT - Request received`);

  try {
    const payload = (await c.req.json()) as typePayloadInput;
    const {
      first_name,
      last_name,
      riding_level,
      date_of_birth,
      gender,
      skill_id,
      profile_image,
      email,
      phone,
      country_code,
      previous_experience,
      riding_goals,
      medical_conditions,
      allergies,
    } = payload;

    const parentId = payload.parent_id ?? user?.id;

    if (!parentId) {
      console.error(
        `${requestId} [STUDENT] - CREATE_STUDENT - Missing user ID`
      );
      return c.json(
        { data: null, error: { message: 'User ID not found' } },
        400
      );
    }

    const result = await createStudentHandler(
      {
        first_name,
        last_name,
        riding_level,
        date_of_birth,
        gender,
        skill_id,
        profile_image,
        email,
        phone,
        country_code,
        previous_experience,
        riding_goals,
        medical_conditions,
        allergies,
      },
      parentId,
      requestId
    );

    if (result.error) {
      console.error(
        `${requestId} [STUDENT] - CREATE_STUDENT - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: { message: result.error.message } },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(
      `${requestId} [STUDENT] - CREATE_STUDENT - Response sent successfully`
    );
    return c.json({ data: result.data, error: result.error }, 201);
  } catch (err) {
    console.error(
      `${requestId} [STUDENT] - CREATE_STUDENT - Controller error: ${err}`
    );
    return c.json(
      { data: null, error: { message: 'Internal server error' } },
      500
    );
  }
};
