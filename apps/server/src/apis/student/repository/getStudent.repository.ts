import { CustomError } from '@server/utils/customError';
import { db } from '@server/db';
import { studentsTable } from '@server/db/schema/student';
import { usersTable } from '@server/db/schema/user';
import { eq, count, and, isNull, inArray } from 'drizzle-orm';

export default async function getStudentRepository(
  userId: string,
  userRole: string,
  userStudioId: string,
  page: number = 1,
  limit: number = 10,
  requestId: string
) {
  console.info(
    `${requestId} [STUDENT] - GET_STUDENT repository operation started for user: ${userId}, role: ${userRole}, studio: ${userStudioId}`
  );

  try {
    const offset = (page - 1) * limit;

    // Determine the filtering logic based on user role
    let whereCondition;

    if (
      userRole === 'admin' ||
      userRole === 'super-admin' ||
      userRole === 'owner'
    ) {
      // For admin roles, get all students whose parents belong to the same studio
      // First, get all parent IDs from the same studio
      const parentsInStudio = await db
        .select({ id: usersTable.id })
        .from(usersTable)
        .where(
          and(
            eq(usersTable.studio_id, userStudioId),
            eq(usersTable.role, 'parent'),
            isNull(usersTable.deleted_at)
          )
        );

      const parentIds = parentsInStudio.map((parent) => parent.id);

      if (parentIds.length === 0) {
        // No parents in this studio, return empty result
        return {
          isSuccess: true,
          data: {
            students: [],
            pagination: {
              page,
              limit,
              total: 0,
              totalPages: 0,
            },
          },
        };
      }

      whereCondition = and(
        inArray(studentsTable.parent_id, parentIds),
        isNull(studentsTable.deleted_at)
      );
    } else {
      // For parent role, get only their own students
      whereCondition = and(
        eq(studentsTable.parent_id, userId),
        isNull(studentsTable.deleted_at)
      );
    }

    // Get total count for pagination
    const [totalResult] = await db
      .select({ count: count() })
      .from(studentsTable)
      .where(whereCondition);

    const total = totalResult?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get students with pagination
    const students = await db
      .select({
        id: studentsTable.id,
        first_name: studentsTable.first_name,
        last_name: studentsTable.last_name,
        riding_level: studentsTable.riding_level,
        date_of_birth: studentsTable.date_of_birth,
        gender: studentsTable.gender,
        skill_id: studentsTable.skill_id,
        parent_id: studentsTable.parent_id,
        profile_image: studentsTable.profile_image,
        email: studentsTable.email,
        phone: studentsTable.phone,
        country_code: studentsTable.country_code,
        previous_experience: studentsTable.previous_experience,
        riding_goals: studentsTable.riding_goals,
        medical_conditions: studentsTable.medical_conditions,
        allergies: studentsTable.allergies,
        created_at: studentsTable.created_at,
        updated_at: studentsTable.updated_at,
      })
      .from(studentsTable)
      .where(whereCondition)
      .limit(limit)
      .offset(offset)
      .orderBy(studentsTable.created_at);
    console.info(
      `${requestId} [STUDENT] - GET_STUDENT repository operation completed. Found ${students.length} students`
    );

    return {
      isSuccess: true,
      data: {
        students,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      },
    };
  } catch (error) {
    console.error(
      `${requestId} [STUDENT] - GET_STUDENT repository operation failed:`,
      error
    );
    throw new CustomError(
      'DB_ERROR',
      `Failed to fetch students: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}
