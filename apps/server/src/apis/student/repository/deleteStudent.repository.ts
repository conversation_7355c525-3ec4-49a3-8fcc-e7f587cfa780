import { CustomError } from '@server/utils/customError';
import { db } from '@server/db';
import { studentsTable } from '@server/db/schema/student';
import { eq, and, isNull } from 'drizzle-orm';

export default async function deleteStudentRepository(
  studentId: string,
  parentId: string,
  requestId: string
) {
  console.info(
    `${requestId} [STUDENT] - DELETE_STUDENT repository operation started for student: ${studentId}`
  );

  try {
    // Step 1: Check if student exists and belongs to the parent
    await checkStudentExists(studentId, parentId, requestId);

    // Step 2: Soft delete the student record
    const deletedStudent = await softDeleteStudentRecord(studentId, requestId);

    console.info(
      `${requestId} [STUDENT] - DELETE_STUDENT repository operation completed`
    );
    return {
      isSuccess: true,
      data: {
        message: 'Student deleted successfully',
        student: {
          id: deletedStudent.id,
          first_name: deletedStudent.first_name,
          last_name: deletedStudent.last_name,
          deleted_at: deletedStudent.deleted_at!,
        },
      },
    };
  } catch (error) {
    console.error(
      `${requestId} [STUDENT] - DELETE_STUDENT repository operation failed:`,
      error
    );
    throw error;
  }
}

async function checkStudentExists(
  studentId: string,
  parentId: string,
  requestId: string
): Promise<void> {
  console.info(
    `${requestId} [STUDENT] - Checking if student exists: ${studentId}`
  );

  try {
    const student = await db
      .select({
        id: studentsTable.id,
      })
      .from(studentsTable)
      .where(
        and(
          eq(studentsTable.id, studentId),
          eq(studentsTable.parent_id, parentId),
          isNull(studentsTable.deleted_at)
        )
      )
      .limit(1);

    if (!student.length) {
      throw new CustomError(
        'STUDENT_NOT_FOUND',
        'Student not found or you do not have permission to delete this student',
        404
      );
    }
  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }
    console.error(`${requestId} [STUDENT] - Database query failed:`, error);
    throw new CustomError(
      'DB_ERROR',
      `Failed to check student existence: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}

async function softDeleteStudentRecord(studentId: string, requestId: string) {
  console.info(
    `${requestId} [STUDENT] - Soft deleting student record: ${studentId}`
  );

  try {
    const deletedStudents = await db
      .update(studentsTable)
      .set({
        deleted_at: new Date(),
      })
      .where(eq(studentsTable.id, studentId))
      .returning({
        id: studentsTable.id,
        first_name: studentsTable.first_name,
        last_name: studentsTable.last_name,
        deleted_at: studentsTable.deleted_at,
      });

    if (!deletedStudents.length) {
      throw new CustomError(
        'DB_ERROR',
        'Failed to delete student record - no data returned',
        500
      );
    }

    const deletedStudent = deletedStudents[0]!;
    console.info(
      `${requestId} [STUDENT] - Student soft deleted successfully: ${deletedStudent.id}`
    );
    return deletedStudent;
  } catch (error) {
    console.error(`${requestId} [STUDENT] - Database delete failed:`, error);
    throw new CustomError(
      'DB_ERROR',
      `Failed to delete student record: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}
