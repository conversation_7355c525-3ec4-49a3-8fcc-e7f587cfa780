import { CustomError } from '@server/utils/customError';
import { db } from '@server/db';
import { studentsTable } from '@server/db/schema/student';
import { eq, and, isNull, ne } from 'drizzle-orm';
import type { typePayload } from '../../../../../shared/src/types/student/updateStudent.type';

export default async function updateStudentRepository(
  studentId: string,
  payload: typePayload,
  requestId: string
) {
  console.info(
    `${requestId} [STUDENT] - UPDATE_STUDENT repository operation started for student: ${studentId}`
  );

  try {
    // Step 1: Check if student exists and belongs to the parent
    await checkStudentExists(studentId, payload.parent_id, requestId);

    // Step 2: Check for duplicate names if name is being updated
    if (payload.first_name || payload.last_name) {
      await checkDuplicateName(studentId, payload, requestId);
    }

    // Step 3: Update student record
    const updatedStudent = await updateStudentRecord(
      studentId,
      payload,
      requestId
    );

    console.info(
      `${requestId} [STUDENT] - UPDATE_STUDENT repository operation completed`
    );
    return {
      isSuccess: true,
      data: {
        message: 'Student updated successfully',
        student: updatedStudent,
      },
    };
  } catch (error) {
    console.error(
      `${requestId} [STUDENT] - UPDATE_STUDENT repository operation failed:`,
      error
    );
    throw error;
  }
}

async function checkStudentExists(
  studentId: string,
  parentId: string,
  requestId: string
): Promise<void> {
  console.info(
    `${requestId} [STUDENT] - Checking if student exists: ${studentId}`
  );

  try {
    const student = await db
      .select({
        id: studentsTable.id,
      })
      .from(studentsTable)
      .where(
        and(
          eq(studentsTable.id, studentId),
          eq(studentsTable.parent_id, parentId),
          isNull(studentsTable.deleted_at)
        )
      )
      .limit(1);

    if (!student.length) {
      throw new CustomError(
        'STUDENT_NOT_FOUND',
        'Student not found or you do not have permission to update this student',
        404
      );
    }
  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }
    console.error(`${requestId} [STUDENT] - Database query failed:`, error);
    throw new CustomError(
      'DB_ERROR',
      `Failed to check student existence: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}

async function checkDuplicateName(
  studentId: string,
  payload: typePayload,
  requestId: string
): Promise<void> {
  if (payload.first_name || payload.last_name) {
    // Get current student data to fill in missing name parts
    const currentStudent = await db
      .select({
        first_name: studentsTable.first_name,
        last_name: studentsTable.last_name,
      })
      .from(studentsTable)
      .where(eq(studentsTable.id, studentId))
      .limit(1);

    if (!currentStudent.length) return;

    const firstNameToCheck = (
      payload.first_name || currentStudent[0]!.first_name
    ).toLowerCase();
    const lastNameToCheck = (
      payload.last_name || currentStudent[0]!.last_name
    ).toLowerCase();

    console.info(
      `${requestId} [STUDENT] - Checking duplicate name: ${firstNameToCheck} ${lastNameToCheck}`
    );

    try {
      const existingStudent = await db
        .select({
          id: studentsTable.id,
        })
        .from(studentsTable)
        .where(
          and(
            eq(studentsTable.first_name, firstNameToCheck),
            eq(studentsTable.last_name, lastNameToCheck),
            eq(studentsTable.parent_id, payload.parent_id),
            ne(studentsTable.id, studentId),
            isNull(studentsTable.deleted_at)
          )
        )
        .limit(1);

      if (existingStudent.length > 0) {
        throw new CustomError(
          'DUPLICATE_STUDENT',
          'Student with same name already exists for this parent',
          409
        );
      }
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      console.error(`${requestId} [STUDENT] - Database query failed:`, error);
      throw new CustomError(
        'DB_ERROR',
        `Failed to check duplicate name: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  }
}

async function updateStudentRecord(
  studentId: string,
  payload: typePayload,
  requestId: string
) {
  console.info(
    `${requestId} [STUDENT] - Updating student record: ${studentId}`
  );

  try {
    // Prepare update data with lowercase names if provided
    const updateData: any = {};

    if (payload.first_name)
      updateData.first_name = payload.first_name.toLowerCase();
    if (payload.last_name)
      updateData.last_name = payload.last_name.toLowerCase();
    if (payload.date_of_birth) updateData.date_of_birth = payload.date_of_birth;
    if (payload.gender) updateData.gender = payload.gender;
    if (payload.skill_id !== undefined)
      updateData.skill_id = payload.skill_id || null;
    if (payload.profile_image !== undefined)
      updateData.profile_image = payload.profile_image || null;
    if (payload.email !== undefined) updateData.email = payload.email || null;
    if (payload.phone !== undefined) updateData.phone = payload.phone || null;
    if (payload.country_code !== undefined)
      updateData.country_code = payload.country_code || null;

    const updatedStudents = await db
      .update(studentsTable)
      .set(updateData)
      .where(eq(studentsTable.id, studentId))
      .returning();

    if (!updatedStudents.length) {
      throw new CustomError(
        'DB_ERROR',
        'Failed to update student record - no data returned',
        500
      );
    }

    const updatedStudent = updatedStudents[0]!;
    console.info(
      `${requestId} [STUDENT] - Student updated successfully: ${updatedStudent.id}`
    );
    return updatedStudent;
  } catch (error) {
    console.error(`${requestId} [STUDENT] - Database update failed:`, error);
    throw new CustomError(
      'DB_ERROR',
      `Failed to update student record: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}
