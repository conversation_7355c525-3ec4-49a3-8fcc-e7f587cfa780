import { CustomError } from '@server/utils/customError';
import { db } from '@server/db';
import { studentsTable } from '@server/db/schema/student';
import { eq, and } from 'drizzle-orm';
import type { typePayload } from '../../../../../shared/src/types/student/createStudent.type';

export default async function createStudentRepository(
  payload: typePayload,
  requestId: string
) {
  console.info(
    `${requestId} [STUDENT] - CREATE_STUDENT repository operation started`
  );

  try {
    // Step 1: Check if student with same email already exists for this parent
    await checkExistingStudent(
      payload.first_name,
      payload.last_name,
      payload.parent_id,
      requestId
    );

    // Step 2: Create student record in database
    const createdStudent = await createStudentRecord(payload, requestId);
    console.info(
      `${requestId} [STUDENT] - CREATE_STUDENT repository operation completed`
    );
    return {
      isSuccess: true,
      data: {
        message: 'Student created successfully',
        student: createdStudent,
      },
    };
  } catch (error) {
    console.error(
      `${requestId} [STUDENT] - CREATE_STUDENT repository operation failed:`,
      error
    );

    throw error;
  }
}

async function checkExistingStudent(
  first_name: string | undefined,
  last_name: string | undefined,
  parentId: string,
  requestId: string
): Promise<void> {
  if (first_name && last_name) {
    // Convert names to lowercase for case-insensitive comparison
    const firstNameLower = first_name.toLowerCase();
    const lastNameLower = last_name.toLowerCase();

    console.info(
      `${requestId} [STUDENT] - Checking existing student with name: ${firstNameLower} ${lastNameLower}`
    );

    try {
      const existingStudent = await db
        .select({
          id: studentsTable.id,
        })
        .from(studentsTable)
        .where(
          and(
            eq(studentsTable.first_name, firstNameLower),
            eq(studentsTable.last_name, lastNameLower),
            eq(studentsTable.parent_id, parentId)
          )
        )
        .limit(1);

      if (existingStudent.length > 0) {
        throw new CustomError(
          'DUPLICATE_STUDENT',
          'Student with same name already exists for this parent',
          409
        );
      }
    } catch (error) {
      if (error instanceof CustomError) {
        throw error; // Re-throw custom errors
      }
      console.error(`${requestId} [STUDENT] - Database query failed:`, error);
      throw new CustomError(
        'DB_ERROR',
        `Failed to check existing student: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  }
}

async function createStudentRecord(payload: typePayload, requestId: string) {
  const {
    first_name,
    last_name,
    riding_level,
    date_of_birth,
    gender,
    skill_id,
    parent_id,
    profile_image,
    email,
    phone,
    country_code,
    previous_experience,
    riding_goals,
    medical_conditions,
    allergies,
  } = payload;

  try {
    console.info(`${requestId} [STUDENT] - Creating student record`);

    const createdStudents = await db
      .insert(studentsTable)
      .values({
        first_name: first_name.toLowerCase(),
        last_name: last_name.toLowerCase(),
        riding_level,
        date_of_birth,
        gender,
        skill_id: skill_id || null,
        parent_id,
        profile_image: profile_image || null,
        email: email || null,
        phone: phone || null,
        country_code: country_code || null,
        previous_experience: previous_experience || null,
        riding_goals: riding_goals || null,
        medical_conditions: medical_conditions || null,
        allergies: allergies || null,
      })
      .returning();

    if (!createdStudents.length) {
      throw new CustomError(
        'DB_ERROR',
        'Failed to create student record - no data returned',
        500
      );
    }

    const createdStudent = createdStudents[0]!;
    console.info(
      `${requestId} [STUDENT] - Student created with ID: ${createdStudent.id}`
    );
    return createdStudent;
  } catch (error) {
    console.error(
      `${requestId} [STUDENT] - Database transaction failed:`,
      error
    );
    throw new CustomError(
      'DB_ERROR',
      `Failed to create student record: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}
