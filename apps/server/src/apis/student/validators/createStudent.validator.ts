import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';

export type CreateStudentInput = z.infer<typeof createStudentSchema>;

export const createStudentSchema = z.object({
  first_name: z.string().min(1).max(30),
  last_name: z.string().min(1).max(30),
  riding_level: z.enum(['beginner', 'intermediate', 'advanced']),
  date_of_birth: z.coerce.date(),
  gender: z.enum(['male', 'female', 'other']),
  skill_id: z.string().uuid().optional(),
  profile_image: z.string().optional(),
  email: z.string().email().max(100).optional(),
  phone: z.string().min(7).max(15).optional(),
  country_code: z
    .string()
    .regex(/^\+\d{1,4}$/, 'Invalid country code')
    .optional(),
  previous_experience: z.string().optional(),
  riding_goals: z.string().optional(),
  medical_conditions: z.string().optional(),
  allergies: z.string().optional(),
});

export const validateCreateStudent = (
  target: 'json' | 'form' | 'query' | 'param' | 'header' | 'cookie' = 'json'
) => zValidator(target, createStudentSchema);
