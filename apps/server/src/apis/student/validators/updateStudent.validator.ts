import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';

export type UpdateStudentInput = z.infer<typeof updateStudentSchema>;

export const updateStudentSchema = z.object({
  first_name: z.string().min(1).max(30).optional(),
  last_name: z.string().min(1).max(30).optional(),
  date_of_birth: z.coerce.date().optional(),
  gender: z.enum(['male', 'female', 'other']).optional(),
  skill_id: z.string().uuid().optional(),
  profile_image: z.string().url().optional(),
  email: z.string().email().max(100).optional(),
  phone: z.string().min(7).max(15).optional(),
  country_code: z
    .string()
    .regex(/^\+\d{1,4}$/, 'Invalid country code')
    .optional(),
  riding_level: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
  previous_experience: z.string().optional(),
  riding_goals: z.string().optional(),
  medical_conditions: z.string().optional(),
  allergies: z.string().optional(),
});

export const validateUpdateStudent = (
  target: 'json' | 'form' | 'query' | 'param' | 'header' | 'cookie' = 'json'
) => zValidator(target, updateStudentSchema);
