import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { InvoiceType, PaymentMethod } from '../types/invoice.types';

const lineItemSchema = z.object({
  name: z.string().min(1).max(255),
  amount: z.number().positive(), // in dollars
  type: z.string().min(1),
  quantity: z.number().int().positive(),
  total: z.number().positive(), // in dollars
});

export const createInvoiceSchema = z.object({
  userId: z.string().uuid(),
  lineItems: z.array(lineItemSchema).min(1),
  entityId: z.string().uuid().optional(),
  entityType: z.string().optional(),
  dueDate: z.string().datetime().optional(),
  type: z.nativeEnum(InvoiceType).default(InvoiceType.ONE_TIME),
  paymentMethod: z.nativeEnum(PaymentMethod).default(PaymentMethod.CARD),
});

export type CreateInvoiceInput = z.infer<typeof createInvoiceSchema>;

export const validateCreateInvoice = (
  target: 'json' | 'form' | 'query' | 'param' | 'header' | 'cookie' = 'json'
) => zValidator(target, createInvoiceSchema);
