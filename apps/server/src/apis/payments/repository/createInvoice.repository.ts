import { CustomError } from '@server/utils/customError';
import { db } from '@server/db';
import {
  invoicesTable,
  stripeCustomersTable,
  usersTable,
  type InsertInvoice,
} from '@server/db/schema';
import { eq, and } from 'drizzle-orm';
import { stripe } from '../../stripe/repository/stripe.client';
import type { CreateInvoiceInput } from '../validators/createInvoice.validator';
import { InvoiceStatus, PaymentProvider } from '../types/invoice.types';

interface CreateInvoiceRepositoryResult {
  isSuccess: boolean;
  invoiceId?: string;
  paymentIntentId?: string;
  clientSecret?: string;
}

export default async function createInvoiceRepository(
  payload: CreateInvoiceInput,
  studioId: string,
  requestId: string
): Promise<CreateInvoiceRepositoryResult> {
  try {
    console.info(
      `${requestId} [PAYMENTS] - CREATE_INVOICE repository operation started`
    );

    // Validate user exists and belongs to studio
    await validateUser(payload.userId, studioId, requestId);

    // Step 2: Get Stripe customer for the user
    const stripeCustomer = await getStripeCustomer(payload.userId, requestId);

    if (!stripeCustomer) {
      console.error(
        `${requestId} [PAYMENTS] - No Stripe customer found for user: ${payload.userId}`
      );
      return {
        isSuccess: false,
      };
    }

    // Step 3: Create invoice record
    const totalAmount = payload.lineItems.reduce(
      (sum, item) => sum + item.total,
      0
    );

    const invoiceId = await createInvoiceRecord(
      payload,
      studioId,
      totalAmount,
      requestId
    );

    // Step 4: Create Stripe payment intent
    const { paymentIntentId, clientSecret } = await createStripePaymentIntent(
      stripeCustomer.stripe_customer_id,
      totalAmount,
      invoiceId,
      studioId,
      requestId
    );

    // Step 6: Update invoice with payment intent ID
    await updateInvoiceWithPaymentIntent(invoiceId, paymentIntentId, requestId);

    console.info(
      `${requestId} [PAYMENTS] - CREATE_INVOICE repository operation completed`
    );

    return {
      isSuccess: true,
      invoiceId,
      paymentIntentId,
      clientSecret,
    };
  } catch (error) {
    console.error(
      `${requestId} [PAYMENTS] - CREATE_INVOICE repository operation failed:`,
      error
    );
    throw error;
  }
}

async function validateUser(
  userId: string,
  studioId: string,
  requestId: string
) {
  console.info(`${requestId} [PAYMENTS] - Validating user: ${userId}`);

  const user = await db
    .select()
    .from(usersTable)
    .where(and(eq(usersTable.id, userId), eq(usersTable.studio_id, studioId)))
    .limit(1);

  if (user.length === 0) {
    throw new CustomError(
      'USER_NOT_FOUND',
      'User not found or does not belong to this studio',
      404
    );
  }

  return user[0];
}

async function getStripeCustomer(userId: string, requestId: string) {
  console.info(
    `${requestId} [PAYMENTS] - Getting Stripe customer for user: ${userId}`
  );

  const stripeCustomer = await db
    .select()
    .from(stripeCustomersTable)
    .where(eq(stripeCustomersTable.user_id, userId))
    .limit(1);

  if (stripeCustomer.length === 0) {
    throw new CustomError(
      'STRIPE_CUSTOMER_NOT_FOUND',
      'Stripe customer not found for this user',
      404
    );
  }

  return stripeCustomer[0];
}

async function createInvoiceRecord(
  payload: CreateInvoiceInput,
  studioId: string,
  totalAmount: number,
  requestId: string
): Promise<string> {
  console.info(`${requestId} [PAYMENTS] - Creating invoice record`);

  const dueDate = payload.dueDate ? new Date(payload.dueDate) : null;

  const [invoice] = await db
    .insert(invoicesTable)
    .values({
      studio_id: studioId,
      user_id: payload.userId,
      entity_id: payload.entityId || null,
      entity_type: payload.entityType || null,
      status: InvoiceStatus.PENDING,
      payment_provider: PaymentProvider.STRIPE,
      payment_method: payload.paymentMethod,
      type: payload.type,
      line_items: payload.lineItems,
      payments: [],
      base_amount: totalAmount.toString(), // Convert to string for varchar field
      final_amount: totalAmount.toString(), // Convert to string for varchar field
      due_date: dueDate,
      metadata: {
        attemptCount: 0,
      },
    } as InsertInvoice)
    .returning({ id: invoicesTable.id });

  if (!invoice) {
    throw new Error('Failed to create invoice record');
  }

  console.info(
    `${requestId} [PAYMENTS] - Invoice created with ID: ${invoice.id}`
  );
  return invoice.id;
}

async function createStripePaymentIntent(
  stripeCustomerId: string,
  amount: number,
  invoiceId: string,
  studioId: string,
  requestId: string
): Promise<{ paymentIntentId: string; clientSecret: string }> {
  console.info(`${requestId} [PAYMENTS] - Creating Stripe payment intent`);

  try {
    // Get the customer's payment methods
    const paymentMethods = await stripe.paymentMethods.list({
      customer: stripeCustomerId,
      type: 'card',
    });

    console.info(
      `${requestId} [PAYMENTS] - Found ${paymentMethods.data.length} payment methods for customer`
    );

    // Use the first available payment method, or let Stripe handle it automatically
    const paymentMethodId =
      paymentMethods.data.length > 0 && paymentMethods.data[0]?.id
        ? paymentMethods.data[0].id
        : undefined;

    const paymentIntentData: any = {
      amount: Math.round(amount * 100), // Convert dollars to cents for Stripe
      currency: 'usd',
      customer: stripeCustomerId,
      metadata: {
        invoice_id: invoiceId,
        studio_id: studioId,
        request_id: requestId,
      },
      off_session: true,
      confirm: true,
      automatic_payment_methods: {
        enabled: true,
      },
    };

    // Add payment method if available
    if (paymentMethodId) {
      paymentIntentData.payment_method = paymentMethodId;
      console.info(
        `${requestId} [PAYMENTS] - Using payment method: ${paymentMethodId}`
      );
    } else {
      console.info(
        `${requestId} [PAYMENTS] - No payment method found, using automatic payment methods`
      );
    }

    const paymentIntent = await stripe.paymentIntents.create(paymentIntentData);

    console.info(
      `${requestId} [PAYMENTS] - Payment intent created: ${paymentIntent.id}`
    );

    return {
      paymentIntentId: paymentIntent.id,
      clientSecret: paymentIntent.client_secret!,
    };
  } catch (error) {
    console.error(
      `${requestId} [PAYMENTS] - Failed to create payment intent:`,
      error
    );

    if (error instanceof Error) {
      throw new CustomError(
        'STRIPE_ERROR',
        `Failed to create payment intent: ${error.message}`,
        500
      );
    }

    throw new CustomError(
      'STRIPE_ERROR',
      'Failed to create payment intent: Unknown error',
      500
    );
  }
}

async function updateInvoiceWithPaymentIntent(
  invoiceId: string,
  paymentIntentId: string,
  requestId: string
): Promise<void> {
  console.info(
    `${requestId} [PAYMENTS] - Updating invoice with payment intent ID`
  );

  await db
    .update(invoicesTable)
    .set({
      transaction_id: paymentIntentId,
      metadata: {
        paymentIntentId,
        attemptCount: 0,
      },
    })
    .where(eq(invoicesTable.id, invoiceId));

  console.info(`${requestId} [PAYMENTS] - Invoice updated successfully`);
}
