import { db } from '@server/db';
import { invoicesTable, usersTable } from '@server/db/schema';
import { count, eq, and, desc } from 'drizzle-orm';

interface GetInvoicesRepositoryResult {
  isSuccess: boolean;
  data?: {
    invoices: any[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
    };
  };
}

export default async function getInvoicesRepository(
  page: number = 1,
  limit: number = 10,
  status: string | undefined,
  studioId: string,
  requestId: string
): Promise<GetInvoicesRepositoryResult> {
  try {
    console.info(
      `${requestId} [PAYMENTS] - GET_INVOICES repository operation started`
    );

    // Calculate offset
    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [eq(invoicesTable.studio_id, studioId)];
    if (status) {
      whereConditions.push(eq(invoicesTable.status, status as any));
    }

    // Get total count
    const totalResult = await db
      .select({ count: count() })
      .from(invoicesTable)
      .where(and(...whereConditions));
    const total = totalResult[0]?.count ?? 0;

    // Get invoices with user data
    const invoices = await db
      .select({
        id: invoicesTable.id,
        studio_id: invoicesTable.studio_id,
        user_id: invoicesTable.user_id,
        entity_id: invoicesTable.entity_id,
        entity_type: invoicesTable.entity_type,
        status: invoicesTable.status,
        payment_provider: invoicesTable.payment_provider,
        payment_method: invoicesTable.payment_method,
        type: invoicesTable.type,
        transaction_id: invoicesTable.transaction_id,
        line_items: invoicesTable.line_items,
        payments: invoicesTable.payments,
        base_amount: invoicesTable.base_amount,
        final_amount: invoicesTable.final_amount,
        payment_date: invoicesTable.payment_date,
        due_date: invoicesTable.due_date,
        start_date: invoicesTable.start_date,
        end_date: invoicesTable.end_date,
        metadata: invoicesTable.metadata,
        created_at: invoicesTable.created_at,
        updated_at: invoicesTable.updated_at,
        deleted_at: invoicesTable.deleted_at,
        user_first_name: usersTable.first_name,
        user_last_name: usersTable.last_name,
        user_email: usersTable.email,
      })
      .from(invoicesTable)
      .leftJoin(usersTable, eq(invoicesTable.user_id, usersTable.id))
      .where(and(...whereConditions))
      .limit(limit)
      .offset(offset)
      .orderBy(desc(invoicesTable.created_at));

    // Transform the data
    const transformedInvoices = invoices.map((invoice) => ({
      id: invoice.id,
      studio_id: invoice.studio_id,
      user_id: invoice.user_id,
      entity_id: invoice.entity_id,
      entity_type: invoice.entity_type,
      status: invoice.status,
      payment_provider: invoice.payment_provider,
      payment_method: invoice.payment_method,
      type: invoice.type,
      transaction_id: invoice.transaction_id,
      line_items: invoice.line_items,
      payments: invoice.payments,
      base_amount: invoice.base_amount,
      final_amount: invoice.final_amount,
      payment_date: invoice.payment_date,
      due_date: invoice.due_date,
      start_date: invoice.start_date,
      end_date: invoice.end_date,
      metadata: invoice.metadata,
      created_at: invoice.created_at,
      updated_at: invoice.updated_at,
      deleted_at: invoice.deleted_at,
      user: invoice.user_first_name
        ? {
            first_name: invoice.user_first_name,
            last_name: invoice.user_last_name,
            email: invoice.user_email,
          }
        : undefined,
    }));

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    console.info(
      `${requestId} [PAYMENTS] - GET_INVOICES repository operation completed`
    );

    return {
      isSuccess: true,
      data: {
        invoices: transformedInvoices,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNextPage,
          hasPrevPage,
        },
      },
    };
  } catch (err) {
    console.error(
      `${requestId} [PAYMENTS] - GET_INVOICES repository operation failed:`,
      err
    );
    throw err;
  }
}
