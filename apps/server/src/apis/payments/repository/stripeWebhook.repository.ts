import { CustomError } from '@server/utils/customError';
import { db } from '@server/db';
import { invoicesTable } from '@server/db/schema';
import { eq, and } from 'drizzle-orm';
import { InvoiceStatus, PaymentMethod } from '../types/invoice.types';
import { env } from '../../../config/env';

interface StripeWebhookRepositoryResult {
  isSuccess: boolean;
  eventType?: string;
}

export default async function stripeWebhookRepository(
  body: string,
  signature: string,
  studioId: string,
  requestId: string
): Promise<StripeWebhookRepositoryResult> {
  try {
    console.info(
      `${requestId} [PAYMENTS] - STRIPE_WEBHOOK repository operation started`
    );

    // Verify the webhook signature
    let event;
    try {
      console.log('body', body);
      console.log('signature', signature);
      console.log('env.STRIPE_WEBHOOK_SECRET', env.STRIPE_WEBHOOK_SECRET);
      // Temporarily bypass signature verification for testing
      // event = stripe.webhooks.constructEvent(
      //   body,
      //   signature,
      //   env.STRIPE_WEBHOOK_SECRET
      // );

      // For now, just parse the body as JSON
      event = JSON.parse(body);
      console.info(
        `${requestId} [PAYMENTS] - Bypassed signature verification for testing`
      );
    } catch (err) {
      console.error(
        `${requestId} [PAYMENTS] - Webhook signature verification failed:`,
        err
      );
      throw new CustomError(
        'INVALID_SIGNATURE',
        'Invalid webhook signature',
        400
      );
    }

    console.info(
      `${requestId} [PAYMENTS] - Processing webhook event: ${event.type}`
    );

    // Handle different event types
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(
          event.data.object,
          studioId,
          requestId
        );
        break;
      case 'payment_intent.payment_failed':
        await handlePaymentIntentFailed(event.data.object, studioId, requestId);
        break;
      default:
        console.info(
          `${requestId} [PAYMENTS] - Unhandled event type: ${event.type}`
        );
    }

    console.info(
      `${requestId} [PAYMENTS] - STRIPE_WEBHOOK repository operation completed`
    );

    return {
      isSuccess: true,
      eventType: event.type,
    };
  } catch (error) {
    console.error(
      `${requestId} [PAYMENTS] - STRIPE_WEBHOOK repository operation failed:`,
      error
    );
    throw error;
  }
}

async function handlePaymentIntentSucceeded(
  paymentIntent: any,
  studioId: string,
  requestId: string
): Promise<void> {
  console.info(
    `${requestId} [PAYMENTS] - Handling payment intent succeeded: ${paymentIntent.id}`
  );

  try {
    const invoiceId = paymentIntent.metadata?.invoice_id;
    const paymentIntentStudioId = paymentIntent.metadata?.studio_id;

    if (!invoiceId) {
      console.warn(
        `${requestId} [PAYMENTS] - No invoice_id in payment intent metadata`
      );
      return;
    }

    if (paymentIntentStudioId !== studioId) {
      console.warn(
        `${requestId} [PAYMENTS] - Studio ID mismatch in payment intent metadata`
      );
      return;
    }

    // Get the invoice
    const invoice = await db
      .select()
      .from(invoicesTable)
      .where(
        and(
          eq(invoicesTable.id, invoiceId),
          eq(invoicesTable.studio_id, studioId)
        )
      )
      .limit(1);

    if (invoice.length === 0) {
      console.warn(`${requestId} [PAYMENTS] - Invoice not found: ${invoiceId}`);
      return;
    }

    const currentInvoice = invoice[0];
    const amountReceived =
      paymentIntent.amount_received || paymentIntent.amount;

    if (!currentInvoice) {
      console.error(
        `${requestId} [PAYMENTS] - Invoice not found: ${invoiceId}`
      );
      return;
    }

    // Create payment record (Stripe amounts are in cents, convert to dollars for storage)
    const newPayment = {
      method: PaymentMethod.CARD, // Default to card for Stripe payments
      amount: amountReceived / 100, // Convert cents to dollars
      paymentIntentId: paymentIntent.id,
      date: new Date(),
    };

    // Update payments array
    const updatedPayments = [...(currentInvoice.payments || []), newPayment];

    // Calculate total paid amount (everything in dollars)
    const totalPaid = updatedPayments.reduce(
      (sum, payment) => sum + payment.amount,
      0
    );

    // Determine new status
    let newStatus: InvoiceStatus;
    if (totalPaid >= parseFloat(currentInvoice.final_amount)) {
      newStatus = InvoiceStatus.PAID;
    } else {
      newStatus = InvoiceStatus.PARTIALLY_PAID;
    }

    // Update invoice with new status and payment info
    await db
      .update(invoicesTable)
      .set({
        status: newStatus,
        payments: updatedPayments,
        payment_date:
          newStatus === InvoiceStatus.PAID
            ? new Date()
            : currentInvoice.payment_date,
        metadata: {
          ...currentInvoice.metadata,
          paymentIntentId: paymentIntent.id,
        },
      })
      .where(eq(invoicesTable.id, invoiceId));

    console.info(
      `${requestId} [PAYMENTS] - Invoice ${invoiceId} updated to status: ${newStatus}, total paid: ${totalPaid}`
    );
  } catch (error) {
    console.error(
      `${requestId} [PAYMENTS] - Error handling payment success:`,
      error
    );
    throw error;
  }
}

async function handlePaymentIntentFailed(
  paymentIntent: any,
  studioId: string,
  requestId: string
): Promise<void> {
  console.info(
    `${requestId} [PAYMENTS] - Handling payment intent failed: ${paymentIntent.id}`
  );

  try {
    const invoiceId = paymentIntent.metadata?.invoice_id;
    const paymentIntentStudioId = paymentIntent.metadata?.studio_id;

    if (!invoiceId) {
      console.warn(
        `${requestId} [PAYMENTS] - No invoice_id in payment intent metadata`
      );
      return;
    }

    if (paymentIntentStudioId !== studioId) {
      console.warn(
        `${requestId} [PAYMENTS] - Studio ID mismatch in payment intent metadata`
      );
      return;
    }

    // Get the invoice
    const invoice = await db
      .select()
      .from(invoicesTable)
      .where(
        and(
          eq(invoicesTable.id, invoiceId),
          eq(invoicesTable.studio_id, studioId)
        )
      )
      .limit(1);

    if (invoice.length === 0) {
      console.warn(`${requestId} [PAYMENTS] - Invoice not found: ${invoiceId}`);
      return;
    }

    const currentInvoice = invoice[0];
    const failureReason =
      paymentIntent.last_payment_error?.message || 'Payment failed';
    const attemptCount =
      ((currentInvoice && currentInvoice.metadata?.attemptCount) || 0) + 1;

    // Update invoice with failure information
    await db
      .update(invoicesTable)
      .set({
        status: InvoiceStatus.FAILED,
        metadata: {
          ...(currentInvoice?.metadata || {}),
          failureReason,
          attemptCount,
          lastAttemptDate: new Date(),
          paymentIntentId: paymentIntent.id,
        },
      })
      .where(eq(invoicesTable.id, invoiceId));

    console.info(
      `${requestId} [PAYMENTS] - Invoice ${invoiceId} marked as failed. Reason: ${failureReason}, Attempt: ${attemptCount}`
    );
  } catch (error) {
    console.error(
      `${requestId} [PAYMENTS] - Error handling payment failure:`,
      error
    );
    throw error;
  }
}
