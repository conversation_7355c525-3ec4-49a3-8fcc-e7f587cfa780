import type { CustomError } from '@server/utils/customError';
import type { CreateInvoiceInput } from '../validators/createInvoice.validator';
import createInvoiceRepository from '../repository/createInvoice.repository';

type typeResultData = {
  invoiceId: string;
  paymentIntentId?: string;
  clientSecret?: string;
  message: string;
};

type typeResultError = {
  code: string;
  message: string;
  statusCode: number;
};

type typeResult = {
  data: null | typeResultData;
  error: null | typeResultError;
};

export default async function createInvoiceHandler(
  payload: CreateInvoiceInput,
  studioId: string,
  requestId: string
): Promise<typeResult> {
  let data: typeResultData | null = null;
  let error: typeResultError | null = null;

  try {
    console.info(`${requestId} [PAYMENTS] - CREATE_INVOICE handler started`);

    const result = await createInvoiceRepository(payload, studioId, requestId);

    if (result.isSuccess) {
      data = {
        invoiceId: result.invoiceId!,
        paymentIntentId: result.paymentIntentId,
        clientSecret: result.clientSecret,
        message: 'Invoice created successfully',
      };
    }

    console.info(
      `${requestId} [PAYMENTS] - CREATE_INVOICE handler completed successfully`
    );
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [PAYMENTS] - CREATE_INVOICE handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
