import type { CustomError } from '@server/utils/customError';
import stripeWebhookRepository from '../repository/stripeWebhook.repository';

type typeResultData = {
  message: string;
  eventType: string;
};

type typeResultError = {
  code: string;
  message: string;
  statusCode: number;
};

type typeResult = {
  data: null | typeResultData;
  error: null | typeResultError;
};

export default async function stripeWebhookHandler(
  body: string,
  signature: string,
  studioId: string,
  requestId: string
): Promise<typeResult> {
  let data: typeResultData | null = null;
  let error: typeResultError | null = null;

  try {
    console.info(`${requestId} [PAYMENTS] - STRIPE_WEBHOOK handler started`);

    const result = await stripeWebhookRepository(
      body,
      signature,
      studioId,
      requestId
    );

    if (result.isSuccess) {
      data = {
        message: 'Webhook processed successfully',
        eventType: result.eventType || 'unknown',
      };
    }

    console.info(
      `${requestId} [PAYMENTS] - STRIPE_WEBHOOK handler completed successfully`
    );
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [PAYMENTS] - STRIPE_WEBHOOK handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
