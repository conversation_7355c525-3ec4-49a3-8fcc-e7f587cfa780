import type { CustomError } from '@server/utils/customError';
import getInvoicesRepository from '../repository/getInvoices.repository';

type typeResultData = {
  invoices: {
    id: string;
    studio_id: string;
    user_id: string;
    entity_id?: string;
    entity_type?: string;
    status: string;
    payment_provider: string;
    payment_method: string;
    type: string;
    transaction_id?: string;
    line_items: any[];
    payments: any[];
    base_amount: string;
    final_amount: string;
    payment_date?: string;
    due_date?: string;
    start_date?: string;
    end_date?: string;
    metadata: any;
    created_at: string;
    updated_at: string;
    deleted_at?: string;
    user?: {
      first_name: string;
      last_name: string;
      email: string;
    };
  }[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
};

type typeResultError = {
  code: string;
  message: string;
  statusCode: number;
};

type typeResult = {
  data: null | typeResultData;
  error: null | typeResultError;
};

export default async function getInvoicesHandler(
  page: number = 1,
  limit: number = 10,
  status: string | undefined,
  studioId: string,
  requestId: string
): Promise<typeResult> {
  let data: typeResultData | null = null;
  let error: typeResultError | null = null;

  try {
    console.info(`${requestId} [PAYMENTS] - GET_INVOICES handler started`);

    const { isSuccess, data: invoicesData } = await getInvoicesRepository(
      page,
      limit,
      status,
      studioId,
      requestId
    );

    if (isSuccess) {
      data = invoicesData as typeResultData;
    }

    console.info(
      `${requestId} [PAYMENTS] - GET_INVOICES handler completed successfully`
    );
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [PAYMENTS] - GET_INVOICES handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
