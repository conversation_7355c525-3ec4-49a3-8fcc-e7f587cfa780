import type { Context } from 'hono';
import createInvoiceHandler from '../handlers/createInvoice.handler';
import type { CreateInvoiceInput } from '../validators/createInvoice.validator';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const createInvoiceController = async (c: Context) => {
  const requestId = c.get('requestId');
  const user = c.get('user');

  console.log(`${requestId} [PAYMENTS] - CREATE_INVOICE - Request received`);

  try {
    // Get studio_id from authenticated user metadata
    const studioId = user?.user_metadata?.studio_id;

    if (!studioId) {
      console.error(
        `${requestId} [PAYMENTS] - CREATE_INVOICE - Missing studio ID`
      );
      return c.json(
        { data: null, error: { message: 'Studio ID not found' } },
        400
      );
    }

    const payload = (await c.req.json()) as CreateInvoiceInput;

    const result = await createInvoice<PERSON>andler(payload, studioId, requestId);

    if (result.error) {
      console.error(
        `${requestId} [PAYMENTS] - CREATE_INVOICE - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: { message: result.error.message } },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(
      `${requestId} [PAYMENTS] - CREATE_INVOICE - Response sent successfully`
    );
    return c.json({ data: result.data, error: result.error }, 201);
  } catch (err) {
    console.error(
      `${requestId} [PAYMENTS] - CREATE_INVOICE - Controller error: ${err}`
    );
    return c.json(
      { data: null, error: { message: 'Internal server error' } },
      500
    );
  }
};
