import type { Context } from 'hono';
import type { ContentfulStatusCode } from 'hono/utils/http-status';
import getInvoicesHandler from '../handlers/getInvoices.handler';

export const getInvoicesController = async (c: Context) => {
  const requestId = c.get('requestId');
  const user = c.get('user');

  console.log(`${requestId} [PAYMENTS] - GET_INVOICES - Request received`);

  try {
    // Get studio_id from authenticated user metadata
    const studioId = user?.user_metadata?.studio_id;

    if (!studioId) {
      console.error(
        `${requestId} [PAYMENTS] - GET_INVOICES - Missing studio ID`
      );
      return c.json(
        { data: null, error: { message: 'Studio ID not found' } },
        400
      );
    }

    // Get pagination parameters from query
    const pageParam = c.req.query('page');
    const limitParam = c.req.query('limit');
    const statusParam = c.req.query('status');

    // Parse and validate pagination parameters
    const page = pageParam ? parseInt(pageParam, 10) : 1;
    const limit = limitParam ? parseInt(limitParam, 10) : 10;
    const status = statusParam || undefined;

    const result = await getInvoicesHandler(
      page,
      limit,
      status,
      studioId,
      requestId
    );

    if (result.error) {
      console.error(
        `${requestId} [PAYMENTS] - GET_INVOICES - Handler error: ${result.error.message}`
      );
      return c.json(
        { data: null, error: { message: result.error.message } },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(
      `${requestId} [PAYMENTS] - GET_INVOICES - Response sent successfully`
    );
    return c.json({ data: result.data, error: result.error }, 200);
  } catch (err) {
    console.error(
      `${requestId} [PAYMENTS] - GET_INVOICES - Controller error: ${err}`
    );
    return c.json(
      { data: null, error: { message: 'Internal server error' } },
      500
    );
  }
};
