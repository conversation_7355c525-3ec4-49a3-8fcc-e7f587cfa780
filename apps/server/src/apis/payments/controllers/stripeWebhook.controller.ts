import type { Context } from 'hono';
import type { ContentfulStatusCode } from 'hono/utils/http-status';
import stripeWebhookHandler from '../handlers/stripeWebhook.handler';

export const stripeWebhookController = async (c: Context) => {
  const requestId = c.get('requestId') || 'webhook-' + Date.now();

  console.log(`${requestId} [PAYMENTS] - STRIPE_WEBHOOK - Request received`);

  try {
    // Get studio_id from query params
    const studioId = c.req.query('studio_id');

    if (!studioId) {
      console.error(
        `${requestId} [PAYMENTS] - STRIPE_WEBHOOK - Missing studio_id parameter`
      );
      return c.json({ error: 'Missing studio_id parameter' }, 400);
    }

    // Get the raw body for Stripe signature verification
    const body = await c.req.text();
    const signature = c.req.header('stripe-signature');

    if (!signature) {
      console.error(
        `${requestId} [PAYMENTS] - STRIPE_WEBHOOK - Missing Stripe signature`
      );
      return c.json({ error: 'Missing Stripe signature' }, 400);
    }

    const result = await stripeWebhookHandler(
      body,
      signature,
      studioId,
      requestId
    );

    if (result.error) {
      console.error(
        `${requestId} [PAYMENTS] - STRIPE_WEBHOOK - Handler error: ${result.error.message}`
      );

      return c.json(
        { error: result.error.message },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(
      `${requestId} [PAYMENTS] - STRIPE_WEBHOOK - Response sent successfully`
    );
    return c.json({ received: true }, 200);
  } catch (err) {
    console.error(
      `${requestId} [PAYMENTS] - STRIPE_WEBHOOK - Controller error: ${err}`
    );
    return c.json({ error: 'Internal server error' }, 500);
  }
};
