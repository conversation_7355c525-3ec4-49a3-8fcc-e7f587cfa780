export enum PaymentProvider {
  STRIPE = 'stripe',
  PAYPAL = 'paypal',
  MANUAL = 'manual',
  CASH = 'cash',
}

export enum PaymentMethod {
  CARD = 'card',
  LINK = 'link',
  US_BANK_ACCOUNT = 'us_bank_account',
  CASH = 'cash',
  CHECK = 'check',
  WALLET = 'wallet',
}

export enum InvoiceType {
  SUBSCRIPTION = 'subscription',
  ONE_TIME = 'one_time',
  UPFRONT = 'upfront',
  MANUAL = 'manual',
  BULK_PAYMENT = 'bulk_payment',
}

export enum InvoiceStatus {
  PAID = 'paid',
  PARTIALLY_PAID = 'partially_paid',
  UPCOMING = 'upcoming',
  PENDING = 'pending',
  FULL_REFUND = 'full_refund',
  PARTIAL_REFUND = 'partial_refund',
  PENDING_REFUND = 'pending_refund',
  REFUND_FAILED = 'refund_failed',
  VOID = 'void',
  FAILED = 'failed',
  SCHEDULED = 'scheduled',
  PAUSED = 'paused',
  PROCESSING = 'processing',
  CANCELLED = 'cancelled',
  FREE = 'free',
}

export interface LineItem {
  name: string;
  amount: number; // in dollars
  type: string;
  quantity: number;
  total: number; // in dollars
}

export interface Payment {
  method: PaymentMethod;
  amount: number; // in dollars
  checkNumber?: string;
  paymentIntentId?: string;
  date: Date;
}

export interface InvoiceMetadata {
  paymentIntentId?: string;
  failureReason?: string;
  refundReason?: string;
  attemptCount?: number;
  lastAttemptDate?: Date;
  internalTransactionId?: string;
  refundId?: string;
}

export interface CreateInvoiceRequest {
  userId: string;
  studioId: string;
  lineItems: LineItem[];
  entityId?: string;
  entityType?: string;
  dueDate?: Date;
  type: InvoiceType;
  paymentMethod?: PaymentMethod;
}

export interface CreatePaymentIntentRequest {
  invoiceId: string;
  amount: number; // in dollars
  currency?: string;
}

export interface StripeWebhookEvent {
  id: string;
  type: string;
  data: {
    object: any;
  };
}
