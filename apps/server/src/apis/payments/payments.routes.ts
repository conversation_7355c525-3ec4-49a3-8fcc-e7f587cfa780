import { Hono } from 'hono';
import { createInvoiceController } from './controllers/createInvoice.controller';
import { getInvoicesController } from './controllers/getInvoices.controller';
import { stripeWebhookController } from './controllers/stripeWebhook.controller';
import { validateCreateInvoice } from './validators/createInvoice.validator';
import { authValidator } from '../../middlewares/auth/auth.middleware';

const paymentsRoutes = new Hono()
  .post(
    '/create-invoice',
    authValidator,
    validateCreateInvoice('json'),
    createInvoiceController
  )
  .get('/invoices', authValidator, getInvoicesController)
  .post(
    '/webhook/stripe',
    stripeWebhookController // No auth middleware for webhooks
  );

export default paymentsRoutes;
