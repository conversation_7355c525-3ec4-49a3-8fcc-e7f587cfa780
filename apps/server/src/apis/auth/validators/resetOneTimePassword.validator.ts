import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { db } from '@server/db';
import { studioTable } from '@server/db/schema/studio';
import { eq } from 'drizzle-orm';
import { validator } from 'hono/validator';

export const resetOneTimePasswordSchema = z.object({
  email: z.string().email(),
  current_password: z.string(),
  new_password: z.string().min(6),
});

export type ResetOneTimePasswordInput = z.infer<
  typeof resetOneTimePasswordSchema
>;

export const validateResetOneTimePassword = (
  target: 'json' | 'form' | 'query' | 'param' | 'header' | 'cookie' = 'json'
) => zValidator(target, resetOneTimePasswordSchema);

export const validateResetOneTimePasswordQuery = validator(
  'query',
  async (value, c) => {
    const studio_id = value['studio_id'];

    if (!studio_id) {
      return c.json(
        {
          success: false,
          data: null,
          error: {
            message: 'Studio ID is required.',
          },
        },
        400
      );
    }

    if (studio_id) {
      const studio = await db
        .selectDistinct()
        .from(studioTable)
        .where(eq(studioTable.id, studio_id as string));

      if (studio.length === 0) {
        return c.json(
          {
            success: false,
            data: null,
            error: {
              message: 'Studio not found.',
            },
          },
          404
        );
      }
    }
  }
);
