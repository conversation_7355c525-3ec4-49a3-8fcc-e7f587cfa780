import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { db } from '@server/db';
import { studioTable } from '@server/db/schema/studio';
import { eq } from 'drizzle-orm';
import { validator } from 'hono/validator';

export const signInSchema = z.object({
  email: z.string().email(),
  password: z.string(),
});

export type SignInInput = z.infer<typeof signInSchema>;

export const validateSignIn = (
  target: 'json' | 'form' | 'query' | 'param' | 'header' | 'cookie' = 'json'
) => zValidator(target, signInSchema);

export const validateSigninQuery = validator('query', async (value, c) => {
  const studio_id = value['studio_id'];

  if (!studio_id) {
    return c.json(
      {
        success: false,
        data: null,
        error: {
          message: 'Studio ID is required.',
        },
      },
      400
    );
  }

  if (studio_id) {
    try {
      // Ensure studio_id is a string (query params can be arrays)
      const studioIdString = Array.isArray(studio_id)
        ? studio_id[0]
        : studio_id;

      // Check if we have a valid string
      if (typeof studioIdString !== 'string') {
        return c.json(
          {
            success: false,
            data: null,
            error: {
              message: 'Invalid studio ID format.',
            },
          },
          400
        );
      }

      // Check if studio exists in database
      const studio = await db
        .select()
        .from(studioTable)
        .where(eq(studioTable.id, studioIdString))
        .limit(1);

      if (studio.length === 0) {
        return c.json(
          {
            success: false,
            data: null,
            error: {
              message:
                'Studio not found. The provided studio ID does not exist in our records.',
            },
          },
          400
        );
      }
    } catch (error) {
      console.error(error);
      return c.json(
        {
          success: false,
          data: null,
          error: {
            message: 'Failed to validate studio information. Please try again.',
          },
        },
        400
      );
    }
  }

  return studio_id;
});
