import { CustomError } from '@server/utils/customError';
import { supabase } from '../../../utils/supabase';

export default async function verifyAuthRepository(
  id: string,
  requestId: string
) {
  try {
    console.info(`${requestId} [AUTH] - VERIFY repository operation started`);
    const { data: dbUser, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single();
    if (userError) {
      throw new CustomError('DB_ERROR', userError.message, 500);
    }
    console.info(`${requestId} [AUTH] - VERIFY repository operation completed`);
    return { isSuccess: true, data: dbUser };
  } catch (err) {
    console.error(
      `${requestId} [AUTH] - VERIFY repository operation failed ${err}`
    );
    throw new CustomError('DB_ERROR', `Failed to verify auth`, 500);
  }
}
