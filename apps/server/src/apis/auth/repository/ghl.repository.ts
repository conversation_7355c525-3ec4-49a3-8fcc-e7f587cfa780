import axios, { AxiosError } from 'axios';
import { CustomError } from '@server/utils/customError';
import { env } from '../../../config/env';
import { db } from '../../../db';
import { studioTable } from '../../../db/schema/studio';
import { eq } from 'drizzle-orm';
import type { GhlHandlerInput } from '../handlers/ghl.handler';

interface GhlRepositoryResult {
  isSuccess: boolean;
  locationId: string;
}

export default async function ghlRepository(
  payload: GhlHandlerInput,
  requestId: string
): Promise<GhlRepositoryResult> {
  console.info(`${requestId} [AUTH] - GHL repository operation started`);

  try {
    // Step 1: Exchange code for tokens
    const tokenData = await exchangeCodeForTokens(payload.code, requestId);

    // Step 2: Store or update studio tokens
    await storeStudioTokens(tokenData, requestId);

    // Step 3: Fetch and update location details
    await updateLocationDetails(
      tokenData.locationId,
      tokenData.access_token,
      requestId
    );

    console.info(`${requestId} [AUTH] - GHL repository operation completed`);
    return { isSuccess: true, locationId: tokenData.locationId };
  } catch (error) {
    console.error(
      `${requestId} [AUTH] - GHL repository operation failed:`,
      error
    );
    throw error;
  }
}

async function exchangeCodeForTokens(code: string, requestId: string) {
  const clientId = env.GHL_CLIENT_ID;
  const clientSecret = env.GHL_CLIENT_SECRET;
  const base_url = env.GHL_BASE_URL;

  console.info(`${requestId} [AUTH] - GHL exchanging code for tokens`);

  try {
    const response = await axios.post(
      `${base_url}/oauth/token`,
      {
        client_id: clientId,
        client_secret: clientSecret,
        user_type: 'Location',
        grant_type: 'authorization_code',
        code,
      },
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );

    const { access_token, refresh_token, expires_in, locationId } =
      response.data;
    const tokenExpiry = new Date(Date.now() + expires_in * 1000);

    console.info(
      `${requestId} [AUTH] - GHL tokens exchanged successfully for location ${locationId}`
    );

    return {
      access_token,
      refresh_token,
      tokenExpiry,
      locationId,
    };
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new CustomError(
        'GHL_ERROR',
        error.response?.data?.message || 'Failed to exchange code for tokens',
        error.response?.status || 400
      );
    }
    throw new CustomError(
      'GHL_ERROR',
      'Failed to exchange code for tokens',
      500
    );
  }
}

async function storeStudioTokens(tokenData: any, requestId: string) {
  const { locationId, access_token, refresh_token, tokenExpiry } = tokenData;

  console.info(
    `${requestId} [AUTH] - GHL storing studio tokens for location ${locationId}`
  );

  try {
    const existing = await db
      .select()
      .from(studioTable)
      .where(eq(studioTable.id, locationId));

    if (existing.length === 0) {
      await db.insert(studioTable).values({
        id: locationId,
        access_token,
        refresh_token,
        token_expiry: tokenExpiry,
      });
      console.info(
        `${requestId} [AUTH] - GHL created new studio record for location ${locationId}`
      );
    } else {
      await db
        .update(studioTable)
        .set({ access_token, refresh_token, token_expiry: tokenExpiry })
        .where(eq(studioTable.id, locationId));
      console.info(
        `${requestId} [AUTH] - GHL updated existing studio record for location ${locationId}`
      );
    }
  } catch (error) {
    throw new CustomError(
      'DB_ERROR',
      `Failed to store studio tokens: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}

async function updateLocationDetails(
  locationId: string,
  accessToken: string,
  requestId: string
) {
  const base_url = env.GHL_BASE_URL;

  console.info(
    `${requestId} [AUTH] - GHL fetching location details for ${locationId}`
  );

  try {
    const locationResponse = await axios.get(
      `${base_url}/locations/${locationId}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Version: '2021-07-28',
        },
      }
    );

    const { location: locationData } = locationResponse.data;
    const { name, address, logoUrl, phone, email, timezone } = locationData;

    await db
      .update(studioTable)
      .set({
        name,
        address,
        logo_url: logoUrl,
        phone,
        email,
        timezone,
      })
      .where(eq(studioTable.id, locationId));

    console.info(
      `${requestId} [AUTH] - GHL updated location details for ${locationId}`
    );
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new CustomError(
        'GHL_ERROR',
        error.response?.data?.message || 'Failed to fetch location details',
        error.response?.status || 400
      );
    }
    throw new CustomError(
      'GHL_ERROR',
      'Failed to update location details',
      500
    );
  }
}
