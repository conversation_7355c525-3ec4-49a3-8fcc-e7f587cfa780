import { CustomError } from '@server/utils/customError';
import { supabase } from '../../../utils/supabase';
import { db } from '@server/db';
import { usersTable } from '@server/db/schema/user';
import { eq, and } from 'drizzle-orm';
import { transformEmailWithStudioId } from '@server/utils/transform-email';

export default async function resetOneTimePasswordRepository(
  {
    email,
    current_password,
    new_password,
  }: { email: string; current_password: string; new_password: string },
  studio_id: string,
  requestId: string
) {
  try {
    console.info(
      `${requestId} [AUTH] - RESET_OTP repository operation started`
    );

    const transformedEmail = transformEmailWithStudioId(email, studio_id);

    // First, verify current password by attempting to sign in
    const { data: signInData, error: signInError } =
      await supabase.auth.signInWithPassword({
        email: transformedEmail,
        password: current_password,
      });

    if (signInError) {
      throw new CustomError(
        'INVALID_CURRENT_PASSWORD',
        'Current password is incorrect',
        401
      );
    }

    const { user } = signInData;

    // Get the current user from database
    const dbUser = await db
      .selectDistinct()
      .from(usersTable)
      .where(
        and(eq(usersTable.id, user.id), eq(usersTable.studio_id, studio_id))
      );

    if (dbUser.length === 0) {
      throw new CustomError(
        'USER_NOT_FOUND',
        'User not found in the database',
        404
      );
    }

    // Update password in Supabase
    const { error: updateError } = await supabase.auth.updateUser({
      password: new_password,
    });

    if (updateError) {
      throw new CustomError(
        'DB_ERROR',
        updateError.message,
        updateError.status || 500
      );
    }

    // Update isPasswordSet to true in the database
    const [updatedUser] = await db
      .update(usersTable)
      .set({ isPasswordSet: true })
      .where(
        and(eq(usersTable.id, user.id), eq(usersTable.studio_id, studio_id))
      )
      .returning();

    if (!updatedUser) {
      throw new CustomError(
        'DB_ERROR',
        'Failed to update password status in database',
        500
      );
    }

    console.info(
      `${requestId} [AUTH] - RESET_OTP repository operation completed`
    );
    return {
      isSuccess: true,
      data: {
        user: updatedUser,
        message:
          'Password has been successfully reset and your account is now fully set up.',
      },
    };
  } catch (err) {
    console.error(
      `${requestId} [AUTH] - RESET_OTP repository operation failed ${err}`
    );
    throw err;
  }
}
