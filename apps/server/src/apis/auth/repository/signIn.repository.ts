import { CustomError } from '@server/utils/customError';
import { supabase } from '../../../utils/supabase';
import { db } from '@server/db';
import { usersTable } from '@server/db/schema/user';
import { eq } from 'drizzle-orm';
import { transformEmailWithStudioId } from '@server/utils/transform-email';

export default async function signInAuthRepository(
  { email, password }: { email: string; password: string },
  studio_id: string,
  requestId: string
) {
  try {
    console.info(`${requestId} [AUTH] - SIGNIN repository operation started`);

    const transformedEmail = transformEmailWithStudioId(email, studio_id);

    const { data, error } = await supabase.auth.signInWithPassword({
      email: transformedEmail,
      password,
    });

    if (error) {
      throw new CustomError('DB_ERROR', error.message, error.status);
    }
    const { user, session } = data;

    const dbUser = await db
      .selectDistinct()
      .from(usersTable)
      .where(eq(usersTable.id, user.id));

    console.info(`${requestId} [AUTH] - SIGNIN repository operation completed`);
    return {
      isSuccess: true,
      data: {
        user: dbUser[0]!,
        accessToken: session.access_token,
        refreshToken: session.refresh_token,
      },
    };
  } catch (err) {
    console.error(
      `${requestId} [AUTH] - SIGNIN repository operation failed ${err}`
    );
    throw err;
  }
}
