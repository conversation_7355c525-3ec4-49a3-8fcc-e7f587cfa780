import { CustomError } from '@server/utils/customError';
import { supabase, supabaseAdmin } from '../../../utils/supabase';
import type { SignupInput } from '../validators/signUp.validator';
import { db } from '@server/db';
import { usersTable, stripeCustomersTable } from '@server/db/schema';
import { eq, and } from 'drizzle-orm';
import { transformEmailWithStudioId } from '@server/utils/transform-email';
import { resend } from '@server/lib/resend-client';
import { signupTemplate } from '@server/utils/emailTemplates';
import { StripeService } from '../../stripe/services/stripe.service';

interface SignUpState {
  userId?: string;
  supabaseUserCreated: boolean;
  dbTransactionCompleted: boolean;
  stripeCustomerCreated: boolean;
  stripeCustomerId?: string;
}

type roleType = 'parent' | 'instructor' | 'admin' | 'super-admin';

export default async function signUpAuthRepository(
  payload: SignupInput,
  role: roleType,
  studioId: string,
  requestId: string
) {
  const state: SignUpState = {
    supabaseUserCreated: false,
    dbTransactionCompleted: false,
    stripeCustomerCreated: false,
  };

  console.info(`${requestId} [AUTH] - SIGNUP repository operation started`);

  try {
    await checkExistingUser(payload.email, studioId);

    // Step 2: Create Supabase auth user
    state.userId = await createSupabaseUser(payload, role, studioId, requestId);
    state.supabaseUserCreated = true;

    // Step 3: Create Stripe customer if role is parent
    if (role === 'parent') {
      state.stripeCustomerId = await createStripeCustomer(
        payload,
        state.userId,
        studioId,
        requestId
      );
      state.stripeCustomerCreated = true;
    }

    // Step 4: Create database records in transaction
    await createDatabaseRecords(
      payload,
      role,
      state.userId,
      studioId,
      state.stripeCustomerId,
      requestId
    );
    state.dbTransactionCompleted = true;

    if (payload.password.startsWith('temp_')) {
      const { error } = await resend.emails.send({
        from: 'Support Team <<EMAIL>>',
        to: payload.email,
        subject: 'Welcome to Horse Riding Studio!',
        html: signupTemplate({
          name: `${payload.first_name} ${payload.last_name}`,
          password: payload.password,
          studioId: studioId,
        }),
      });
      if (error) {
        console.info(`${requestId} [AUTH] - error sending email`, error);
        throw new Error('Error sending mail.');
      }
    }
    console.info(`${requestId} [AUTH] - SIGNUP repository operation completed`);
    return { isSuccess: true };
  } catch (error) {
    console.error(
      `${requestId} [AUTH] - SIGNUP repository operation failed:`,
      error
    );

    // Rollback operations in reverse order
    await rollbackOperations(state, requestId);

    // Re-throw the original error
    throw error;
  }
}

async function checkExistingUser(
  email: string,
  studioId: string
): Promise<void> {
  const existingUser = await db
    .selectDistinct()
    .from(usersTable)
    .where(
      and(eq(usersTable.email, email), eq(usersTable.studio_id, studioId))
    );

  if (existingUser.length > 0) {
    throw new CustomError(
      'DB_ERROR',
      'User with same email already exists',
      409
    );
  }
}

async function createSupabaseUser(
  payload: SignupInput,
  role: roleType,
  studioId: string,
  requestId: string
): Promise<string> {
  const { password } = payload;

  // Transform email to include studio ID
  const transformedEmail = transformEmailWithStudioId(payload.email, studioId);

  const { data: supabaseData, error } = await supabase.auth.signUp({
    email: transformedEmail,
    password: password,
    options: {
      data: {
        role,
        studio_id: studioId,
      },
    },
  });

  if (error) {
    throw new CustomError('SUPABASE_ERROR', error.message, error.status || 500);
  }

  const userId = supabaseData.user?.id;
  if (!userId) {
    throw new CustomError(
      'SUPABASE_ERROR',
      'User ID not returned from Supabase signUp',
      500
    );
  }

  console.info(`${requestId} [AUTH] - Supabase user created: ${userId}`);
  return userId;
}

async function createStripeCustomer(
  payload: SignupInput,
  userId: string,
  studioId: string,
  requestId: string
): Promise<string> {
  try {
    console.info(`${requestId} [AUTH] - Creating Stripe customer for parent`);

    const stripeCustomerId = await StripeService.createCustomer(
      {
        email: payload.email,
        name: `${payload.first_name} ${payload.last_name}`,
        phone: payload.phone,
        metadata: {
          user_id: userId,
          studio_id: studioId,
          role: 'parent',
        },
      },
      requestId
    );

    console.info(
      `${requestId} [AUTH] - Stripe customer created: ${stripeCustomerId}`
    );
    return stripeCustomerId;
  } catch (error) {
    console.error(
      `${requestId} [AUTH] - Failed to create Stripe customer:`,
      error
    );
    throw error;
  }
}

async function createDatabaseRecords(
  payload: SignupInput,
  role: roleType,
  userId: string,
  studioId: string,
  stripeCustomerId: string | undefined,
  requestId: string
): Promise<void> {
  const {
    email,
    first_name,
    last_name,
    address,
    country_code,
    date_of_birth,
    emergency_contact_name,
    emergency_contact_phone,
    phone,
    profile_image,
  } = payload;

  try {
    await db.transaction(async (tx) => {
      // Insert user record
      await tx.insert(usersTable).values({
        id: userId,
        email,
        first_name,
        last_name,
        role,
        studio_id: studioId,
        address,
        country_code,
        date_of_birth: date_of_birth as unknown as string,
        emergency_contact_name,
        emergency_contact_phone,
        phone,
        profile_image,
        isPasswordSet: payload.password.startsWith('temp_') ? false : true,
      });

      // Insert Stripe customer record if role is parent
      if (role === 'parent' && stripeCustomerId) {
        await tx.insert(stripeCustomersTable).values({
          user_id: userId,
          stripe_customer_id: stripeCustomerId,
        });
      }
    });

    console.info(
      `${requestId} [AUTH] - Database transaction completed for user: ${userId}`
    );
  } catch (error) {
    console.error(`${requestId} [AUTH] - Database transaction failed:`, error);
    throw new CustomError(
      'DB_ERROR',
      `Failed to create database records: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}

async function rollbackOperations(
  state: SignUpState,
  requestId: string
): Promise<void> {
  // Rollback Stripe customer if created
  if (state.stripeCustomerCreated && state.stripeCustomerId) {
    try {
      console.info(
        `${requestId} [AUTH] - Rolling back Stripe customer: ${state.stripeCustomerId}`
      );
      await StripeService.deleteCustomer(state.stripeCustomerId, requestId);
    } catch (rollbackError) {
      console.error(
        `${requestId} [AUTH] - Failed to rollback Stripe customer:`,
        rollbackError
      );
    }
  }

  // Rollback Supabase user if created
  if (
    state.supabaseUserCreated &&
    !state.dbTransactionCompleted &&
    state.userId
  ) {
    try {
      console.info(
        `${requestId} [AUTH] - Rolling back Supabase user: ${state.userId}`
      );

      const { error } = await supabaseAdmin.auth.admin.deleteUser(state.userId);

      if (error) {
        console.error(
          `${requestId} [AUTH] - Failed to rollback Supabase user:`,
          error
        );
      } else {
        console.info(
          `${requestId} [AUTH] - Successfully rolled back Supabase user: ${state.userId}`
        );
      }
    } catch (rollbackError) {
      console.error(
        `${requestId} [AUTH] - Rollback operation failed:`,
        rollbackError
      );
    }
  }
}
