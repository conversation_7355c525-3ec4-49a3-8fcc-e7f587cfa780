import type {
  typeResult,
  typeResultData,
  typeResultError,
} from '../../../../../shared/src/types/auth/verify.types';
import authVerify from '../repository/verify.repository';
import type { CustomError } from '@server/utils/customError';

export default async function verifyAuth(
  id: string,
  requestId: string
): Promise<typeResult> {
  let data: typeResultData | null = null;
  let error: typeResultError | null = null;

  try {
    console.info(`${requestId} [AUTH] - VERIFY handler started`);

    const { isSuccess, data: userData } = await authVerify(id, requestId);

    if (isSuccess) {
      data = userData;
    }

    console.info(`${requestId} [AUTH] - VERIFY handler completed successfully`);
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [AUTH] - VERIFY handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
