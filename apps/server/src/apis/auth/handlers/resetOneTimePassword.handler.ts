import type {
  typeResult,
  typeResultData,
  typeResultError,
} from '../../../../../shared/src/types/auth/resetOneTimePassword.types';
import resetOneTimePasswordRepo from '../repository/resetOneTimePassword.repository';
import type { CustomError } from '@server/utils/customError';

export default async function resetOneTimePasswordHandler(
  {
    email,
    current_password,
    new_password,
  }: { email: string; current_password: string; new_password: string },
  studio_id: string,
  requestId: string
): Promise<typeResult> {
  let data: typeResultData | null = null;
  let error: typeResultError | null = null;

  try {
    console.info(`${requestId} [AUTH] - RESET_OTP handler started`);

    const { isSuccess, data: resetData } = await resetOneTimePasswordRepo(
      { email, current_password, new_password },
      studio_id,
      requestId
    );

    if (isSuccess) {
      data = resetData as typeResultData;
    }

    console.info(
      `${requestId} [AUTH] - RESET_OTP handler completed successfully`
    );
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [AUTH] - RESET_OTP handler error ${customError.message}`
    );
    error = {
      code:
        (customError.errorCode as
          | 'USER_NOT_FOUND'
          | 'INVALID_CURRENT_PASSWORD'
          | 'DB_ERROR'
          | 'VALIDATION_ERROR'
          | 'UNEXPECTED_ERROR'
          | 'STUDIO_NOT_FOUND'
          | 'ACCOUNT_DISABLED') ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
