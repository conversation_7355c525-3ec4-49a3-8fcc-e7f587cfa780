import type {
  typeResult,
  typeResultData,
  typeResultError,
} from '../../../../../shared/src/types/auth/signIn.types';
import authSignIn from '../repository/signIn.repository';
import type { CustomError } from '@server/utils/customError';

export default async function signInAuth(
  { email, password }: { email: string; password: string },
  studio_id: string,
  requestId: string
): Promise<typeResult> {
  let data: typeResultData | null = null;
  let error: typeResultError | null = null;

  try {
    console.info(`${requestId} [AUTH] - SIGNIN handler started`);

    const { isSuccess, data: signInData } = await authSignIn(
      { email, password },
      studio_id,
      requestId
    );

    if (isSuccess) {
      data = signInData as typeResultData;
    }

    console.info(`${requestId} [AUTH] - SIGNIN handler completed successfully`);
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [AUTH] - SIGNIN handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
