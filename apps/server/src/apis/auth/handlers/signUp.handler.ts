import type {
  typeResult,
  typeResultData,
  typeResultError,
} from '../../../../../shared/src/types/auth/signUp.types';
import authSignUp from '../repository/signUp.repository';
import type { CustomError } from '@server/utils/customError';
import type { SignupInput } from '../validators/signUp.validator';

export default async function signUpAuth(
  payload: SignupInput,
  studioId: string,
  requestId: string
): Promise<typeResult> {
  let data: typeResultData | null = null;
  let error: typeResultError | null = null;

  try {
    console.info(`${requestId} [AUTH] - SIGNUP handler started`);

    const { isSuccess } = await authSignUp(
      payload,
      'parent',
      studioId,
      requestId
    );

    if (isSuccess) {
      data = {
        message:
          'A verification email has been sent to your inbox. Please check your email to complete the signup process.',
      };
    }

    console.info(`${requestId} [AUTH] - SIGNUP handler completed successfully`);
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [AUTH] - SIGNUP handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
