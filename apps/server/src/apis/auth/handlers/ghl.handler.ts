import type { CustomError } from '@server/utils/customError';
import ghlRepository from '../repository/ghl.repository';

export interface GhlHandlerInput {
  code: string;
}

export interface GhlHandlerResult {
  data: {
    message: string;
    locationId: string;
  } | null;
  error: {
    code: string;
    message: string;
    statusCode: number;
  } | null;
}

export default async function ghlHandler(
  payload: GhlHandlerInput,
  requestId: string
): Promise<GhlHandlerResult> {
  let data: GhlHandlerResult['data'] = null;
  let error: GhlHandlerResult['error'] = null;

  try {
    console.info(`${requestId} [AUTH] - GHL handler started`);

    const { isSuccess, locationId } = await ghlRepository(payload, requestId);

    if (isSuccess) {
      data = {
        message: 'GHL integration completed successfully',
        locationId,
      };
    }

    console.info(`${requestId} [AUTH] - GHL handler completed successfully`);
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [AUTH] - GHL handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
