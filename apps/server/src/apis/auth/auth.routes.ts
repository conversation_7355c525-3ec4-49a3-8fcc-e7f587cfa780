import { Hono } from 'hono';
import { signInAuthController } from './controllers/signIn.controller';
import { signUpAuthController } from './controllers/signUp.controller';
import { verifyAuthController } from './controllers/verify.controller';
import { resetOneTimePasswordController } from './controllers/resetOneTimePassword.controller';
import {
  validateSignIn,
  validateSigninQuery,
} from './validators/signIn.validator';
import {
  validateSignup,
  validateSignupQuery,
} from './validators/signUp.validator';
import {
  validateResetOneTimePassword,
  validateResetOneTimePasswordQuery,
} from './validators/resetOneTimePassword.validator';
import { authValidator } from '../../middlewares/auth/auth.middleware';
import { ghlController } from './controllers/ghl.controller';

const authRoutes = new Hono()
  .post(
    '/signin',
    validateSigninQuery,
    validateSignIn('json'),
    signInAuthController
  )
  .post(
    '/signup',
    validateSignupQuery,
    validateSignup('json'),
    signUpAuthController
  )
  .post(
    '/reset-password',
    validateResetOneTimePasswordQuery,
    validateResetOneTimePassword('json'),
    resetOneTimePasswordController
  )
  .get('/verify', authValidator, verifyAuthController)
  .get('/callback', ghlController);

export default authRoutes;
