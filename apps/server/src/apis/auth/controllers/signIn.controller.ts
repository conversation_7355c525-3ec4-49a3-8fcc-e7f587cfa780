import type { Context } from 'hono';
import signIn<PERSON>uth<PERSON><PERSON><PERSON> from '../handlers/signIn.handler';
import type { typePayload } from '../../../../../shared/src/types/auth/signIn.types';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const signInAuthController = async (c: Context) => {
  const requestId = c.get('requestId');
  console.log(`${requestId} [AUTH] - SIGNIN - Request received`);

  try {
    const studioId = c.req.query('studio_id');
    const payload = (await c.req.json()) as typePayload;

    const result = await signIn<PERSON><PERSON><PERSON><PERSON><PERSON>(payload, studioId!, requestId);

    if (result.error) {
      console.error(
        `${requestId} [AUTH] - SIGNIN - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: { message: result.error.message } },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(`${requestId} [AUTH] - SIGNIN - Response sent successfully`);
    return c.json({ data: result.data, error: result.error }, 200);
  } catch (err) {
    console.error(`${requestId} [AUTH] - SIGNIN - Controller error: ${err}`);
    return c.json(
      { data: null, error: { message: 'Internal server error' } },
      500
    );
  }
};
