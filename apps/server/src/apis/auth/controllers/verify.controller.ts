import type { Context } from 'hono';
import verify<PERSON><PERSON><PERSON><PERSON><PERSON> from '../handlers/verify.handler';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const verifyAuthController = async (c: Context) => {
  const requestId = c.get('requestId');
  console.log(`${requestId} [AUTH] - VERIFY - Request received`);

  try {
    const payload = c.get('user');
    const result = await verifyAuth<PERSON><PERSON><PERSON>(payload.id as string, requestId);

    if (result.error) {
      console.error(
        `${requestId} [AUTH] - VERIFY - Handler error: ${result.error.message}`
      );
      return c.json(
        { data: null, error: result.error },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(`${requestId} [AUTH] - VERIFY - Response sent successfully`);
    return c.json({ data: result.data, error: result.error }, 200);
  } catch (err) {
    console.error(`${requestId} [AUTH] - VERIFY - Controller error: ${err}`);
    return c.json(
      { data: null, error: { message: 'Internal server error' } },
      500
    );
  }
};
