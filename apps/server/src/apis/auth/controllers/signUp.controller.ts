import type { Context } from 'hono';
import signUpAuth<PERSON>and<PERSON> from '../handlers/signUp.handler';
import type { SignupInput } from '../validators/signUp.validator';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const signUpAuthController = async (c: Context) => {
  const requestId = c.get('requestId');
  const studioId = c.req.query('studio_id')!;
  console.log(`${requestId} [AUTH] - SIGNUP - Request received`);
  try {
    const payload = (await c.req.json()) as SignupInput;
    const {
      email,
      first_name,
      last_name,
      password,
      address,
      country_code,
      date_of_birth,
      emergency_contact_name,
      emergency_contact_phone,
      phone,
      profile_image,
    } = payload;
    const result = await signUpAuthHandler(
      {
        email,
        first_name,
        last_name,
        password,
        address,
        country_code,
        date_of_birth,
        emergency_contact_name,
        emergency_contact_phone,
        phone,
        profile_image,
      },
      studioId,
      requestId
    );
    console.log(
      `${requestId} [AUTH] - SIGNUP - Handler result:`,
      JSON.stringify(result, null, 2)
    );
    console.log(`${requestId} [AUTH] - SIGNUP - Response sent successfully`);
    if (result.error) {
      return c.json(
        { data: null, error: result.error },
        (result.error?.statusCode as ContentfulStatusCode) || 500
      );
    }
    return c.json({ data: result.data, error: result.error }, 201);
  } catch (err) {
    console.error(`${requestId} [AUTH] - SIGNUP - Controller error: ${err}`);
    return c.json(
      { data: null, error: { message: 'Internal server error' } },
      500
    );
  }
};
