import type { Context } from 'hono';
import ghlHandler from '../handlers/ghl.handler';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const ghlController = async (c: Context) => {
  const requestId = c.get('requestId');
  console.log(`${requestId} [AUTH] - GHL - Request received`);

  try {
    const code = c.req.query('code');
    if (!code) {
      console.error(`${requestId} [AUTH] - GHL - Missing code parameter`);
      return c.json(
        { data: null, error: { message: 'Missing code param' } },
        400
      );
    }

    const result = await ghlHandler({ code }, requestId);

    console.log(
      `${requestId} [AUTH] - GHL - Handler result:`,
      JSON.stringify(result, null, 2)
    );

    if (result.error) {
      console.error(
        `${requestId} [AUTH] - GHL - Handler error: ${result.error.message}`
      );
      return c.json(
        { data: null, error: result.error },
        (result.error?.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(`${requestId} [AUTH] - GHL - Response sent successfully`);
    return c.html(`
      <html>
        <body>
          <script>
            window.close();
          </script>
          <p>If the tab did not close, please close it manually.</p>
        </body>
      </html>
    `);
  } catch (err) {
    console.error(`${requestId} [AUTH] - GHL - Controller error: ${err}`);
    return c.json(
      { data: null, error: { message: 'Internal server error' } },
      500
    );
  }
};
