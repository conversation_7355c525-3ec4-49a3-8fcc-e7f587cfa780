import type { Context } from 'hono';
import resetOneTimePasswordHandler from '../handlers/resetOneTimePassword.handler';
import type { typePayload } from '../../../../../shared/src/types/auth/resetOneTimePassword.types';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const resetOneTimePasswordController = async (c: Context) => {
  const requestId = c.get('requestId');
  console.log(`${requestId} [AUTH] - RESET_OTP - Request received`);

  try {
    const studioId = c.req.query('studio_id');
    const payload = (await c.req.json()) as typePayload;

    const result = await resetOneTimePasswordHandler(
      payload,
      studioId!,
      requestId
    );

    if (result.error) {
      console.error(
        `${requestId} [AUTH] - RESET_OTP - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: { message: result.error.message } },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(`${requestId} [AUTH] - RESET_OTP - Response sent successfully`);
    return c.json({ data: result.data, error: result.error }, 200);
  } catch (err) {
    console.error(`${requestId} [AUTH] - RESET_OTP - Controller error: ${err}`);
    return c.json(
      { data: null, error: { message: 'Internal server error' } },
      500
    );
  }
};
