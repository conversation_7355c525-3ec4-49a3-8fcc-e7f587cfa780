import {
  eq,
  and,
  inArray,
  gte,
  lte,
  desc,
  asc,
  count,
  like,
  ilike,
  ne,
} from 'drizzle-orm';
import { db } from '../../../db';
import * as schema from '../../../db/schema';
import type {
  CreateArenaInput,
  UpdateArenaInput,
  GetArenasQuery,
  GetArenaScheduleQuery,
} from '../validators/arena.validator';

export class ArenaRepository {
  // Create a new arena
  static async createArena(arenaData: CreateArenaInput) {
    const [arena] = await db
      .insert(schema.arenasTable)
      .values({
        name: arenaData.name,
        location: arenaData.location || null,
        description: arenaData.description || null,
        capacity: arenaData.capacity,
        equipment: arenaData.equipment || null,
        is_active: arenaData.is_active ? 1 : 0,
      })
      .returning();

    return arena;
  }

  // Get all arenas with filtering
  static async getArenas(filters: GetArenasQuery) {
    const { is_active, search, page = 1, limit = 20 } = filters;

    // Build where conditions
    const whereConditions = [];

    if (is_active !== undefined) {
      whereConditions.push(eq(schema.arenasTable.is_active, is_active ? 1 : 0));
    }

    if (search) {
      whereConditions.push(ilike(schema.arenasTable.name, `%${search}%`));
    }

    const whereClause =
      whereConditions.length > 0 ? and(...whereConditions) : undefined;

    // Get total count
    const [{ total }]: any[] = await db
      .select({ total: count() })
      .from(schema.arenasTable)
      .where(whereClause);

    // Get arenas with lesson count
    const offset = (page - 1) * limit;
    const arenas = await db.query.arenasTable.findMany({
      where: whereClause,
      with: {
        lessons: {
          columns: { id: true, status: true },
        },
      },
      orderBy: [asc(schema.arenasTable.name)],
      limit,
      offset,
    });

    return {
      arenas: arenas.map((arena) => ({
        ...arena,
        is_active: Boolean(arena.is_active),
        lesson_count: {
          total: (arena as any).lessons?.length || 0,
          scheduled:
            (arena as any).lessons?.filter((l: any) => l.status === 'scheduled')
              .length || 0,
          completed:
            (arena as any).lessons?.filter((l: any) => l.status === 'completed')
              .length || 0,
        },
      })),
      total: Number(total),
      page,
      limit,
      total_pages: Math.ceil(Number(total) / limit),
    };
  }

  // Get arena by ID
  static async getArenaById(id: number) {
    const arena = await db.query.arenasTable.findFirst({
      where: eq(schema.arenasTable.id, id),
      with: {
        lessons: {
          columns: {
            id: true,
            title: true,
            status: true,
            date: true,
            start_time: true,
            end_time: true,
          },
          orderBy: [desc(schema.lessonsTable.date)],
          limit: 10, // Recent lessons
        },
      },
    });

    if (!arena) {
      return null;
    }

    return {
      ...arena,
      is_active: Boolean(arena.is_active),
      recent_lessons: (arena as any).lessons || [],
      lesson_stats: {
        total: (arena as any).lessons?.length || 0,
        scheduled:
          (arena as any).lessons?.filter((l: any) => l.status === 'scheduled')
            .length || 0,
        completed:
          (arena as any).lessons?.filter((l: any) => l.status === 'completed')
            .length || 0,
        cancelled:
          (arena as any).lessons?.filter((l: any) => l.status === 'cancelled')
            .length || 0,
      },
    };
  }

  // Update arena
  static async updateArena(id: number, updateData: UpdateArenaInput) {
    const updateFields: any = {};

    if (updateData.name !== undefined) updateFields.name = updateData.name;
    if (updateData.location !== undefined)
      updateFields.location = updateData.location;
    if (updateData.description !== undefined)
      updateFields.description = updateData.description;
    if (updateData.capacity !== undefined)
      updateFields.capacity = updateData.capacity;
    if (updateData.equipment !== undefined)
      updateFields.equipment = updateData.equipment;
    if (updateData.is_active !== undefined)
      updateFields.is_active = updateData.is_active ? 1 : 0;

    const [updatedArena] = await db
      .update(schema.arenasTable)
      .set(updateFields)
      .where(eq(schema.arenasTable.id, id))
      .returning();

    if (!updatedArena) {
      return null;
    }

    return {
      ...updatedArena,
      is_active: Boolean(updatedArena.is_active),
    };
  }

  // Delete arena (soft delete)
  static async deleteArena(id: number) {
    // Check if arena has any scheduled lessons
    const scheduledLessons = await db.query.lessonsTable.findMany({
      where: and(
        eq(schema.lessonsTable.arena_id, id),
        eq(schema.lessonsTable.status, 'scheduled')
      ),
      columns: { id: true },
    });

    if (scheduledLessons.length > 0) {
      throw new Error('Cannot delete arena with scheduled lessons');
    }

    const [deletedArena] = await db
      .update(schema.arenasTable)
      .set({ deleted_at: new Date() })
      .where(eq(schema.arenasTable.id, id))
      .returning();

    return deletedArena;
  }

  // Get arena schedule
  static async getArenaSchedule(id: number, filters: GetArenaScheduleQuery) {
    const { date_from, date_to, status } = filters;

    const whereConditions = [eq(schema.lessonsTable.arena_id, id)];

    if (status) {
      whereConditions.push(eq(schema.lessonsTable.status, status));
    }

    if (date_from) {
      whereConditions.push(gte(schema.lessonsTable.date, new Date(date_from)));
    }

    if (date_to) {
      whereConditions.push(lte(schema.lessonsTable.date, new Date(date_to)));
    }

    const whereClause = and(...whereConditions);

    const lessons = await db.query.lessonsTable.findMany({
      where: whereClause,
      with: {
        instructors: {
          with: {
            instructor: {
              columns: {
                id: true,
                first_name: true,
                last_name: true,
              },
            },
          },
        },
        enrollments: {
          columns: {
            id: true,
            student_id: true,
            attendance_status: true,
          },
        },
      },
      orderBy: [
        asc(schema.lessonsTable.date),
        asc(schema.lessonsTable.start_time),
      ],
    });

    return lessons.map((lesson) => ({
      ...lesson,
      instructor_names:
        (lesson as any).instructors
          ?.map(
            (i: any) => `${i.instructor.first_name} ${i.instructor.last_name}`
          )
          .join(', ') || 'No instructor assigned',
      enrollment_count: (lesson as any).enrollments?.length || 0,
    }));
  }

  // Check if arena exists and is active
  static async validateArena(arenaId: number) {
    const arena = await db.query.arenasTable.findFirst({
      where: and(
        eq(schema.arenasTable.id, arenaId),
        eq(schema.arenasTable.is_active, 1)
      ),
    });

    return arena;
  }

  // Get arena availability for a time slot
  static async checkArenaAvailability(
    arenaId: number,
    startTime: Date,
    endTime: Date,
    excludeLessonId?: number
  ) {
    const whereConditions = [
      eq(schema.lessonsTable.arena_id, arenaId),
      eq(schema.lessonsTable.status, 'scheduled'),
      and(
        lte(schema.lessonsTable.start_time, endTime),
        gte(schema.lessonsTable.end_time, startTime)
      ),
    ];

    if (excludeLessonId) {
      whereConditions.push(ne(schema.lessonsTable.id, excludeLessonId));
    }

    const conflictingLessons = await db.query.lessonsTable.findMany({
      where: and(...whereConditions),
      columns: {
        id: true,
        title: true,
        start_time: true,
        end_time: true,
      },
    });

    return {
      available: conflictingLessons.length === 0,
      conflicts: conflictingLessons,
    };
  }
}
