import { <PERSON>o } from 'hono';
import { authValidator } from '../../middlewares/auth/auth.middleware';
import { adminMiddleware } from '../../middlewares/auth/admin.middleware';
import createArenaController from './controllers/createArena.controller';
import getArenasController from './controllers/getArenas.controller';
import getArenaByIdController from './controllers/getArenaById.controller';
import updateArenaController from './controllers/updateArena.controller';
import deleteArenaController from './controllers/deleteArena.controller';
import getArenaScheduleController from './controllers/getArenaSchedule.controller';

const arenaRoutes = new Hono()
  // Arena CRUD - Admin only for create/update/delete
  .post('/', authValidator, adminMiddleware, createArenaController)
  .get('/', authValidator, getArenasController)
  .get('/:id', authValidator, getArenaByIdController)
  .put('/:id', authValidator, adminMiddleware, updateArenaController)
  .delete('/:id', authValidator, adminMiddleware, deleteArenaController)

  // Arena scheduling - Available to instructors and admins
  .get('/:id/schedule', authValidator, getArenaScheduleController);

export default arenaRoutes;
