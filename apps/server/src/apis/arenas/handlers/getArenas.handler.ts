import type { GetArenasQuery } from '../validators/arena.validator';
import { ArenaRepository } from '../repository/arena.repository';

interface GetArenasResult {
  data: {
    arenas: any[];
    total: number;
    page: number;
    limit: number;
    total_pages: number;
  } | null;
  error: {
    code: string;
    message: string;
    statusCode: number;
  } | null;
}

export default async function getArenasHandler(
  query: GetArenasQuery,
  userId: string,
  userRole: string,
  requestId: string
): Promise<GetArenasResult> {
  try {
    console.log(
      `${requestId} [ARENAS] - GET_LIST - Handler started with filters:`,
      query
    );

    // Get arenas with filtering
    const result = await ArenaRepository.getArenas(query);

    console.log(
      `${requestId} [ARENAS] - GET_LIST - Found ${result.arenas.length} arenas`
    );

    return {
      data: result,
      error: null,
    };
  } catch (error) {
    console.error(`${requestId} [ARENAS] - GET_LIST - Handler error:`, error);

    return {
      data: null,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to get arenas',
        statusCode: 500,
      },
    };
  }
}
