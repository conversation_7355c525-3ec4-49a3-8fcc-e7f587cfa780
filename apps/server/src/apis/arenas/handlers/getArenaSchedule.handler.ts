import { ArenaRepository } from '../repository/arena.repository';
import type { GetArenaScheduleQuery } from '../validators/arena.validator';

interface GetArenaScheduleResult {
  data: {
    arena_id: number;
    lessons: any[];
    filters_applied: GetArenaScheduleQuery;
  } | null;
  error: { code: string; message: string; statusCode: number } | null;
}

export default async function getArenaScheduleHandler(
  arenaId: number,
  query: GetArenaScheduleQuery,
  userId: string,
  userRole: string,
  requestId: string
): Promise<GetArenaScheduleResult> {
  try {
    console.log(
      `${requestId} [ARENAS] - GET_SCHEDULE - <PERSON><PERSON> started for arena ${arenaId}`
    );

    // Check if arena exists
    const arena = await ArenaRepository.validateArena(arenaId);
    if (!arena) {
      return {
        data: null,
        error: {
          code: 'ARENA_NOT_FOUND',
          message: 'Arena not found or inactive',
          statusCode: 404,
        },
      };
    }

    const lessons = await ArenaRepository.getArenaSchedule(arenaId, query);

    console.log(
      `${requestId} [ARENAS] - GET_SCHEDULE - Found ${lessons.length} lessons`
    );

    return {
      data: {
        arena_id: arenaId,
        lessons,
        filters_applied: query,
      },
      error: null,
    };
  } catch (error) {
    console.error(
      `${requestId} [ARENAS] - GET_SCHEDULE - Handler error:`,
      error
    );

    return {
      data: null,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to get arena schedule',
        statusCode: 500,
      },
    };
  }
}
