import { ArenaRepository } from '../repository/arena.repository';
import type { UpdateArenaInput } from '../validators/arena.validator';

interface UpdateArenaResult {
  data: { arena: any } | null;
  error: { code: string; message: string; statusCode: number } | null;
}

export default async function updateArenaHandler(
  id: number,
  payload: UpdateArenaInput,
  userId: string,
  userRole: string,
  requestId: string
): Promise<UpdateArenaResult> {
  try {
    console.log(
      `${requestId} [ARENAS] - UPDATE - Handler started for arena ${id}`
    );

    const updatedArena = await ArenaRepository.updateArena(id, payload);

    if (!updatedArena) {
      return {
        data: null,
        error: {
          code: 'ARENA_NOT_FOUND',
          message: 'Arena not found',
          statusCode: 404,
        },
      };
    }

    console.log(
      `${requestId} [ARENAS] - UPDATE - Arena updated: ${updatedArena.name}`
    );

    return {
      data: { arena: updatedArena },
      error: null,
    };
  } catch (error) {
    console.error(`${requestId} [ARENAS] - UPDATE - Handler error:`, error);

    return {
      data: null,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to update arena',
        statusCode: 500,
      },
    };
  }
}
