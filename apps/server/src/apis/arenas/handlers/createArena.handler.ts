import type { CreateArenaInput } from '../validators/arena.validator';
import { ArenaRepository } from '../repository/arena.repository';

interface CreateArenaResult {
  data: {
    arena: any;
  } | null;
  error: {
    code: string;
    message: string;
    statusCode: number;
  } | null;
}

export default async function createArenaHandler(
  payload: CreateArenaInput,
  userId: string,
  userRole: string,
  requestId: string
): Promise<CreateArenaResult> {
  try {
    console.log(
      `${requestId} [ARENAS] - CREATE - Handler started for arena: ${payload.name}`
    );

    // Only admins can create arenas (handled by middleware, but double-check)
    if (
      userRole !== 'admin' &&
      userRole !== 'super-admin' &&
      userRole !== 'owner'
    ) {
      return {
        data: null,
        error: {
          code: 'ACCESS_DENIED',
          message: 'Only administrators can create arenas',
          statusCode: 403,
        },
      };
    }

    // Create arena
    const arena = await ArenaRepository.createArena(payload);

    console.log(
      `${requestId} [ARENAS] - CREATE - Arena created with ID: ${arena.id}`
    );

    return {
      data: {
        arena: {
          ...arena,
          is_active: <PERSON><PERSON><PERSON>(arena.is_active),
        },
      },
      error: null,
    };
  } catch (error) {
    console.error(`${requestId} [ARENAS] - CREATE - Handler error:`, error);

    return {
      data: null,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to create arena',
        statusCode: 500,
      },
    };
  }
}
