import { ArenaRepository } from '../repository/arena.repository';

interface GetArenaByIdResult {
  data: { arena: any } | null;
  error: { code: string; message: string; statusCode: number } | null;
}

export default async function getArenaByIdHandler(
  id: number,
  userId: string,
  userRole: string,
  requestId: string
): Promise<GetArenaByIdResult> {
  try {
    console.log(
      `${requestId} [ARENAS] - GET_BY_ID - Hand<PERSON> started for arena ${id}`
    );

    const arena = await ArenaRepository.getArenaById(id);

    if (!arena) {
      return {
        data: null,
        error: {
          code: 'ARENA_NOT_FOUND',
          message: 'Arena not found',
          statusCode: 404,
        },
      };
    }

    console.log(
      `${requestId} [ARENAS] - GET_BY_ID - Arena found: ${arena.name}`
    );

    return {
      data: { arena },
      error: null,
    };
  } catch (error) {
    console.error(`${requestId} [ARENAS] - GET_BY_ID - Handler error:`, error);

    return {
      data: null,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to get arena',
        statusCode: 500,
      },
    };
  }
}
