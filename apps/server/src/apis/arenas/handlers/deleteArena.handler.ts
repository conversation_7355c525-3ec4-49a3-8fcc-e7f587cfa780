import { ArenaRepository } from '../repository/arena.repository';

interface DeleteArenaResult {
  data: { message: string } | null;
  error: { code: string; message: string; statusCode: number } | null;
}

export default async function deleteArenaHandler(
  id: number,
  userId: string,
  userRole: string,
  requestId: string
): Promise<DeleteArenaResult> {
  try {
    console.log(
      `${requestId} [ARENAS] - DELETE - Handler started for arena ${id}`
    );

    await ArenaRepository.deleteArena(id);

    console.log(`${requestId} [ARENAS] - DELETE - Arena deleted successfully`);

    return {
      data: { message: 'Arena deleted successfully' },
      error: null,
    };
  } catch (error: any) {
    console.error(`${requestId} [ARENAS] - DELETE - <PERSON><PERSON> error:`, error);

    if (error.message === 'Cannot delete arena with scheduled lessons') {
      return {
        data: null,
        error: {
          code: 'ARENA_HAS_SCHEDULED_LESSONS',
          message: 'Cannot delete arena with scheduled lessons',
          statusCode: 400,
        },
      };
    }

    return {
      data: null,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to delete arena',
        statusCode: 500,
      },
    };
  }
}
