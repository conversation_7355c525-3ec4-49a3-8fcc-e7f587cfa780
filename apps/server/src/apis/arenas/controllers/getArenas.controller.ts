import { Context } from 'hono';
import { authValidator } from '../../../middleware/auth.middleware';
import getArenasHandler from '../handlers/getArenas.handler';
import { getArenasQuerySchema } from '../validators/arena.validator';
import { CustomError } from '../../../utils/error/customError';

export default async function getArenasController(c: Context) {
  try {
    // Validate authentication
    const auth = await authValidator(c);
    if (!auth.valid) {
      return c.json(auth.response, auth.response.error?.statusCode || 401);
    }

    const { userId, userRole } = auth;

    // Parse and validate query parameters
    const queryParams = {
      is_active: c.req.query('is_active'),
      search: c.req.query('search'),
      page: c.req.query('page') || '1',
      limit: c.req.query('limit') || '20',
    };

    const validatedQuery = getArenasQuerySchema.parse(queryParams);

    // Get request ID for logging
    const requestId = c.get('requestId');

    // Call handler
    const result = await getArenasHandler(
      validatedQuery,
      userId!,
      userRole!,
      requestId
    );

    if (result.error) {
      return c.json(result, result.error.statusCode);
    }

    return c.json(result, 200);
  } catch (error: any) {
    console.error('GET ARENAS Controller Error:', error);

    if (error.name === 'ZodError') {
      const customError = new CustomError(
        'VALIDATION_ERROR',
        'Invalid query parameters',
        400
      );
      customError.details = error.errors;

      return c.json(
        {
          data: null,
          error: {
            code: customError.code,
            message: customError.message,
            statusCode: customError.statusCode,
            details: customError.details,
          },
        },
        400
      );
    }

    return c.json(
      {
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get arenas',
          statusCode: 500,
        },
      },
      500
    );
  }
}
