import { Context } from 'hono';
import { authValidator } from '../../../middleware/auth.middleware';
import deleteArenaHandler from '../handlers/deleteArena.handler';
import { arenaParamsSchema } from '../validators/arena.validator';

export default async function deleteArenaController(c: Context) {
  try {
    const auth = await authValidator(c);
    if (!auth.valid) {
      return c.json(auth.response, auth.response.error?.statusCode || 401);
    }

    const { userId, userRole } = auth;
    const { id } = arenaParamsSchema.parse({ id: c.req.param('id') });
    const requestId = c.get('requestId');

    const result = await deleteArenaHandler(id, userId!, userRole!, requestId);

    if (result.error) {
      return c.json(result, result.error.statusCode);
    }

    return c.json(result, 200);
  } catch (error: any) {
    console.error('DELETE ARENA Controller Error:', error);

    return c.json(
      {
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to delete arena',
          statusCode: 500,
        },
      },
      500
    );
  }
}
