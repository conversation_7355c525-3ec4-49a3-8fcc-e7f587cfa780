import { Context } from 'hono';
import { authValidator } from '../../../middleware/auth.middleware';
import updateArenaHandler from '../handlers/updateArena.handler';
import {
  updateArenaSchema,
  arenaParamsSchema,
} from '../validators/arena.validator';

export default async function updateArenaController(c: Context) {
  try {
    const auth = await authValidator(c);
    if (!auth.valid) {
      return c.json(auth.response, auth.response.error?.statusCode || 401);
    }

    const { userId, userRole } = auth;
    const { id } = arenaParamsSchema.parse({ id: c.req.param('id') });
    const body = await c.req.json();
    const validatedData = updateArenaSchema.parse(body);
    const requestId = c.get('requestId');

    const result = await updateArenaHandler(
      id,
      validatedData,
      userId!,
      userRole!,
      requestId
    );

    if (result.error) {
      return c.json(result, result.error.statusCode);
    }

    return c.json(result, 200);
  } catch (error: any) {
    console.error('UPDATE ARENA Controller Error:', error);

    return c.json(
      {
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to update arena',
          statusCode: 500,
        },
      },
      500
    );
  }
}
