import { Context } from 'hono';
import { authValidator } from '../../../middleware/auth.middleware';
import getArenaByIdHandler from '../handlers/getArenaById.handler';
import { arenaParamsSchema } from '../validators/arena.validator';

export default async function getArenaByIdController(c: Context) {
  try {
    const auth = await authValidator(c);
    if (!auth.valid) {
      return c.json(auth.response, auth.response.error?.statusCode || 401);
    }

    const { userId, userRole } = auth;
    const { id } = arenaParamsSchema.parse({ id: c.req.param('id') });
    const requestId = c.get('requestId');

    const result = await getArenaByIdHandler(id, userId!, userRole!, requestId);

    if (result.error) {
      return c.json(result, result.error.statusCode);
    }

    return c.json(result, 200);
  } catch (error: any) {
    console.error('GET ARENA BY ID Controller Error:', error);

    return c.json(
      {
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get arena',
          statusCode: 500,
        },
      },
      500
    );
  }
}
