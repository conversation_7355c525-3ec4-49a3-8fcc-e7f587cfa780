import { Context } from 'hono';
import { authValidator } from '../../../middleware/auth.middleware';
import getArenaScheduleHandler from '../handlers/getArenaSchedule.handler';
import {
  arenaParamsSchema,
  getArenaScheduleQuerySchema,
} from '../validators/arena.validator';

export default async function getArenaScheduleController(c: Context) {
  try {
    const auth = await authValidator(c);
    if (!auth.valid) {
      return c.json(auth.response, auth.response.error?.statusCode || 401);
    }

    const { userId, userRole } = auth;
    const { id } = arenaParamsSchema.parse({ id: c.req.param('id') });

    const queryParams = {
      date_from: c.req.query('date_from'),
      date_to: c.req.query('date_to'),
      status: c.req.query('status'),
    };

    const validatedQuery = getArenaScheduleQuerySchema.parse(queryParams);
    const requestId = c.get('requestId');

    const result = await getArenaScheduleHandler(
      id,
      validatedQuery,
      userId!,
      userRole!,
      requestId
    );

    if (result.error) {
      return c.json(result, result.error.statusCode);
    }

    return c.json(result, 200);
  } catch (error: any) {
    console.error('GET ARENA SCHEDULE Controller Error:', error);

    return c.json(
      {
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get arena schedule',
          statusCode: 500,
        },
      },
      500
    );
  }
}
