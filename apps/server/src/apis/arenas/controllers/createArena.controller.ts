import { Context } from 'hono';
import { authValidator } from '../../../middleware/auth.middleware';
import createArenaHandler from '../handlers/createArena.handler';
import { createArenaSchema } from '../validators/arena.validator';
import { CustomError } from '../../../utils/error/customError';

export default async function createArenaController(c: Context) {
  try {
    // Validate authentication
    const auth = await authValidator(c);
    if (!auth.valid) {
      return c.json(auth.response, auth.response.error?.statusCode || 401);
    }

    const { userId, userRole } = auth;

    // Parse and validate request body
    const body = await c.req.json();
    const validatedData = createArenaSchema.parse(body);

    // Get request ID for logging
    const requestId = c.get('requestId');

    // Call handler
    const result = await create<PERSON>renaHandler(
      validatedData,
      userId!,
      userRole!,
      requestId
    );

    if (result.error) {
      return c.json(result, result.error.statusCode);
    }

    return c.json(result, 201);
  } catch (error: any) {
    console.error('CREATE ARENA Controller Error:', error);

    if (error.name === 'ZodError') {
      const customError = new CustomError(
        'VALIDATION_ERROR',
        'Invalid arena data',
        400
      );
      customError.details = error.errors;

      return c.json(
        {
          data: null,
          error: {
            code: customError.code,
            message: customError.message,
            statusCode: customError.statusCode,
            details: customError.details,
          },
        },
        400
      );
    }

    return c.json(
      {
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to create arena',
          statusCode: 500,
        },
      },
      500
    );
  }
}
