import { z } from 'zod';

export const createArenaSchema = z.object({
  name: z
    .string()
    .min(1, 'Arena name is required')
    .max(255, 'Arena name too long'),
  location: z.string().max(255, 'Location too long').optional(),
  description: z.string().optional(),
  capacity: z
    .number()
    .min(1, 'Capacity must be at least 1')
    .max(50, 'Capacity too large')
    .default(10),
  equipment: z.string().optional(),
  is_active: z.boolean().default(true),
});

export const updateArenaSchema = z.object({
  name: z
    .string()
    .min(1, 'Arena name is required')
    .max(255, 'Arena name too long')
    .optional(),
  location: z.string().max(255, 'Location too long').optional(),
  description: z.string().optional(),
  capacity: z
    .number()
    .min(1, 'Capacity must be at least 1')
    .max(50, 'Capacity too large')
    .optional(),
  equipment: z.string().optional(),
  is_active: z.boolean().optional(),
});

export const arenaParamsSchema = z.object({
  id: z.string().transform(Number),
});

export const getArenasQuerySchema = z.object({
  is_active: z
    .string()
    .transform((val) => val === 'true')
    .optional(),
  search: z.string().optional(),
  page: z.string().transform(Number).default('1'),
  limit: z.string().transform(Number).default('20'),
});

export const getArenaScheduleQuerySchema = z.object({
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  status: z
    .enum(['scheduled', 'completed', 'cancelled', 'in_progress'])
    .optional(),
});

export type CreateArenaInput = z.infer<typeof createArenaSchema>;
export type UpdateArenaInput = z.infer<typeof updateArenaSchema>;
export type ArenaParams = z.infer<typeof arenaParamsSchema>;
export type GetArenasQuery = z.infer<typeof getArenasQuerySchema>;
export type GetArenaScheduleQuery = z.infer<typeof getArenaScheduleQuerySchema>;
