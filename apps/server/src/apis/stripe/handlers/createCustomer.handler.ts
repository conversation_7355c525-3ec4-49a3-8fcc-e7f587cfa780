import type { CustomError } from '@server/utils/customError';
import type { CreateCustomerInput } from '../validators/createCustomer.validator';
import createCustomerRepository from '../repository/createCustomer.repository';

type typeResultData = {
  customerId: string;
  message: string;
};

type typeResultError = {
  code: string;
  message: string;
  statusCode: number;
};

type typeResult = {
  data: null | typeResultData;
  error: null | typeResultError;
};

export default async function createCustomerHandler(
  payload: CreateCustomerInput,
  studioId: string,
  requestId: string
): Promise<typeResult> {
  let data: typeResultData | null = null;
  let error: typeResultError | null = null;

  try {
    console.info(`${requestId} [STRIPE] - CREATE_CUSTOMER handler started`);

    const { isSuccess, customerId } = await createCustomerRepository(
      payload,
      studioId,
      requestId
    );

    if (isSuccess) {
      data = {
        customerId,
        message: 'Stripe customer created successfully',
      };
    }

    console.info(
      `${requestId} [STRIPE] - CREATE_CUSTOMER handler completed successfully`
    );
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [STRIPE] - CREATE_CUSTOMER handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
