import { Hono } from 'hono';
import { createCustomerController } from './controllers/createCustomer.controller';
import { validateCreateCustomer } from './validators/createCustomer.validator';
import { authValidator } from '../../middlewares/auth/auth.middleware';

const stripeRoutes = new Hono().post(
  '/customers',
  authValidator,
  validateCreateCustomer('json'),
  createCustomerController
);

export default stripeRoutes;
