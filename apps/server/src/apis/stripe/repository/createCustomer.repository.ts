import { CustomError } from '@server/utils/customError';
import { stripe } from './stripe.client';
import type { CreateCustomerInput } from '../validators/createCustomer.validator';

interface CreateCustomerRepositoryResult {
  isSuccess: boolean;
  customerId: string;
}

export default async function createCustomerRepository(
  payload: CreateCustomerInput,
  studioId: string,
  requestId: string
): Promise<CreateCustomerRepositoryResult> {
  try {
    console.info(
      `${requestId} [STRIPE] - CREATE_CUSTOMER repository operation started`
    );

    const customer = await stripe.customers.create({
      email: payload.email,
      name: payload.name,
      phone: payload.phone,
      metadata: {
        ...payload.metadata,
        studio_id: studioId,
        created_via: 'horse_riding_app',
        request_id: requestId,
      },
    });

    console.info(
      `${requestId} [STRIPE] - CREATE_CUSTOMER repository operation completed`
    );
    return { isSuccess: true, customerId: customer.id };
  } catch (error) {
    console.error(
      `${requestId} [STRIPE] - CREATE_CUSTOMER repository operation failed:`,
      error
    );

    if (error instanceof Error) {
      throw new CustomError(
        'STRIPE_ERROR',
        `Failed to create Stripe customer: ${error.message}`,
        500
      );
    }

    throw new CustomError(
      'STRIPE_ERROR',
      'Failed to create Stripe customer: Unknown error',
      500
    );
  }
}
