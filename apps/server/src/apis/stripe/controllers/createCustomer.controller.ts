import type { Context } from 'hono';
import createCustomerHandler from '../handlers/createCustomer.handler';
import type { CreateCustomerInput } from '../validators/createCustomer.validator';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const createCustomerController = async (c: Context) => {
  const requestId = c.get('requestId');
  const user = c.get('user');

  console.log(`${requestId} [STRIPE] - CREATE_CUSTOMER - Request received`);

  try {
    // Get studio_id from authenticated user metadata
    const studioId = user?.user_metadata?.studio_id;

    if (!studioId) {
      console.error(
        `${requestId} [STRIPE] - CREATE_CUSTOMER - Missing studio ID`
      );
      return c.json(
        { data: null, error: { message: 'Studio ID not found' } },
        400
      );
    }

    const payload = (await c.req.json()) as CreateCustomerInput;

    const result = await createCustomerHandler(payload, studioId, requestId);

    if (result.error) {
      console.error(
        `${requestId} [STRIPE] - CREATE_CUSTOMER - Hand<PERSON> error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: { message: result.error.message } },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(
      `${requestId} [STRIPE] - CREATE_CUSTOMER - Response sent successfully`
    );
    return c.json({ data: result.data, error: result.error }, 201);
  } catch (err) {
    console.error(
      `${requestId} [STRIPE] - CREATE_CUSTOMER - Controller error: ${err}`
    );
    return c.json(
      { data: null, error: { message: 'Internal server error' } },
      500
    );
  }
};
