import { CustomError } from '@server/utils/customError';
import { stripe } from '../repository/stripe.client';

export interface CreateStripeCustomerData {
  email: string;
  name: string;
  phone?: string;
  metadata?: Record<string, string>;
}

export class StripeService {
  /**
   * Create a new Stripe customer
   */
  static async createCustomer(
    userData: CreateStripeCustomerData,
    requestId: string
  ): Promise<string> {
    try {
      console.info(
        `${requestId} [STRIPE] - Creating customer for ${userData.email}`
      );

      const customer = await stripe.customers.create({
        email: userData.email,
        name: userData.name,
        phone: userData.phone,
        metadata: {
          ...userData.metadata,
          created_via: 'horse_riding_app',
          request_id: requestId,
        },
      });

      console.info(
        `${requestId} [STRIPE] - Customer created successfully: ${customer.id}`
      );
      return customer.id;
    } catch (error) {
      console.error(
        `${requestId} [STRIPE] - Failed to create customer:`,
        error
      );

      if (error instanceof Error) {
        throw new CustomError(
          'STRIPE_ERROR',
          `Failed to create Stripe customer: ${error.message}`,
          500
        );
      }

      throw new CustomError(
        'STRIPE_ERROR',
        'Failed to create Stripe customer: Unknown error',
        500
      );
    }
  }

  /**
   * Delete a Stripe customer (for rollback purposes)
   */
  static async deleteCustomer(
    stripeCustomerId: string,
    requestId: string
  ): Promise<void> {
    try {
      console.info(
        `${requestId} [STRIPE] - Deleting customer: ${stripeCustomerId}`
      );

      await stripe.customers.del(stripeCustomerId);

      console.info(
        `${requestId} [STRIPE] - Customer deleted successfully: ${stripeCustomerId}`
      );
    } catch (error) {
      console.error(
        `${requestId} [STRIPE] - Failed to delete customer:`,
        error
      );

      // Don't throw error for deletion failures during rollback
      // Just log the error as it's not critical for the main flow
      console.warn(
        `${requestId} [STRIPE] - Customer deletion failed, but continuing with rollback`
      );
    }
  }
}
