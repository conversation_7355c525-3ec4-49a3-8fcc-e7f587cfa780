import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';

export type CreateCustomerInput = z.infer<typeof createCustomerSchema>;

export const createCustomerSchema = z.object({
  email: z.string().email().max(100),
  name: z.string().min(1).max(100),
  phone: z.string().min(7).max(15).optional(),
  metadata: z.record(z.string()).optional(),
});

export const validateCreateCustomer = (
  target: 'json' | 'form' | 'query' | 'param' | 'header' | 'cookie' = 'json'
) => zValidator(target, createCustomerSchema);
