import { Hono } from 'hono';
import { getArenasController } from './controllers/getArenas.controller';
import { getInstructorsController } from './controllers/getInstructors.controller';
import { getCurriculumController } from './controllers/getCurriculum.controller';
import { authValidator } from '../../middlewares/auth/auth.middleware';

const arenasRoutes = new Hono().get('/', authValidator, getArenasController);

const instructorsRoutes = new Hono().get(
  '/',
  authValidator,
  getInstructorsController
);

const curriculumRoutes = new Hono().get(
  '/',
  authValidator,
  getCurriculumController
);

export { arenasRoutes, instructorsRoutes, curriculumRoutes };
