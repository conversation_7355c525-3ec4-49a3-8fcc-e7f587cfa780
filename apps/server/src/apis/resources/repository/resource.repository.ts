import { eq } from 'drizzle-orm';
import { db } from '../../../db';
import * as schema from '../../../db/schema';

export class ResourceRepository {
  // Get all active arenas
  static async getArenas() {
    const arenas = await db.query.arenasTable.findMany({
      where: eq(schema.arenasTable.is_active, 1),
      orderBy: schema.arenasTable.name,
    });

    return arenas.map((arena: any) => ({
      ...arena,
      equipment: arena.equipment ? JSON.parse(arena.equipment) : [],
      is_active: <PERSON><PERSON><PERSON>(arena.is_active),
    }));
  }

  // Get all instructors (users with instructor role)
  static async getInstructors() {
    // For now, we'll get all users and filter by role in the metadata
    // In a real implementation, you might want to add a role column to the users table
    const users = await db.query.usersTable.findMany({
      orderBy: schema.usersTable.first_name,
    });

    // Filter for instructors and admins (who can also instruct)
    const instructors = users.filter((user: any) => {
      // This is a simplified approach - in reality you'd want proper role management
      return (
        user.name?.toLowerCase().includes('instructor') ||
        user.email?.includes('instructor') ||
        user.email?.includes('admin')
      );
    });

    return instructors.map((instructor: any) => ({
      id: instructor.id,
      name: instructor.name || 'Unknown',
      email: instructor.email,
      phone: null, // Not in current schema
      avatar: null, // Not in current schema
      specialties: [], // Not in current schema
      experience_years: null, // Not in current schema
      is_active: true,
    }));
  }

  // Get all active curriculum items
  static async getCurriculum() {
    const curriculum = await db.query.curriculumTable.findMany({
      where: eq(schema.curriculumTable.is_active, 1),
      orderBy: [
        schema.curriculumTable.skill_level,
        schema.curriculumTable.name,
      ],
    });

    return curriculum.map((item: any) => ({
      ...item,
      prerequisites: item.prerequisites ? JSON.parse(item.prerequisites) : [],
      is_active: Boolean(item.is_active),
    }));
  }

  // Get arena by ID
  static async getArenaById(id: number) {
    const arena = await db.query.arenasTable.findFirst({
      where: eq(schema.arenasTable.id, id),
    });

    if (!arena) return null;

    return {
      ...arena,
      equipment: arena.equipment ? JSON.parse(arena.equipment) : [],
      is_active: Boolean(arena.is_active),
    };
  }

  // Get instructor by ID
  static async getInstructorById(id: string) {
    const user = await db.query.usersTable.findFirst({
      where: eq(schema.usersTable.id, id),
    });

    if (!user) return null;

    return {
      id: user.id,
      name: `${user.first_name} ${user.last_name}`.trim() || 'Unknown',
      email: user.email,
      phone: user.phone,
      avatar: user.profile_image,
      specialties: [],
      experience_years: null,
      is_active: true,
    };
  }

  // Get curriculum item by ID
  static async getCurriculumById(id: number) {
    const item = await db.query.curriculumTable.findFirst({
      where: eq(schema.curriculumTable.id, id),
    });

    if (!item) return null;

    return {
      ...item,
      prerequisites: item.prerequisites ? JSON.parse(item.prerequisites) : [],
      is_active: Boolean(item.is_active),
    };
  }
}
