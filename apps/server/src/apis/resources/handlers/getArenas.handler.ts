import { ResourceRepository } from '../repository/resource.repository';
import type { Lessons } from 'shared';

export default async function getArenasHandler(
  requestId: string
): Promise<Lessons.GetArenasResult> {
  try {
    console.log(`${requestId} [RESOURCES] - GET_ARENAS - Handler started`);

    const arenas = await ResourceRepository.getArenas();

    console.log(
      `${requestId} [RESOURCES] - GET_ARENAS - Found ${arenas.length} arenas`
    );

    return {
      data: {
        arenas: arenas as any,
        total: arenas.length,
      },
      error: null,
    };
  } catch (error) {
    console.error(
      `${requestId} [RESOURCES] - GET_ARENAS - Handler error:`,
      error
    );

    return {
      data: null,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to retrieve arenas',
        statusCode: 500,
      },
    };
  }
}
