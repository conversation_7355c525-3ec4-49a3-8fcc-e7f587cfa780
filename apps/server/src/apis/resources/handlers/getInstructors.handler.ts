import { ResourceRepository } from '../repository/resource.repository';
import type { Lessons } from 'shared';

export default async function getInstructorsHandler(
  requestId: string
): Promise<Lessons.GetInstructorsResult> {
  try {
    console.log(`${requestId} [RESOURCES] - GET_INSTRUCTORS - <PERSON><PERSON> started`);

    const instructors = await ResourceRepository.getInstructors();

    console.log(
      `${requestId} [RESOURCES] - GET_INSTRUCTORS - Found ${instructors.length} instructors`
    );

    return {
      data: {
        instructors: instructors as any,
        total: instructors.length,
      },
      error: null,
    };
  } catch (error) {
    console.error(
      `${requestId} [RESOURCES] - GET_INSTRUCTORS - <PERSON><PERSON> error:`,
      error
    );

    return {
      data: null,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to retrieve instructors',
        statusCode: 500,
      },
    };
  }
}
