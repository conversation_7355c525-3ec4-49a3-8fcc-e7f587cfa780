import { ResourceRepository } from '../repository/resource.repository';
import type { Lessons } from 'shared';

export default async function getCurriculumHandler(
  requestId: string
): Promise<Lessons.GetCurriculumResult> {
  try {
    console.log(`${requestId} [RESOURCES] - GET_CURRICULUM - <PERSON><PERSON> started`);

    const curriculum = await ResourceRepository.getCurriculum();

    console.log(
      `${requestId} [RESOURCES] - GET_CURRICULUM - Found ${curriculum.length} curriculum items`
    );

    return {
      data: {
        curriculum: curriculum as any,
        total: curriculum.length,
      },
      error: null,
    };
  } catch (error) {
    console.error(
      `${requestId} [RESOURCES] - GET_CURRICULUM - <PERSON><PERSON> error:`,
      error
    );

    return {
      data: null,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to retrieve curriculum',
        statusCode: 500,
      },
    };
  }
}
