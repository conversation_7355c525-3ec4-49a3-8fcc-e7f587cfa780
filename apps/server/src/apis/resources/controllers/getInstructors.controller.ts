import type { Context } from 'hono';
import getInstructors<PERSON>andler from '../handlers/getInstructors.handler';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const getInstructorsController = async (c: Context) => {
  const requestId = c.get('requestId');
  console.log(`${requestId} [RESOURCES] - GET_INSTRUCTORS - Request received`);

  try {
    const result = await getInstructors<PERSON>and<PERSON>(requestId);

    if (result.error) {
      console.error(
        `${requestId} [RESOURCES] - GET_INSTRUCTORS - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: result.error },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(
      `${requestId} [RESOURCES] - GET_INSTRUCTORS - Response sent successfully`
    );

    return c.json({ data: result.data, error: result.error }, 200);
  } catch (err) {
    console.error(
      `${requestId} [RESOURCES] - GET_INSTRUCTORS - Controller error: ${err}`
    );
    return c.json(
      {
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          statusCode: 500,
        },
      },
      500
    );
  }
};
