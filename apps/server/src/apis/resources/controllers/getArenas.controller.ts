import type { Context } from 'hono';
import get<PERSON>renasHandler from '../handlers/getArenas.handler';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const getArenasController = async (c: Context) => {
  const requestId = c.get('requestId');
  console.log(`${requestId} [RESOURCES] - GET_ARENAS - Request received`);

  try {
    const result = await get<PERSON>renas<PERSON>andler(requestId);

    if (result.error) {
      console.error(
        `${requestId} [RESOURCES] - GET_ARENAS - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: result.error },
        (result.error.statusCode as ContentfulStatusCode) || 500
      );
    }

    console.log(
      `${requestId} [RESOURCES] - GET_ARENAS - Response sent successfully`
    );

    return c.json({ data: result.data, error: result.error }, 200);
  } catch (err) {
    console.error(
      `${requestId} [RESOURCES] - GET_ARENAS - Controller error: ${err}`
    );
    return c.json(
      {
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          statusCode: 500,
        },
      },
      500
    );
  }
};
