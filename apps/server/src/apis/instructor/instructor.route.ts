import { Hono } from 'hono';
import { adminMiddleware } from '@server/middlewares/admin/admin.middleware';
import { createInstructorController } from './controllers/createInstructor.controller';
import { getInstructorController } from './controllers/getInstructor.controller';
import { updateInstructorController } from './controllers/updateInstructor.controller';
import { deleteInstructorController } from './controllers/deleteInstructor.controller';
import { validateCreateInstructor } from './validators/createInstructor.validator';
import { validateUpdateInstructor } from './validators/updateInstructor.validator';

const instructorRoute = new Hono()
  .use('*', adminMiddleware(['admin', 'super-admin']))
  .get('/', getInstructorController)
  .post('/', validateCreateInstructor, createInstructorController)
  .put('/:id', validateUpdateInstructor, updateInstructorController)
  .delete('/:id', deleteInstructorController);

export default instructorRoute;
