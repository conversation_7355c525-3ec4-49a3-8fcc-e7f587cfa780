import { zV<PERSON>da<PERSON> } from '@hono/zod-validator';
import { z } from 'zod';

const validDays = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'] as const;

const updateInstructorSchema = z
  .object({
    first_name: z
      .string()
      .min(1, 'First name is required')
      .max(50, 'First name must be less than 50 characters')
      .optional(),
    last_name: z
      .string()
      .min(1, 'Last name is required')
      .max(50, 'Last name must be less than 50 characters')
      .optional(),
    email: z
      .string()
      .email('Invalid email format')
      .max(255, 'Email must be less than 255 characters')
      .optional(),
    phone_number: z
      .string()
      .min(1, 'Phone number is required')
      .max(20, 'Phone number must be less than 20 characters')
      .optional(),
    profile_image: z
      .string()
      .max(255, 'Profile image URL must be less than 255 characters')
      .optional(),
    specialization: z
      .array(z.string().min(1, 'Specialization item cannot be empty'))
      .min(1, 'At least one specialization is required')
      .optional(),
    qualification: z.string().optional(),
    teaching_philosophy: z.string().optional(),
    experience: z
      .number()
      .min(0, 'Experience must be a non-negative number')
      .max(50, 'Experience cannot exceed 50 years')
      .optional(),
    availability: z
      .array(z.enum(validDays))
      .min(1, 'At least one day of availability is required')
      .refine((days) => [...new Set(days)].length === days.length, {
        message: 'Duplicate days are not allowed',
      })
      .optional(),
    bio_notes: z.string().optional(),
  })
  .refine((data) => Object.keys(data).length > 0, {
    message: 'At least one field must be provided for update',
  });

export const validateUpdateInstructor = zValidator(
  'json',
  updateInstructorSchema
);
