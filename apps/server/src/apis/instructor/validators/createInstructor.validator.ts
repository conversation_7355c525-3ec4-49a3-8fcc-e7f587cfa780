import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';

const validDays = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'] as const;

const createInstructorSchema = z.object({
  first_name: z
    .string()
    .min(1, 'First name is required')
    .max(50, 'First name must be less than 50 characters'),
  last_name: z
    .string()
    .min(1, 'Last name is required')
    .max(50, 'Last name must be less than 50 characters'),
  email: z
    .string()
    .email('Invalid email format')
    .max(255, 'Email must be less than 255 characters'),
  phone_number: z
    .string()
    .min(1, 'Phone number is required')
    .max(20, 'Phone number must be less than 20 characters'),
  profile_image: z
    .string()
    .max(255, 'Profile image URL must be less than 255 characters')
    .optional(),
  specialization: z
    .array(z.string().min(1, 'Specialization item cannot be empty'))
    .min(1, 'At least one specialization is required'),
  qualification: z.string().optional(),
  teaching_philosophy: z.string().optional(),
  experience: z
    .number()
    .min(0, 'Experience must be a non-negative number')
    .max(50, 'Experience cannot exceed 50 years'),
  availability: z
    .array(z.enum(validDays))
    .min(1, 'At least one day of availability is required')
    .refine((days) => [...new Set(days)].length === days.length, {
      message: 'Duplicate days are not allowed',
    }),
  bio_notes: z.string().optional(),
  // studio_id is automatically added by the controller
});

export const validateCreateInstructor = zValidator(
  'json',
  createInstructorSchema
);
