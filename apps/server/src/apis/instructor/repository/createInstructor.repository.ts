import { CustomError } from '@server/utils/customError';
import { db } from '@server/db';
import { instructorsTable } from '@server/db/schema/instructor';
import { eq, and, isNull } from 'drizzle-orm';
import type { typePayload } from '../../../../../shared/src/types/instructor/createInstructor.type';

export default async function createInstructorRepository(
  payload: typePayload,
  requestId: string
) {
  console.info(
    `${requestId} [INSTRUCTOR] - CREATE_INSTRUCTOR repository operation started`
  );

  try {
    // Step 1: Check for duplicate email
    await checkDuplicateEmail(payload.email, payload.studio_id, requestId);

    // Step 2: Create instructor record
    const newInstructor = await createInstructorRecord(payload, requestId);

    console.info(
      `${requestId} [INSTRUCTOR] - CREATE_INSTRUCTOR repository operation completed`
    );
    return {
      isSuccess: true,
      data: {
        message: 'Instructor created successfully',
        instructor: newInstructor,
      },
    };
  } catch (error) {
    console.error(
      `${requestId} [INSTRUCTOR] - CREATE_INSTRUCTOR repository operation failed:`,
      error
    );
    throw error;
  }
}

async function checkDuplicateEmail(
  email: string,
  studioId: string,
  requestId: string
): Promise<void> {
  const emailToCheck = email.toLowerCase();

  console.info(
    `${requestId} [INSTRUCTOR] - Checking duplicate email: ${emailToCheck}`
  );

  try {
    const existingInstructor = await db
      .select({
        id: instructorsTable.id,
      })
      .from(instructorsTable)
      .where(
        and(
          eq(instructorsTable.email, emailToCheck),
          isNull(instructorsTable.deleted_at)
        )
      )
      .limit(1);

    if (existingInstructor.length > 0) {
      throw new CustomError(
        'DUPLICATE_EMAIL',
        'Instructor with same email already exists',
        409
      );
    }
  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }
    console.error(`${requestId} [INSTRUCTOR] - Database query failed:`, error);
    throw new CustomError(
      'DB_ERROR',
      `Failed to check duplicate email: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}

async function createInstructorRecord(payload: typePayload, requestId: string) {
  console.info(`${requestId} [INSTRUCTOR] - Creating instructor record`);

  try {
    const newInstructors = await db
      .insert(instructorsTable)
      .values({
        first_name: payload.first_name.toLowerCase(),
        last_name: payload.last_name.toLowerCase(),
        email: payload.email.toLowerCase(),
        phone_number: payload.phone_number,
        profile_image: payload.profile_image,
        specialization: JSON.stringify(payload.specialization), // Convert array to JSON string
        qualification: payload.qualification,
        teaching_philosophy: payload.teaching_philosophy,
        experience: payload.experience,
        availability: JSON.stringify(payload.availability), // Convert array to JSON string
        bio_notes: payload.bio_notes,
        studio_id: payload.studio_id,
      })
      .returning();

    if (!newInstructors.length) {
      throw new CustomError(
        'DB_ERROR',
        'Failed to create instructor record - no data returned',
        500
      );
    }

    const newInstructor = newInstructors[0]!;

    // Parse JSON fields back to arrays for the response
    const instructorWithParsedFields = {
      ...newInstructor,
      specialization: JSON.parse(newInstructor.specialization as string),
      availability: JSON.parse(newInstructor.availability as string),
    };

    console.info(
      `${requestId} [INSTRUCTOR] - Instructor created successfully: ${newInstructor.id}`
    );
    return instructorWithParsedFields;
  } catch (error) {
    console.error(
      `${requestId} [INSTRUCTOR] - Database insertion failed:`,
      error
    );
    throw new CustomError(
      'DB_ERROR',
      `Failed to create instructor record: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}
