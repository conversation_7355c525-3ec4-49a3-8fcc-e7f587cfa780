import { CustomError } from '@server/utils/customError';
import { db } from '@server/db';
import { instructorsTable } from '@server/db/schema/instructor';
import { eq, and, isNull, ne } from 'drizzle-orm';
import type { typePayload } from '../../../../../shared/src/types/instructor/updateInstructor.type';

export default async function updateInstructorRepository(
  instructorId: string,
  payload: typePayload,
  requestId: string
) {
  console.info(
    `${requestId} [INSTRUCTOR] - UPDATE_INSTRUCTOR repository operation started for instructor: ${instructorId}`
  );

  try {
    // Step 1: Check if instructor exists
    await checkInstructorExists(instructorId, payload.studio_id, requestId);

    // Step 2: Check for duplicate names if name is being updated
    if (payload.first_name || payload.last_name) {
      await checkDuplicateName(instructorId, payload, requestId);
    }

    // Step 3: Check for duplicate email if email is being updated
    if (payload.email) {
      await checkDuplicateEmail(
        instructorId,
        payload.email,
        payload.studio_id,
        requestId
      );
    }

    // Step 4: Update instructor record
    const updatedInstructor = await updateInstructorRecord(
      instructorId,
      payload,
      requestId
    );

    console.info(
      `${requestId} [INSTRUCTOR] - UPDATE_INSTRUCTOR repository operation completed`
    );
    return {
      isSuccess: true,
      data: {
        message: 'Instructor updated successfully',
        instructor: updatedInstructor,
      },
    };
  } catch (error) {
    console.error(
      `${requestId} [INSTRUCTOR] - UPDATE_INSTRUCTOR repository operation failed:`,
      error
    );
    throw error;
  }
}

async function checkInstructorExists(
  instructorId: string,
  studioId: string,
  requestId: string
): Promise<void> {
  console.info(
    `${requestId} [INSTRUCTOR] - Checking if instructor exists: ${instructorId}`
  );

  try {
    const instructor = await db
      .select({
        id: instructorsTable.id,
      })
      .from(instructorsTable)
      .where(
        and(
          eq(instructorsTable.id, instructorId),
          eq(instructorsTable.studio_id, studioId),
          isNull(instructorsTable.deleted_at)
        )
      )
      .limit(1);

    if (!instructor.length) {
      throw new CustomError(
        'INSTRUCTOR_NOT_FOUND',
        'Instructor not found or you do not have permission to update this instructor',
        404
      );
    }
  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }
    console.error(`${requestId} [INSTRUCTOR] - Database query failed:`, error);
    throw new CustomError(
      'DB_ERROR',
      `Failed to check instructor existence: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}

async function checkDuplicateName(
  instructorId: string,
  payload: typePayload,
  requestId: string
): Promise<void> {
  if (payload.first_name || payload.last_name) {
    // Get current instructor data to fill in missing name parts
    const currentInstructor = await db
      .select({
        first_name: instructorsTable.first_name,
        last_name: instructorsTable.last_name,
      })
      .from(instructorsTable)
      .where(eq(instructorsTable.id, instructorId))
      .limit(1);

    if (!currentInstructor.length) return;

    const firstNameToCheck = (
      payload.first_name || currentInstructor[0]!.first_name
    ).toLowerCase();
    const lastNameToCheck = (
      payload.last_name || currentInstructor[0]!.last_name
    ).toLowerCase();

    console.info(
      `${requestId} [INSTRUCTOR] - Checking duplicate name: ${firstNameToCheck} ${lastNameToCheck}`
    );

    try {
      const existingInstructor = await db
        .select({
          id: instructorsTable.id,
        })
        .from(instructorsTable)
        .where(
          and(
            eq(instructorsTable.first_name, firstNameToCheck),
            eq(instructorsTable.last_name, lastNameToCheck),
            eq(instructorsTable.studio_id, payload.studio_id),
            ne(instructorsTable.id, instructorId),
            isNull(instructorsTable.deleted_at)
          )
        )
        .limit(1);

      if (existingInstructor.length > 0) {
        throw new CustomError(
          'DUPLICATE_INSTRUCTOR',
          'Instructor with same name already exists in this studio',
          409
        );
      }
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      console.error(
        `${requestId} [INSTRUCTOR] - Database query failed:`,
        error
      );
      throw new CustomError(
        'DB_ERROR',
        `Failed to check duplicate name: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500
      );
    }
  }
}

async function checkDuplicateEmail(
  instructorId: string,
  email: string,
  studioId: string,
  requestId: string
): Promise<void> {
  const emailToCheck = email.toLowerCase();

  console.info(
    `${requestId} [INSTRUCTOR] - Checking duplicate email: ${emailToCheck}`
  );

  try {
    const existingInstructor = await db
      .select({
        id: instructorsTable.id,
      })
      .from(instructorsTable)
      .where(
        and(
          eq(instructorsTable.email, emailToCheck),
          eq(instructorsTable.studio_id, studioId),
          ne(instructorsTable.id, instructorId),
          isNull(instructorsTable.deleted_at)
        )
      )
      .limit(1);

    if (existingInstructor.length > 0) {
      throw new CustomError(
        'DUPLICATE_EMAIL',
        'Instructor with same email already exists in this studio',
        409
      );
    }
  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }
    console.error(`${requestId} [INSTRUCTOR] - Database query failed:`, error);
    throw new CustomError(
      'DB_ERROR',
      `Failed to check duplicate email: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}

async function updateInstructorRecord(
  instructorId: string,
  payload: typePayload,
  requestId: string
) {
  console.info(
    `${requestId} [INSTRUCTOR] - Updating instructor record: ${instructorId}`
  );

  try {
    // Prepare update data with lowercase names if provided
    const updateData: any = {};

    if (payload.first_name)
      updateData.first_name = payload.first_name.toLowerCase();
    if (payload.last_name)
      updateData.last_name = payload.last_name.toLowerCase();
    if (payload.email) updateData.email = payload.email.toLowerCase();
    if (payload.phone_number) updateData.phone_number = payload.phone_number;
    if (payload.profile_image !== undefined)
      updateData.profile_image = payload.profile_image || null;
    if (payload.specialization)
      updateData.specialization = JSON.stringify(payload.specialization);
    if (payload.qualification !== undefined)
      updateData.qualification = payload.qualification || null;
    if (payload.teaching_philosophy !== undefined)
      updateData.teaching_philosophy = payload.teaching_philosophy || null;
    if (payload.experience) updateData.experience = payload.experience;
    if (payload.availability)
      updateData.availability = JSON.stringify(payload.availability);
    if (payload.bio_notes !== undefined)
      updateData.bio_notes = payload.bio_notes || null;

    const updatedInstructors = await db
      .update(instructorsTable)
      .set(updateData)
      .where(eq(instructorsTable.id, instructorId))
      .returning();

    if (!updatedInstructors.length) {
      throw new CustomError(
        'DB_ERROR',
        'Failed to update instructor record - no data returned',
        500
      );
    }

    const updatedInstructor = updatedInstructors[0]!;

    // Parse JSON fields back to arrays for the response
    const instructorWithParsedFields = {
      ...updatedInstructor,
      specialization: JSON.parse(updatedInstructor.specialization as string),
      availability: JSON.parse(updatedInstructor.availability as string),
    };

    console.info(
      `${requestId} [INSTRUCTOR] - Instructor updated successfully: ${updatedInstructor.id}`
    );
    return instructorWithParsedFields;
  } catch (error) {
    console.error(`${requestId} [INSTRUCTOR] - Database update failed:`, error);
    throw new CustomError(
      'DB_ERROR',
      `Failed to update instructor record: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}
