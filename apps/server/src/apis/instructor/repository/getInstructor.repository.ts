import { CustomError } from '@server/utils/customError';
import { db } from '@server/db';
import { instructorsTable } from '@server/db/schema/instructor';
import { eq, and, isNull, ilike, sql } from 'drizzle-orm';
import type { typeQueryParams } from '../../../../../shared/src/types/instructor/getInstructor.type';

export default async function getInstructorRepository(
  queryParams: typeQueryParams,
  requestId: string
) {
  console.info(
    `${requestId} [INSTRUCTOR] - GET_INSTRUCTOR repository operation started`
  );

  try {
    const page = parseInt(queryParams.page || '1', 10);
    const limit = parseInt(queryParams.limit || '10', 10);
    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [isNull(instructorsTable.deleted_at)];

    if (queryParams.studio_id) {
      whereConditions.push(
        eq(instructorsTable.studio_id, queryParams.studio_id)
      );
    }

    if (queryParams.specialization) {
      whereConditions.push(
        ilike(
          instructorsTable.specialization,
          `%${queryParams.specialization}%`
        )
      );
    }

    if (queryParams.first_name) {
      whereConditions.push(
        ilike(instructorsTable.first_name, `%${queryParams.first_name}%`)
      );
    }

    if (queryParams.last_name) {
      whereConditions.push(
        ilike(instructorsTable.last_name, `%${queryParams.last_name}%`)
      );
    }

    if (queryParams.email) {
      whereConditions.push(
        ilike(instructorsTable.email, `%${queryParams.email}%`)
      );
    }

    if (queryParams.experience) {
      whereConditions.push(
        eq(instructorsTable.experience, parseInt(queryParams.experience, 10))
      );
    }

    if (queryParams.availability) {
      whereConditions.push(
        ilike(instructorsTable.availability, `%${queryParams.availability}%`)
      );
    }

    // Get instructors with pagination
    const instructors = await db
      .select()
      .from(instructorsTable)
      .where(and(...whereConditions))
      .orderBy(instructorsTable.created_at)
      .limit(limit)
      .offset(offset);

    // Parse JSON fields for each instructor
    const instructorsWithParsedFields = instructors.map((instructor) => ({
      ...instructor,
      specialization: JSON.parse(instructor.specialization as string),
      availability: JSON.parse(instructor.availability as string),
    }));

    // Get total count for pagination
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(instructorsTable)
      .where(and(...whereConditions));

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    console.info(
      `${requestId} [INSTRUCTOR] - GET_INSTRUCTOR repository operation completed`
    );
    return {
      isSuccess: true,
      data: {
        message: 'Instructors retrieved successfully',
        instructors: instructorsWithParsedFields,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      },
    };
  } catch (error) {
    console.error(
      `${requestId} [INSTRUCTOR] - GET_INSTRUCTOR repository operation failed:`,
      error
    );
    throw new CustomError(
      'DB_ERROR',
      `Failed to retrieve instructors: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}
