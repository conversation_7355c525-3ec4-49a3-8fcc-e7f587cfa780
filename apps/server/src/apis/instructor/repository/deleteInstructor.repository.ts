import { CustomError } from '@server/utils/customError';
import { db } from '@server/db';
import { instructorsTable } from '@server/db/schema/instructor';
import { eq, and, isNull } from 'drizzle-orm';

export default async function deleteInstructorRepository(
  instructorId: string,
  studioId: string,
  requestId: string
) {
  console.info(
    `${requestId} [INSTRUCTOR] - DELETE_INSTRUCTOR repository operation started for instructor: ${instructorId}`
  );

  try {
    // Step 1: Check if instructor exists and belongs to the studio
    await checkInstructorExists(instructorId, studioId, requestId);

    // Step 2: Soft delete the instructor record
    const deletedInstructor = await softDeleteInstructorRecord(
      instructorId,
      requestId
    );

    console.info(
      `${requestId} [INSTRUCTOR] - DELETE_INSTRUCTOR repository operation completed`
    );
    return {
      isSuccess: true,
      data: {
        message: 'Instructor deleted successfully',
        instructor: {
          id: deletedInstructor.id,
          first_name: deletedInstructor.first_name,
          last_name: deletedInstructor.last_name,
          deleted_at: deletedInstructor.deleted_at!,
        },
      },
    };
  } catch (error) {
    console.error(
      `${requestId} [INSTRUCTOR] - DELETE_INSTRUCTOR repository operation failed:`,
      error
    );
    throw error;
  }
}

async function checkInstructorExists(
  instructorId: string,
  studioId: string,
  requestId: string
): Promise<void> {
  console.info(
    `${requestId} [INSTRUCTOR] - Checking if instructor exists: ${instructorId}`
  );

  try {
    const instructor = await db
      .select({
        id: instructorsTable.id,
      })
      .from(instructorsTable)
      .where(
        and(
          eq(instructorsTable.id, instructorId),
          eq(instructorsTable.studio_id, studioId),
          isNull(instructorsTable.deleted_at)
        )
      )
      .limit(1);

    if (!instructor.length) {
      throw new CustomError(
        'INSTRUCTOR_NOT_FOUND',
        'Instructor not found or you do not have permission to delete this instructor',
        404
      );
    }
  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }
    console.error(`${requestId} [INSTRUCTOR] - Database query failed:`, error);
    throw new CustomError(
      'DB_ERROR',
      `Failed to check instructor existence: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}

async function softDeleteInstructorRecord(
  instructorId: string,
  requestId: string
) {
  console.info(
    `${requestId} [INSTRUCTOR] - Soft deleting instructor record: ${instructorId}`
  );

  try {
    const deletedInstructors = await db
      .update(instructorsTable)
      .set({
        deleted_at: new Date(),
      })
      .where(eq(instructorsTable.id, instructorId))
      .returning({
        id: instructorsTable.id,
        first_name: instructorsTable.first_name,
        last_name: instructorsTable.last_name,
        deleted_at: instructorsTable.deleted_at,
      });

    if (!deletedInstructors.length) {
      throw new CustomError(
        'DB_ERROR',
        'Failed to delete instructor record - no data returned',
        500
      );
    }

    const deletedInstructor = deletedInstructors[0]!;
    console.info(
      `${requestId} [INSTRUCTOR] - Instructor soft deleted successfully: ${deletedInstructor.id}`
    );
    return deletedInstructor;
  } catch (error) {
    console.error(`${requestId} [INSTRUCTOR] - Database delete failed:`, error);
    throw new CustomError(
      'DB_ERROR',
      `Failed to delete instructor record: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}
