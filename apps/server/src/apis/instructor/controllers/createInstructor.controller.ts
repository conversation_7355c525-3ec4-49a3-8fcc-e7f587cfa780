import type { Context } from 'hono';
import createInstructorHandler from '../handlers/createInstructor.handler';
import type { typePayloadInput } from '../../../../../shared/src/types/instructor/createInstructor.type';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const createInstructorController = async (c: Context) => {
  const requestId = c.get('requestId');
  const user = c.get('user');

  console.log(
    `${requestId} [INSTRUCTOR] - CREATE_INSTRUCTOR - Request received`
  );

  try {
    // Get studio_id from authenticated user metadata
    const studioId = user?.user_metadata?.studio_id;

    if (!studioId) {
      console.error(
        `${requestId} [INSTRUCTOR] - CREATE_INSTRUCTOR - Missing studio ID`
      );
      return c.json(
        { data: null, error: { message: 'Studio ID not found' } },
        400
      );
    }

    const payload = (await c.req.json()) as typePayloadInput;

    // Add studio_id to payload
    const payloadWithStudio = {
      ...payload,
      studio_id: studioId,
    };

    const result = await createInstructorHandler(payloadWithStudio, requestId);

    if (result.error) {
      console.error(
        `${requestId} [INSTRUCTOR] - CREATE_INSTRUCTOR - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: result.error },
        result.error.statusCode as ContentfulStatusCode
      );
    }

    console.log(`${requestId} [INSTRUCTOR] - CREATE_INSTRUCTOR - Success`);
    return c.json({ data: result.data, error: null }, 201);
  } catch (error) {
    console.error(
      `${requestId} [INSTRUCTOR] - CREATE_INSTRUCTOR - Unexpected error: ${error}`
    );

    return c.json(
      {
        data: null,
        error: {
          code: 'UNEXPECTED_ERROR',
          message: 'An unexpected error occurred',
          statusCode: 500,
        },
      },
      500
    );
  }
};
