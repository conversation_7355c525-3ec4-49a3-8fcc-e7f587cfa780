import type { Context } from 'hono';
import getInstructorHandler from '../handlers/getInstructor.handler';
import type { typeQueryParams } from '../../../../../shared/src/types/instructor/getInstructor.type';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const getInstructorController = async (c: Context) => {
  const requestId = c.get('requestId');
  const user = c.get('user');

  console.log(`${requestId} [INSTRUCTOR] - GET_INSTRUCTOR - Request received`);

  try {
    // Get studio_id from authenticated user metadata
    const studioId = user?.user_metadata?.studio_id;

    if (!studioId) {
      console.error(
        `${requestId} [INSTRUCTOR] - GET_INSTRUCTOR - Missing studio ID`
      );
      return c.json(
        { data: null, error: { message: 'Studio ID not found' } },
        400
      );
    }

    // Get query parameters
    const queryParams: typeQueryParams = {
      page: c.req.query('page'),
      limit: c.req.query('limit'),
      studio_id: studioId, // Always filter by user's studio
      specialization: c.req.query('specialization'),
      first_name: c.req.query('first_name'),
      last_name: c.req.query('last_name'),
    };

    const result = await getInstructorHandler(queryParams, requestId);

    if (result.error) {
      console.error(
        `${requestId} [INSTRUCTOR] - GET_INSTRUCTOR - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: result.error },
        result.error.statusCode as ContentfulStatusCode
      );
    }

    console.log(`${requestId} [INSTRUCTOR] - GET_INSTRUCTOR - Success`);
    return c.json({ data: result.data, error: null }, 200);
  } catch (error) {
    console.error(
      `${requestId} [INSTRUCTOR] - GET_INSTRUCTOR - Unexpected error: ${error}`
    );

    return c.json(
      {
        data: null,
        error: {
          code: 'UNEXPECTED_ERROR',
          message: 'An unexpected error occurred',
          statusCode: 500,
        },
      },
      500
    );
  }
};
