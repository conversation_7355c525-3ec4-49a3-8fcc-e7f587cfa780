import type { Context } from 'hono';
import deleteInstructorHandler from '../handlers/deleteInstructor.handler';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

export const deleteInstructorController = async (c: Context) => {
  const requestId = c.get('requestId');
  const user = c.get('user');

  console.log(
    `${requestId} [INSTRUCTOR] - DELETE_INSTRUCTOR - Request received`
  );

  try {
    // Get studio_id from authenticated user metadata
    const studioId = user?.user_metadata?.studio_id;

    if (!studioId) {
      console.error(
        `${requestId} [INSTRUCTOR] - DELETE_INSTRUCTOR - Missing studio ID`
      );
      return c.json(
        { data: null, error: { message: 'Studio ID not found' } },
        400
      );
    }

    const instructorId = c.req.param('id');

    if (!instructorId) {
      console.error(
        `${requestId} [INSTRUCTOR] - DELETE_INSTRUCTOR - Missing instructor ID`
      );
      return c.json(
        { data: null, error: { message: 'Instructor ID is required' } },
        400
      );
    }

    const payload = {
      id: instructorId,
      studio_id: studioId,
    };

    const result = await deleteInstructorHandler(payload, requestId);

    if (result.error) {
      console.error(
        `${requestId} [INSTRUCTOR] - DELETE_INSTRUCTOR - Handler error: ${result.error.message}`
      );

      return c.json(
        { data: null, error: result.error },
        result.error.statusCode as ContentfulStatusCode
      );
    }

    console.log(`${requestId} [INSTRUCTOR] - DELETE_INSTRUCTOR - Success`);
    return c.json({ data: result.data, error: null }, 200);
  } catch (error) {
    console.error(
      `${requestId} [INSTRUCTOR] - DELETE_INSTRUCTOR - Unexpected error: ${error}`
    );

    return c.json(
      {
        data: null,
        error: {
          code: 'UNEXPECTED_ERROR',
          message: 'An unexpected error occurred',
          statusCode: 500,
        },
      },
      500
    );
  }
};
