import type { CustomError } from '@server/utils/customError';
import type {
  typePayload,
  typeResult,
} from '../../../../../shared/src/types/instructor/updateInstructor.type';
import updateInstructorRepository from '../repository/updateInstructor.repository';

export default async function updateInstructorHandler(
  instructorId: string,
  payload: typePayload,
  requestId: string
): Promise<typeResult> {
  let data: typeResult['data'] = null;
  let error: typeResult['error'] = null;

  try {
    console.info(
      `${requestId} [INSTRUCTOR] - UPDATE_INSTRUCTOR handler started for instructor: ${instructorId}`
    );

    const { isSuccess, data: instructorData } =
      await updateInstructorRepository(instructorId, payload, requestId);

    if (isSuccess) {
      data = instructorData;
    }

    console.info(
      `${requestId} [INSTRUCTOR] - UPDATE_INSTRUCTOR handler completed successfully`
    );
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [INSTRUCTOR] - UPDATE_INSTRUCTOR handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
