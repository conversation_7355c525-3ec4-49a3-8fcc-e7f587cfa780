import type { CustomError } from '@server/utils/customError';
import type {
  typeQueryParams,
  typeResult,
} from '../../../../../shared/src/types/instructor/getInstructor.type';
import getInstructorRepository from '../repository/getInstructor.repository';

export default async function getInstructorHandler(
  queryParams: typeQueryParams,
  requestId: string
): Promise<typeResult> {
  let data: typeResult['data'] = null;
  let error: typeResult['error'] = null;

  try {
    console.info(`${requestId} [INSTRUCTOR] - GET_INSTRUCTOR handler started`);

    const { isSuccess, data: instructorData } = await getInstructorRepository(
      queryParams,
      requestId
    );

    if (isSuccess) {
      data = instructorData;
    }

    console.info(
      `${requestId} [INSTRUCTOR] - GET_INSTRUCTOR handler completed successfully`
    );
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [INSTRUCTOR] - GET_INSTRUCTOR handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
