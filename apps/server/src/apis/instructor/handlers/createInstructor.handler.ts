import type { CustomError } from '@server/utils/customError';
import type {
  typePayload,
  typeResult,
} from '../../../../../shared/src/types/instructor/createInstructor.type';
import createInstructorRepository from '../repository/createInstructor.repository';

export default async function createInstructorHandler(
  payload: typePayload,
  requestId: string
): Promise<typeResult> {
  let data: typeResult['data'] = null;
  let error: typeResult['error'] = null;

  try {
    console.info(
      `${requestId} [INSTRUCTOR] - CREATE_INSTRUCTOR handler started`
    );

    const { isSuccess, data: instructorData } =
      await createInstructorRepository(payload, requestId);

    if (isSuccess) {
      data = instructorData;
    }

    console.info(
      `${requestId} [INSTRUCTOR] - CREATE_INSTRUCTOR handler completed successfully`
    );
  } catch (err) {
    const customError = err as CustomError;
    console.error(
      `${requestId} [INSTRUCTOR] - CREATE_INSTRUCTOR handler error ${customError.message}`
    );
    error = {
      code: customError.errorCode ?? 'UNEXPECTED_ERROR',
      message: customError.message ?? 'An unexpected error occurred',
      statusCode: customError.statusCode ?? 500,
    };
  }

  return { data, error };
}
