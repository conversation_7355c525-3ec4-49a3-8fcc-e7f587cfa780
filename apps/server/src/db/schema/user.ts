import {
  pgTable,
  varchar,
  uuid,
  pgEnum,
  date,
  boolean,
} from 'drizzle-orm/pg-core';
import { timestamps } from '../helper/timestamp';
import { studioTable } from './studio';

export const roleEnum = pgEnum('role', [
  'parent',
  'admin',
  'instructor',
  'super-admin',
  'owner',
]);

export const usersTable = pgTable('users', {
  id: uuid('id').primaryKey(),
  email: varchar('email', { length: 100 }).notNull(),
  first_name: varchar('first_name', { length: 30 }).notNull(),
  last_name: varchar('last_name', { length: 30 }).notNull(),
  studio_id: varchar('studio_id')
    .references(() => studioTable.id)
    .notNull(),
  role: roleEnum('role').notNull(),
  phone: varchar('phone', { length: 15 }),
  country_code: varchar('country_code', { length: 5 }),
  address: varchar('address', { length: 255 }),
  date_of_birth: date('date_of_birth'),
  profile_image: varchar('profile_image', { length: 255 }),
  emergency_contact_phone: varchar('emergency_contact_phone', { length: 15 }),
  emergency_contact_name: varchar('emergency_contact_name', { length: 30 }),
  isPasswordSet: boolean('isPasswordSet').default(false).notNull(),
  ...timestamps,
});
