import {
  integer,
  pgTable,
  varchar,
  text,
  timestamp,
  decimal,
  uuid,
} from 'drizzle-orm/pg-core';
import { timestamps } from '../helper/timestamp';
import { arenasTable } from './arena';
import { usersTable } from './user';

export const lessonsTable = pgTable('lessons', {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  title: varchar({ length: 255 }).notNull(),
  lesson_type: varchar({ length: 50 }).notNull(), // single-lesson, recurring-lesson, camp
  arena_id: integer().references(() => arenasTable.id),
  date: timestamp().notNull(),
  start_time: timestamp().notNull(),
  end_time: timestamp().notNull(),
  duration_minutes: integer().notNull(),
  max_students: integer().notNull().default(6),
  current_students: integer().notNull().default(0),
  status: varchar({ length: 50 }).notNull().default('scheduled'), // scheduled, completed, cancelled, in_progress
  notes: text(),
  require_form: integer().notNull().default(0), // 0 for false, 1 for true
  require_payment: integer().notNull().default(1), // 0 for false, 1 for true
  price: decimal({ precision: 10, scale: 2 }), // lesson price
  created_by: uuid()
    .references(() => usersTable.id)
    .notNull(), // instructor/admin who created
  parent_lesson_id: integer(), // for recurring lessons - self reference removed for now
  recurrence_pattern: text(), // JSON string for recurrence rules
  curriculum_items: text(), // JSON array of curriculum IDs
  attachments: text(), // JSON array of attachment URLs/metadata
  ...timestamps,
});

// Relations will be defined in a separate relations file to avoid circular imports
