import { pgTable, varchar, text, timestamp } from 'drizzle-orm/pg-core';
import { timestamps } from '../helper/timestamp';

export const studioTable = pgTable('studios', {
  id: varchar('id').notNull().primaryKey(),
  access_token: text('access_token'),
  refresh_token: text('refresh_token'),
  token_expiry: timestamp('token_expiry'),
  timezone: varchar('timezone', { length: 50 }),
  currency: varchar('currency', { length: 3 }).default('USD'),
  address: text('address'),
  name: varchar('name', { length: 100 }),
  logo_url: text('logo_url'),
  phone: varchar('phone', { length: 20 }),
  email: varchar('email', { length: 100 }),
  ...timestamps,
});
