import {
  integer,
  pgTable,
  varchar,
  text,
  jsonb,
  decimal,
} from 'drizzle-orm/pg-core';
import { timestamps } from '../helper/timestamp';

export const curriculumTable = pgTable('curriculum', {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  name: varchar({ length: 255 }).notNull(),
  description: text(),
  skill_level: varchar({ length: 50 }).notNull(), // beginner, intermediate, advanced
  category: varchar({ length: 100 }).notNull().default('general'), // general, jumping, dressage, trail, etc.
  duration_minutes: integer(), // typical duration for this curriculum item
  estimated_lessons: integer().default(1), // Number of lessons typically needed
  prerequisites: jsonb().default('[]'), // Array of prerequisite curriculum IDs
  learning_objectives: jsonb().default('[]'), // Array of learning objectives/goals
  milestones: jsonb().default('[]'), // Array of milestone objects with id, name, description
  assessment_criteria: jsonb().default('[]'), // Array of assessment criteria
  resources: jsonb().default('[]'), // Array of resource objects (videos, documents, etc.)
  difficulty_rating: decimal({ precision: 3, scale: 1 }).default('1.0'), // 1.0 to 10.0 difficulty scale
  version: varchar({ length: 20 }).default('1.0'), // Version for curriculum updates
  is_active: integer().notNull().default(1), // 1 for active, 0 for inactive
  ...timestamps,
});
