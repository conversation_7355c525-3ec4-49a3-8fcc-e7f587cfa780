import { relations } from 'drizzle-orm';
import { lessonsTable } from './lesson';
import { arenasTable } from './arena';
import { usersTable } from './user';
import { lessonInstructorsTable } from './lesson-instructor';
import { lessonEnrollmentsTable } from './lesson-enrollment';
import { studentsTable } from './student';
import { curriculumTable } from './curriculum';
import { curriculumProgressTable } from './curriculum-progress';

// Lesson Relations
export const lessonsRelations = relations(lessonsTable, ({ one, many }) => ({
  arena: one(arenasTable, {
    fields: [lessonsTable.arena_id],
    references: [arenasTable.id],
  }),
  creator: one(usersTable, {
    fields: [lessonsTable.created_by],
    references: [usersTable.id],
  }),
  parentLesson: one(lessonsTable, {
    fields: [lessonsTable.parent_lesson_id],
    references: [lessonsTable.id],
    relationName: 'parentChild',
  }),
  childLessons: many(lessonsTable, {
    relationName: 'parentChild',
  }),
  instructors: many(lessonInstructorsTable),
  enrollments: many(lessonEnrollmentsTable),
}));

// Arena Relations
export const arenasRelations = relations(arenasTable, ({ many }) => ({
  lessons: many(lessonsTable),
}));

// Lesson Instructor Relations
export const lessonInstructorsRelations = relations(
  lessonInstructorsTable,
  ({ one }) => ({
    lesson: one(lessonsTable, {
      fields: [lessonInstructorsTable.lesson_id],
      references: [lessonsTable.id],
    }),
    instructor: one(usersTable, {
      fields: [lessonInstructorsTable.instructor_id],
      references: [usersTable.id],
    }),
  })
);

// Lesson Enrollment Relations
export const lessonEnrollmentsRelations = relations(
  lessonEnrollmentsTable,
  ({ one }) => ({
    lesson: one(lessonsTable, {
      fields: [lessonEnrollmentsTable.lesson_id],
      references: [lessonsTable.id],
    }),
    student: one(studentsTable, {
      fields: [lessonEnrollmentsTable.student_id],
      references: [studentsTable.id],
    }),
  })
);

// User Relations (for instructors)
export const usersRelations = relations(usersTable, ({ many }) => ({
  createdLessons: many(lessonsTable, { relationName: 'creator' }),
  instructorAssignments: many(lessonInstructorsTable),
}));

// Student Relations
export const studentsRelations = relations(studentsTable, ({ one, many }) => ({
  parent: one(usersTable, {
    fields: [studentsTable.parent_id],
    references: [usersTable.id],
  }),
  enrollments: many(lessonEnrollmentsTable),
  curriculumProgress: many(curriculumProgressTable),
}));

// Curriculum Relations
export const curriculumRelations = relations(curriculumTable, ({ many }) => ({
  progress: many(curriculumProgressTable),
}));

// Curriculum Progress Relations
export const curriculumProgressRelations = relations(
  curriculumProgressTable,
  ({ one }) => ({
    curriculum: one(curriculumTable, {
      fields: [curriculumProgressTable.curriculum_id],
      references: [curriculumTable.id],
    }),
    student: one(studentsTable, {
      fields: [curriculumProgressTable.student_id],
      references: [studentsTable.id],
    }),
    instructor: one(usersTable, {
      fields: [curriculumProgressTable.instructor_id],
      references: [usersTable.id],
    }),
  })
);
