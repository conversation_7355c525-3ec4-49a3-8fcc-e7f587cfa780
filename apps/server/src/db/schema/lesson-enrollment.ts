import {
  integer,
  pgTable,
  varchar,
  text,
  decimal,
  uuid,
} from 'drizzle-orm/pg-core';
import { timestamps } from '../helper/timestamp';
import { lessonsTable } from './lesson';
import { studentsTable } from './student';

export const lessonEnrollmentsTable = pgTable('lesson_enrollments', {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  lesson_id: integer()
    .notNull()
    .references(() => lessonsTable.id),
  student_id: uuid()
    .notNull()
    .references(() => studentsTable.id), // UUID string reference to students table
  attendance_status: varchar({ length: 50 }).default('pending'), // pending, present, absent, excused
  payment_status: varchar({ length: 50 }).default('unpaid'), // paid, unpaid, partial, refunded
  payment_amount: decimal({ precision: 10, scale: 2 }),
  assigned_horse: varchar({ length: 255 }), // horse name/identifier
  special_requirements: text(), // dietary, medical, equipment needs
  notes: text(), // instructor notes about the student
  ...timestamps,
});

// Relations will be defined in a separate relations file to avoid circular imports
