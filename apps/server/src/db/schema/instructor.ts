import { pgTable, varchar, uuid, text, integer } from 'drizzle-orm/pg-core';
import { timestamps } from '../helper/timestamp';
import { studioTable } from './studio';

export const instructorsTable = pgTable('instructors', {
  id: uuid('id').primaryKey().defaultRandom(),
  first_name: varchar('first_name', { length: 50 }).notNull(),
  last_name: varchar('last_name', { length: 50 }).notNull(),
  email: varchar('email', { length: 255 }).notNull(),
  phone_number: varchar('phone_number', { length: 20 }).notNull(),
  profile_image: varchar('profile_image', { length: 255 }),
  specialization: text('specialization').notNull(), // JSON array of strings
  qualification: text('qualification'),
  teaching_philosophy: text('teaching_philosophy'),
  experience: integer('experience').notNull(), // years of experience
  availability: text('availability').notNull(), // JSON array of days: ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun']
  bio_notes: text('bio_notes'),
  studio_id: varchar('studio_id')
    .references(() => studioTable.id)
    .notNull(),
  ...timestamps,
});
