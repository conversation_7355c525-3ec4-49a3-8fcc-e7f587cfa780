import { pgTable, varchar, uuid } from 'drizzle-orm/pg-core';
import { timestamps } from '../helper/timestamp';
import { usersTable } from './user';

export const stripeCustomersTable = pgTable('stripe_customers', {
  id: uuid('id').primaryKey().defaultRandom(),
  user_id: uuid('user_id')
    .references(() => usersTable.id, { onDelete: 'cascade' })
    .notNull(),
  stripe_customer_id: varchar('stripe_customer_id', { length: 255 })
    .notNull()
    .unique(),
  ...timestamps,
});
