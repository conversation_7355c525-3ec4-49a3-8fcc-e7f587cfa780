import {
  integer,
  pgTable,
  varchar,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { timestamps } from '../helper/timestamp';
import { lessonsTable } from './lesson';
import { usersTable } from './user';

export const lessonInstructorsTable = pgTable('lesson_instructors', {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  lesson_id: integer()
    .notNull()
    .references(() => lessonsTable.id),
  instructor_id: uuid()
    .notNull()
    .references(() => usersTable.id),
  time_block_start: timestamp(), // for split instruction periods
  time_block_end: timestamp(),
  assigned_horses: text(), // JSON array of horse names assigned to this instructor
  role: varchar({ length: 50 }).default('primary'), // primary, assistant, substitute
  notes: text(), // special instructions for this instructor
  ...timestamps,
});

// Relations will be defined in a separate relations file to avoid circular imports
