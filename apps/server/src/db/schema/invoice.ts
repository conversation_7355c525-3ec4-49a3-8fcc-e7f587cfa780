import {
  pgTable,
  varchar,
  uuid,
  timestamp,
  json,
  pgEnum,
} from 'drizzle-orm/pg-core';
import { timestamps } from '../helper/timestamp';
import { usersTable } from './user';

// Create enums for PostgreSQL
export const paymentProviderEnum = pgEnum('payment_provider', [
  'stripe',
  'paypal',
  'manual',
  'cash',
]);
export const paymentMethodEnum = pgEnum('payment_method', [
  'card',
  'link',
  'us_bank_account',
  'cash',
  'check',
  'wallet',
]);
export const invoiceTypeEnum = pgEnum('invoice_type', [
  'subscription',
  'one_time',
  'upfront',
  'manual',
  'bulk_payment',
]);
export const invoiceStatusEnum = pgEnum('invoice_status', [
  'paid',
  'partially_paid',
  'upcoming',
  'pending',
  'full_refund',
  'partial_refund',
  'pending_refund',
  'refund_failed',
  'void',
  'failed',
  'scheduled',
  'paused',
  'processing',
  'cancelled',
  'free',
]);

export const invoicesTable = pgTable('invoices', {
  id: uuid('id').primaryKey().defaultRandom(),
  studio_id: varchar('studio_id', { length: 255 }).notNull(),
  user_id: uuid('user_id')
    .references(() => usersTable.id, { onDelete: 'cascade' })
    .notNull(),

  // Optional entity linking
  entity_id: uuid('entity_id'),
  entity_type: varchar('entity_type', { length: 50 }), // 'LESSON', etc.

  // Status and payment tracking
  status: invoiceStatusEnum('status').default('pending').notNull(),
  payment_provider: paymentProviderEnum('payment_provider').default('stripe'),
  payment_method: paymentMethodEnum('payment_method').default('card').notNull(),
  type: invoiceTypeEnum('type').notNull(),

  // Stripe/external provider transaction ID
  transaction_id: varchar('transaction_id', { length: 255 }),

  // Line items (JSON array)
  line_items: json('line_items')
    .$type<
      Array<{
        name: string;
        amount: number;
        type: string;
        quantity: number;
        total: number;
      }>
    >()
    .notNull(),

  // Payments (JSON array) - tracks each payment made
  payments: json('payments')
    .$type<
      Array<{
        method: string;
        amount: number; // in dollars
        paymentIntentId?: string;
        date: Date;
      }>
    >()
    .default([]),

  // Amount tracking
  base_amount: varchar('base_amount').notNull(), // in dollars (as string to avoid precision issues)
  final_amount: varchar('final_amount').notNull(), // in dollars, after discounts (as string to avoid precision issues)

  // Date tracking
  payment_date: timestamp('payment_date'),
  due_date: timestamp('due_date'),
  start_date: timestamp('start_date'),
  end_date: timestamp('end_date'),

  // Metadata (JSON object)
  metadata: json('metadata')
    .$type<{
      paymentIntentId?: string;
      failureReason?: string;
      refundReason?: string;
      attemptCount?: number;
      lastAttemptDate?: Date;
      internalTransactionId?: string;
      refundId?: string;
    }>()
    .default({}),

  ...timestamps,
});

// Indexes for better query performance
export type Invoice = typeof invoicesTable.$inferSelect;
export type InsertInvoice = typeof invoicesTable.$inferInsert;
