import {
  pgEnum,
  text,
  varchar,
  integer,
  uuid,
  pgTable,
} from 'drizzle-orm/pg-core';
import { timestamps } from '../helper/timestamp';
import { studioTable } from './studio';

export const horseStatusEnum = pgEnum('horse_status', [
  'available',
  'resting',
  'injured',
]);

export const horsesTable = pgTable('horses', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 100 }).notNull(),
  breed: varchar('breed', { length: 100 }).notNull(),
  status: horseStatusEnum('status').notNull().default('available'),
  age: integer('age').notNull(),
  training_level: varchar('training_level', { length: 50 }).notNull(),
  specialties: text('specialties').array(),
  suitable_for: text('suitable_for').array(),
  notes: text('notes'),
  studio_id: varchar('studio_id')
    .references(() => studioTable.id)
    .notNull(),
  ...timestamps,
});
