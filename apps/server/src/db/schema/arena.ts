import { integer, pgTable, varchar, text } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { timestamps } from '../helper/timestamp';

export const arenasTable = pgTable('arenas', {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  name: varchar({ length: 255 }).notNull(),
  location: varchar({ length: 255 }),
  description: text(),
  capacity: integer().notNull().default(10),
  equipment: text(), // JSON string of available equipment
  is_active: integer().notNull().default(1), // 1 for active, 0 for inactive
  ...timestamps,
});

// Relations will be defined in a separate relations file to avoid circular imports
