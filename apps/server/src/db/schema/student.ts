import { pgEnum, text, varchar } from 'drizzle-orm/pg-core';
import { uuid, pgTable, date } from 'drizzle-orm/pg-core';
import { timestamps } from '../helper/timestamp';
import { usersTable } from './user';
import { skillTable } from './skill';

export const genderEnum = pgEnum('gender', ['male', 'female', 'other']);
export const ridingLevelEnum = pgEnum('riding_level', [
  'beginner',
  'intermediate',
  'advanced',
]);

export const studentsTable = pgTable('students', {
  id: uuid('id').primaryKey().defaultRandom(),
  first_name: varchar('first_name', { length: 30 }).notNull(),
  last_name: varchar('last_name', { length: 30 }).notNull(),
  riding_level: ridingLevelEnum('riding_level').notNull(),
  date_of_birth: date('date_of_birth').notNull(),
  gender: genderEnum('gender').notNull(),
  skill_id: uuid('skill_id').references(() => skillTable.id),
  parent_id: uuid('parent_id')
    .references(() => usersTable.id)
    .notNull(),
  profile_image: text('profile_image'),
  email: varchar('email', { length: 100 }),
  phone: varchar('phone', { length: 15 }),
  country_code: varchar('country_code', { length: 5 }),
  previous_experience: text('previous_experience'),
  riding_goals: text('riding_goals'),
  medical_conditions: text('medical_conditions'),
  allergies: text('allergies'),
  ...timestamps,
});
