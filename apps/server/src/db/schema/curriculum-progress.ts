import {
  integer,
  pgTable,
  varchar,
  text,
  decimal,
  jsonb,
  uuid,
} from 'drizzle-orm/pg-core';
import { timestamps } from '../helper/timestamp';
import { curriculumTable } from './curriculum';
import { studentsTable } from './student';
import { usersTable } from './user';

export const curriculumProgressTable = pgTable('curriculum_progress', {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  curriculum_id: integer()
    .notNull()
    .references(() => curriculumTable.id),
  student_id: uuid()
    .notNull()
    .references(() => studentsTable.id),
  instructor_id: uuid()
    .notNull()
    .references(() => usersTable.id),
  status: varchar({ length: 50 }).notNull().default('not_started'), // not_started, in_progress, completed, on_hold
  progress_percentage: decimal({ precision: 5, scale: 2 }).default('0.00'), // 0.00 to 100.00
  completed_milestones: jsonb().default('[]'), // Array of completed milestone IDs
  current_milestone: integer(), // Current milestone being worked on
  notes: text(), // Instructor notes on progress
  skills_mastered: jsonb().default('[]'), // Array of mastered skill IDs
  assessment_scores: jsonb().default('{}'), // Object with assessment results
  start_date: timestamps.created_at,
  completion_date: timestamps.updated_at.default(null),
  last_lesson_date: timestamps.updated_at.default(null),
  ...timestamps,
});

// Relations will be defined in a separate relations file to avoid circular imports
