import { supabase } from '../../utils/supabase';
import type { Context, Next } from 'hono';

type AllowedRole = 'owner' | 'super-admin' | 'admin';

export const adminMiddleware = (allowedRoles: AllowedRole[]) => {
  return async (c: Context, next: Next) => {
    const authHeader =
      c.req.header('authorization') || c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json(
        {
          data: null,
          error: { message: 'Missing or invalid Authorization header' },
        },
        401
      );
    }
    const token = authHeader.replace('Bearer ', '');

    const { data, error } = await supabase.auth.getUser(token);
    if (error || !data?.user) {
      return c.json(
        { data: null, error: { message: 'Invalid or expired token' } },
        401
      );
    }

    const role = data.user.user_metadata?.role;
    if (!allowedRoles.includes(role)) {
      const rolesList = allowedRoles.join(', ');
      return c.json(
        {
          data: null,
          error: {
            message: `Forbidden: Only ${rolesList} roles allowed`,
          },
        },
        403
      );
    }

    c.set('user', data.user);
    await next();
  };
};
