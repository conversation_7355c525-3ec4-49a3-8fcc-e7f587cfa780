import type { MiddlewareHandler } from 'hono';
import { getBearerToken } from '../../utils/getBearerToken';
import { supabase } from '../../utils/supabase';

export const authValidator: MiddlewareHandler = async (c, next) => {
  const token = getBearerToken(c);

  if (!token) {
    return c.json(
      { data: null, error: { message: 'Missing Authorization header' } },
      401
    );
  }

  const {
    data: { user },
    error,
  } = await supabase.auth.getUser(token);

  if (error || !user) {
    return c.json(
      { data: null, error: { message: 'Invalid or expired token' } },
      401
    );
  }

  c.set('user', user);
  await next();
};
