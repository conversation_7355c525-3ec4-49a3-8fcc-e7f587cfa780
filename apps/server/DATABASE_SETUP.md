# Database Migration Setup Guide

## 🔧 Setup Instructions

### 1. Environment Configuration

```bash
# Copy the example environment file
cp .env.example .env

# Fill in your actual database credentials:
# - SUPABASE_URL: Your Supabase project URL
# - SUPABASE_ANON_KEY: Your Supabase anonymous key
# - SUPABASE_SERVICE_ROLE_KEY: Your Supabase service role key
# - SUPABASE_DB_URI: Your PostgreSQL connection string
# - STRIPE_SECRET_KEY: Your Stripe secret key
# - STRIPE_PUBLISHABLE_KEY: Your Stripe publishable key
# - STRIPE_WEBHOOK_SECRET: Your Stripe webhook secret
```

### 2. Database Migration Commands

```bash
# Generate migrations from schema changes
bun run db:generate

# Apply migrations to database
bun run db:migrate

# Open Drizzle Studio (database browser)
bun run db:studio

# Setup database (generate + migrate)
bun run db:migrate

# Check migration status
bun run db:check
```

## 🚨 Migration Issues - RESOLVED

The following migration issues have been fixed:

### ✅ **Fixed Issues:**

1. **Missing Environment Variables** - Added required Stripe environment variables
2. **Migration Journal Corruption** - Regenerated clean migration state
3. **Schema Inconsistencies** - Resolved data type conflicts in foreign keys
4. **Missing Snapshot Files** - Created proper snapshot chain
5. **Broken Migration Commands** - All commands now work correctly

### 🔄 **Migration Reset Process Applied:**

The migration state has been reset and regenerated with:

- Clean migration journal (`_journal.json`)
- Single comprehensive migration file (`0000_polite_dust.sql`)
- Consistent schema definitions across all tables
- Proper foreign key relationships (UUID references)

## 📁 Current Database Schema

The database now includes the following tables:

- `users` - User accounts with roles and studio association
- `studios` - Studio/organization data
- `horses` - Horse management with status tracking
- `instructors` - Instructor profiles and specializations
- `students` - Student profiles with parent relationships
- `lessons` - Lesson scheduling and management
- `lesson_enrollments` - Student-lesson relationships
- `lesson_instructors` - Instructor-lesson assignments
- `arenas` - Facility management
- `curriculum` - Course content and structure
- `skills` - Student skill levels
- `stripe_customers` - Stripe payment integration
- `invoices` - Billing and payment tracking

## 🛠 Troubleshooting

### Migration Fails with "ENOTFOUND"

This indicates network connectivity issues with Supabase. Check:

- Your internet connection
- Supabase service status
- Database URL format in `.env`

### "Permission Denied" Errors

Ensure your Supabase service role key has the correct permissions:

- Database read/write access
- Schema modification permissions

### Schema Conflicts

If you encounter schema conflicts:

1. Run `bun run db:generate` to create new migration
2. Review the generated SQL carefully
3. Apply with `bun run db:migrate`

## 🔄 Team Workflow

### For New Team Members:

1. Clone the repository
2. Copy `.env.example` to `.env` and fill in credentials
3. Run `bun install` to install dependencies
4. Run `bun run db:migrate` to apply existing migrations
5. Verify setup with `bun run db:studio`

### For Schema Changes:

1. Modify schema files in `src/db/schema/`
2. Run `bun run db:generate` to create migration
3. Review generated migration file
4. Test migration with `bun run db:migrate`
5. Commit both schema and migration files

## 📋 Environment Variables Required

```env
# Database
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
SUPABASE_DB_URI=postgresql://postgres:<EMAIL>:5432/postgres

# External Services
GHL_CLIENT_ID=your_ghl_client_id
GHL_CLIENT_SECRET=your_ghl_client_secret
GHL_BASE_URL=https://services.leadconnectorhq.com
RESEND_API_KEY=your_resend_api_key

# Payment Processing
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

---

## ✅ **MIGRATION SYSTEM SUCCESSFULLY FIXED AND TESTED!**

**Status: All database migrations are now working correctly!**

✅ **Migration generation:** `bun run db:generate` - Working  
✅ **Migration application:** `bun run db:migrate` - Working  
✅ **Database connectivity:** Confirmed working with Supabase  
✅ **Schema consistency:** All tables and relationships verified  
✅ **Team ready:** Environment setup and documentation complete

**Last tested:** Successfully applied migration `0000_polite_dust.sql` with all tables, enums, and foreign key constraints.

The migration system is fully functional and ready for team use!
