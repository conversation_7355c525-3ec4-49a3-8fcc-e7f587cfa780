import { createClient } from '@supabase/supabase-js';
import { eq } from 'drizzle-orm';
import type { SignupInput } from '../../src/apis/auth/validators/signUp.validator';
import { db } from '../../src/db';
import { studioTable } from '../../src/db/schema/studio';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceRoleKey) {
  throw new Error('Missing Supabase environment variables');
}

function transformEmailWithStudioId(email: string, studioId: string): string {
  const [localPart, domain] = email.split('@');
  return `${localPart}+${studioId}@${domain}`;
}
export const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

export async function createTestUserByPayload(
  payload: SignupInput,
  studio_id: string,
  role: string = 'parent'
) {
  try {
    const transformedEmail = transformEmailWithStudioId(
      payload.email,
      studio_id
    );
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: transformedEmail,
      password: payload.password,
      options: {
        data: {
          role: role,
        },
      },
    });

    if (authError) {
      throw new Error(`Failed to create auth user: ${authError.message}`);
    }

    const userId = authData.user?.id as string;

    const { password, ...userData } = payload;
    const { error: insertError } = await supabase.from('users').insert({
      id: userId,
      ...userData,
      email: payload.email,
      role: role,
      studio_id,
    });

    if (insertError) {
      await supabase.auth.admin.deleteUser(userId);
      throw new Error(`Failed to insert user in DB: ${insertError.message}`);
    }

    return {
      success: true,
      userId,
      email: payload.email,
    };
  } catch (error) {
    console.error('Error creating test user:', error);
    throw error;
  }
}

export async function deleteTestUserByEmail(email: string, studio_id: string) {
  console.log(`Starting deletion process for user with email: ${email}`);

  try {
    const { data, error } = await supabase.auth.admin.listUsers({
      perPage: 100,
    });

    if (error) {
      throw error;
    }

    const transformedEmail = transformEmailWithStudioId(email, studio_id);

    const user = data.users.find((u) => u.email === transformedEmail);

    const userId = user?.id;

    const { error: dbDeleteError } = await supabase
      .from('users')
      .delete()
      .eq('id', userId);
    if (dbDeleteError) {
      throw error;
    }

    const { error: authDeleteError } = await supabase.auth.admin.deleteUser(
      userId!
    );

    if (authDeleteError) {
      throw authDeleteError;
    }
    console.log(`Successfully completed deletion process for user: ${email}`);
    return { success: true };
  } catch (error) {
    throw error;
  }
}

export function getRandomUserPayload(
  overrides: Partial<SignupInput> = {}
): SignupInput {
  const defaultPayload: SignupInput = {
    email: `${Math.round(Math.random() * 10000000)}@gmail.com`,
    password: 'strongpassword123',
    first_name: 'test',
    last_name: 'Sharma',
    phone: '9876543210',
    address: '123 Main Street, Delhi',
    date_of_birth: new Date('2000-01-01'),
    profile_image: 'https://example.com/profile.jpg',
  };

  return {
    ...defaultPayload,
    ...overrides,
  };
}

export async function createTestStudio(studioId?: string) {
  const testStudioId =
    studioId || `test_studio_${Math.round(Math.random() * 10000000)}`;

  try {
    const studioData = {
      id: testStudioId,
      name: 'Test Studio',
      email: '<EMAIL>',
      phone: '+1234567890',
      address: '123 Test Street, Test City',
      timezone: 'UTC',
      currency: 'USD',
    };

    await db.insert(studioTable).values(studioData);

    console.log(`Created test studio with id: ${testStudioId}`);
    return { studioId: testStudioId };
  } catch (error) {
    throw error;
  }
}

export async function deleteTestStudio(studioId: string) {
  try {
    await db.delete(studioTable).where(eq(studioTable.id, studioId));
    console.log(`Deleted test studio with id: ${studioId}`);
    return { success: true };
  } catch (error) {
    throw error;
  }
}

export async function signInTestUser(
  email: string,
  password: string,
  studioId: string
) {
  try {
    const transformedEmail = transformEmailWithStudioId(email, studioId);

    const { data, error } = await supabase.auth.signInWithPassword({
      email: transformedEmail,
      password: password,
    });

    if (error || !data.session) {
      throw new Error(`Failed to sign in test user: ${error?.message}`);
    }

    return {
      success: true,
      accessToken: data.session.access_token,
      refreshToken: data.session.refresh_token,
      user: data.user,
    };
  } catch (error) {
    console.error('Error signing in test user:', error);
    throw error;
  }
}
