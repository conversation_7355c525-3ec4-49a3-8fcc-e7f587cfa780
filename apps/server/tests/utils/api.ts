import app from '../../src';
import { supabase } from '../../src/utils/supabase';

export class ApiTestUtils {
  // Helper to make authenticated requests
  static async makeAuthenticatedRequest(
    path: string,
    options: RequestInit & { token?: string } = {}
  ) {
    const { token, ...requestOptions } = options;

    const headers = new Headers(requestOptions.headers);
    headers.set('Content-Type', 'application/json');

    if (token) {
      headers.set('Authorization', `Bearer ${token}`);
    }

    return app.request(path, {
      ...requestOptions,
      headers,
    });
  }

  // Helper to create a test user and get auth token
  static async createTestUserWithAuth(userData: {
    email: string;
    password: string;
    name: string;
    role?: 'admin' | 'instructor' | 'student' | 'parent';
  }) {
    const { email, password, name, role = 'instructor' } = userData;

    // Create user in Supabase Auth
    const { data: authData, error: authError } =
      await supabase.auth.admin.createUser({
        email,
        password,
        email_confirm: true,
        user_metadata: {
          name,
          role,
        },
      });

    if (authError || !authData.user) {
      throw new Error(`Failed to create test user: ${authError?.message}`);
    }

    // Sign in to get tokens
    const { data: signInData, error: signInError } =
      await supabase.auth.signInWithPassword({
        email,
        password,
      });

    if (signInError || !signInData.session) {
      throw new Error(`Failed to sign in test user: ${signInError?.message}`);
    }

    return {
      user: authData.user,
      session: signInData.session,
      accessToken: signInData.session.access_token,
    };
  }

  // Helper to clean up test users
  static async cleanupTestUser(userId: string) {
    try {
      await supabase.auth.admin.deleteUser(userId);
    } catch (error) {
      console.warn('Failed to cleanup test user:', error);
    }
  }

  // Helper to make POST requests
  static async post(path: string, data: any, token?: string) {
    return this.makeAuthenticatedRequest(path, {
      method: 'POST',
      body: JSON.stringify(data),
      token,
    });
  }

  // Helper to make GET requests
  static async get(path: string, token?: string) {
    return this.makeAuthenticatedRequest(path, {
      method: 'GET',
      token,
    });
  }

  // Helper to make PUT requests
  static async put(path: string, data: any, token?: string) {
    return this.makeAuthenticatedRequest(path, {
      method: 'PUT',
      body: JSON.stringify(data),
      token,
    });
  }

  // Helper to make DELETE requests
  static async delete(path: string, token?: string) {
    return this.makeAuthenticatedRequest(path, {
      method: 'DELETE',
      token,
    });
  }

  // Helper to assert successful API response
  static assertSuccessResponse(response: Response, expectedStatus = 200) {
    if (response.status !== expectedStatus) {
      throw new Error(
        `Expected status ${expectedStatus}, got ${response.status}`
      );
    }
  }

  // Helper to assert error API response
  static assertErrorResponse(response: Response, expectedStatus: number) {
    if (response.status !== expectedStatus) {
      throw new Error(
        `Expected error status ${expectedStatus}, got ${response.status}`
      );
    }
  }

  // Helper to parse JSON response
  static async parseJsonResponse(response: Response) {
    const text = await response.text();
    try {
      return JSON.parse(text);
    } catch (error) {
      throw new Error(`Failed to parse JSON response: ${text}`);
    }
  }
}
