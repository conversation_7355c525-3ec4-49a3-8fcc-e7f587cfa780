import { describe, test, expect, beforeAll, afterAll } from 'bun:test';
import { app } from '../../../src';
import {
  createTestStudio,
  deleteTestStudio,
  createTestUserByPayload,
  deleteTestUserByEmail,
  getRandomUserPayload,
} from '../../utils/auth-test-utils';

let testStudioId: string;
let parentPayload: any;

describe('Auth verify - success', () => {
  beforeAll(async () => {
    try {
      const result = await createTestStudio();
      testStudioId = result.studioId;
    } catch (error) {
      throw new Error('Failed to create test studio');
    }
    parentPayload = getRandomUserPayload();
  });

  afterAll(async () => {
    try {
      await deleteTestUserByEmail(parentPayload.email, testStudioId);
      await deleteTestStudio(testStudioId);
    } catch (error) {
      throw new Error('Error in cleanup');
    }
  });

  test('should successfully handle verify operation', async () => {
    await createTestUserByPayload(parentPayload, testStudioId);

    console.log(`Testing verify with email: ${parentPayload.email}`);
    console.log(`Using studio ID: ${testStudioId}`);

    const loginData = await app.request(
      `api/v1/auth/signin?studio_id=${testStudioId}`,
      {
        method: 'POST',
        body: JSON.stringify({
          email: parentPayload.email,
          password: parentPayload.password,
        }),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );

    const userData = await loginData.json();
    const accessToken = userData.data.accessToken;

    const res = await app.request('api/v1/auth/verify', {
      method: 'GET',
      headers: new Headers({
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      }),
    });

    expect(res.status).toBe(200);
    const result = await res.json();

    expect(result.error).toBeNull();
    expect(result.data).toBeObject();
    expect(result.data.email).toBe(parentPayload.email);
  });
});
