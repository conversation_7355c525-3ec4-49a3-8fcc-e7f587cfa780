import { describe, test, expect } from 'bun:test';
import { app } from '../../../src';

describe('Auth verify - error', () => {
  const invalidToken = '310e08b1-1dc3-44e9-8120-5f818ea197db';
  test('should handle errors for missing authorization header', async () => {
    const res = await app.request('api/v1/auth/verify', {
      method: 'GET',
      headers: new Headers({ 'Content-Type': 'application/json' }),
    });
    expect(res.status).toBe(401);
    const { data, error } = await res.json();

    expect(data).toBeNull();
    expect(error).toBeDefined();
    expect(error.message).toBe('Missing Authorization header');
  });

  test('should handle errors for invalid authorization header', async () => {
    const res = await app.request('api/v1/auth/verify', {
      method: 'GET',
      headers: new Headers({
        'Content-Type': 'application/json',
        Authorization: `Bear<PERSON> ${invalidToken}`,
      }),
    });
    expect(res.status).toBe(401);
    const { data, error } = await res.json();

    expect(data).toBeNull();
    expect(error).toBeDefined();
    expect(error.message).toBe('Invalid or expired token');
  });
});
