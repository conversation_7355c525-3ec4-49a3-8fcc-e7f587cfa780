import { describe, test, expect, beforeAll, afterAll } from 'bun:test';
import { app } from '../../../src';
import {
  createTestStudio,
  deleteTestStudio,
} from '../../utils/auth-test-utils';

let testStudioId: string;

describe('Auth signUp - validation', () => {
  beforeAll(async () => {
    try {
      const result = await createTestStudio();
      testStudioId = result.studioId;
    } catch (error) {
      throw new Error('Failed to create test studio');
    }
  });

  afterAll(async () => {
    try {
      if (testStudioId) {
        await deleteTestStudio(testStudioId);
      }
    } catch (error) {
      throw new Error('Failed to delete test studio');
    }
  });

  test('should return 400 on invalid email format', async () => {
    const invalidPayload = {
      email: 'invalid-email-format',
      password: 'securePassword123',
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
    };

    const res = await app.request(
      `api/v1/auth/signup?studio_id=${testStudioId}`,
      {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );
    expect(res.status).toBe(400);
    const { success, error } = await res.json();

    expect(success).toBeFalse();
    expect(error).toBeDefined();
    expect(error.issues).toBeArray();
    expect(error.issues[0].message).toBe('Invalid email');
  });

  test('should return 400 on password too short', async () => {
    const invalidPayload = {
      email: '<EMAIL>',
      password: '123',
      first_name: 'John',
      last_name: 'Doe',
    };

    const res = await app.request(
      `api/v1/auth/signup?studio_id=${testStudioId}`,
      {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );
    expect(res.status).toBe(400);
    const result = await res.json();
    const { success, error } = result;

    expect(success).toBeFalse();
    expect(error).toBeDefined();
    expect(error.issues).toBeArray();
    expect(error.issues[0].message).toBe(
      'String must contain at least 6 character(s)'
    );
  });

  test('should return 400 on missing required fields', async () => {
    const invalidPayload = {
      email: '<EMAIL>',
      password: 'validpassword',
      // Missing first_name and last_name
    };

    const res = await app.request(
      `api/v1/auth/signup?studio_id=${testStudioId}`,
      {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );
    expect(res.status).toBe(400);
    const { success, error } = await res.json();

    expect(success).toBeFalse();
    expect(error).toBeDefined();
    expect(error.issues).toBeArray();
    expect(error.issues.length).toBeGreaterThan(1); // Should have multiple validation errors
  });

  test('should return 400 on invalid phone number length', async () => {
    const invalidPayload = {
      email: '<EMAIL>',
      password: 'validpassword',
      first_name: 'John',
      last_name: 'Doe',
      phone: '123',
    };

    const res = await app.request(
      `api/v1/auth/signup?studio_id=${testStudioId}`,
      {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );
    expect(res.status).toBe(400);
    const { success, error } = await res.json();

    expect(success).toBeFalse();
    expect(error).toBeDefined();
    expect(error.issues).toBeArray();
    expect(error.issues[0].message).toBe(
      'String must contain at least 7 character(s)'
    );
  });

  test('should return 400 on invalid profile image URL', async () => {
    const invalidPayload = {
      email: '<EMAIL>',
      password: 'validpassword',
      first_name: 'John',
      last_name: 'Doe',
      profile_image: 'not-a-valid-url',
    };

    const res = await app.request(
      `api/v1/auth/signup?studio_id=${testStudioId}`,
      {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );
    expect(res.status).toBe(400);
    const { success, error } = await res.json();

    expect(success).toBeFalse();
    expect(error).toBeDefined();
    expect(error.issues).toBeArray();
    expect(error.issues[0].message).toBe('Invalid url');
  });

  test('should return 400 on invalid date format', async () => {
    const invalidPayload = {
      email: '<EMAIL>',
      password: 'validpassword',
      first_name: 'John',
      last_name: 'Doe',
      date_of_birth: 'invalid-date',
    };

    const res = await app.request(
      `api/v1/auth/signup?studio_id=${testStudioId}`,
      {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );
    expect(res.status).toBe(400);
    const { success, error } = await res.json();

    expect(success).toBeFalse();
    expect(error).toBeDefined();
    expect(error.issues).toBeArray();
    expect(error.issues[0].message).toBe('Invalid date');
  });

  test('should return 400 on invalid country code format', async () => {
    const invalidPayload = {
      email: '<EMAIL>',
      password: 'validpassword',
      first_name: 'John',
      last_name: 'Doe',
      country_code: '1', // Missing + prefix
    };

    const res = await app.request(
      `api/v1/auth/signup?studio_id=${testStudioId}`,
      {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );
    expect(res.status).toBe(400);
    const { success, error } = await res.json();

    expect(success).toBeFalse();
    expect(error).toBeDefined();
    expect(error.issues).toBeArray();
    expect(error.issues[0].message).toBe('Invalid country code');
  });

  test('should return 400 when studio_id is missing', async () => {
    const validPayload = {
      email: '<EMAIL>',
      password: 'validpassword',
      first_name: 'John',
      last_name: 'Doe',
    };

    const res = await app.request('api/v1/auth/signup', {
      method: 'POST',
      body: JSON.stringify(validPayload),
      headers: new Headers({ 'Content-Type': 'application/json' }),
    });
    expect(res.status).toBe(400);
    const { success, error } = await res.json();

    expect(success).toBeFalse();
    expect(error).toBeDefined();
    expect(error.message).toBe('Studio ID is required.');
  });

  test('should return 400 when studio_id does not exist in database', async () => {
    const validPayload = {
      email: '<EMAIL>',
      password: 'validpassword',
      first_name: 'John',
      last_name: 'Doe',
    };

    const res = await app.request(
      'api/v1/auth/signup?studio_id=non_existent_studio_123',
      {
        method: 'POST',
        body: JSON.stringify(validPayload),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );
    expect(res.status).toBe(400);
    const { success, error } = await res.json();

    expect(success).toBeFalse();
    expect(error).toBeDefined();
    expect(error.message).toBe(
      'Studio not found. The provided studio ID does not exist in our records.'
    );
  });

  test('should return 400 when studio_id is empty string', async () => {
    const validPayload = {
      email: '<EMAIL>',
      password: 'validpassword',
      first_name: 'John',
      last_name: 'Doe',
    };

    const res = await app.request('api/v1/auth/signup?studio_id=', {
      method: 'POST',
      body: JSON.stringify(validPayload),
      headers: new Headers({ 'Content-Type': 'application/json' }),
    });
    expect(res.status).toBe(400);
    const { success, error } = await res.json();

    expect(success).toBeFalse();
    expect(error).toBeDefined();
    expect(error.message).toBe('Studio ID is required.');
  });
});
