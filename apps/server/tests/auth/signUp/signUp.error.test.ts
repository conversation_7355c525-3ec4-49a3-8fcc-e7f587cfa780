import { describe, test, expect, beforeAll, afterAll } from 'bun:test';
import { app } from '../../../src';
import {
  createTestUserByPayload,
  deleteTestUserByEmail,
  getRandomUserPayload,
  createTestStudio,
  deleteTestStudio,
} from '../../utils/auth-test-utils';

describe('Auth signUp - error', () => {
  let testStudioId: string;
  let userPayload: any;

  beforeAll(async () => {
    testStudioId = (await createTestStudio()).studioId;
    userPayload = getRandomUserPayload();
    await createTestUserByPayload(userPayload, testStudioId);
  });

  afterAll(async () => {
    await deleteTestUserByEmail(userPayload.email, testStudioId);
    if (testStudioId) {
      await deleteTestStudio(testStudioId);
    }
  });

  test('should return 409 for duplicate email', async () => {
    const res = await app.request(
      `api/v1/auth/signup?studio_id=${testStudioId}`,
      {
        method: 'POST',
        body: JSON.stringify(userPayload),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );
    expect(res.status).toBe(409);
    const result = await res.json();
    const { data, error } = result;
    expect(error).toBeObject();
    expect(error.message).toBe('User with same email already exists');
    expect(error.code).toBe('DB_ERROR');
    expect(data).toBeNull();
  });
});
