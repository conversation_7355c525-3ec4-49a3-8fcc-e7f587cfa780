import { describe, expect, test, beforeAll, afterAll } from 'bun:test';
import { app } from '../../../src';
import {
  createTestStudio,
  deleteTestStudio,
  deleteTestUserByEmail,
  getRandomUserPayload,
} from '../../utils/auth-test-utils';

let testStudioId: string;
let parentPayload: any;

describe('Auth signUp - success', () => {
  beforeAll(async () => {
    try {
      const result = await createTestStudio();
      testStudioId = result.studioId;
    } catch (error) {
      throw new Error('Failed to create test studio');
    }
    parentPayload = getRandomUserPayload();
  });

  afterAll(async () => {
    try {
      await deleteTestUserByEmail(parentPayload.email, testStudioId);
      await deleteTestStudio(testStudioId);
    } catch (error) {
      throw new Error('Error in cleanup');
    }
  });

  test('should successfully handle signUp operation for parent', async () => {
    const payload = {
      email: parentPayload.email,
      password: parentPayload.password,
      first_name: parentPayload.first_name,
      last_name: parentPayload.last_name,
      phone: parentPayload.phone,
      country_code: '+1',
      address: parentPayload.address,
      date_of_birth: '1990-05-15',
      profile_image: parentPayload.profile_image,
      emergency_contact_phone: '1234567890',
      emergency_contact_name: 'Emergency Contact',
    };

    console.log(`Testing signup with email: ${payload.email}`);
    console.log(`Using studio ID: ${testStudioId}`);

    const res = await app.request(
      `api/v1/auth/signup?studio_id=${testStudioId}`,
      {
        method: 'POST',
        body: JSON.stringify(payload),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );

    expect(res.status).toBe(201);
    const result = await res.json();

    expect(result.data).toBeDefined();
    expect(result.data.message).toBe(
      'A verification email has been sent to your inbox. Please check your email to complete the signup process.'
    );
    expect(result.error).toBeNull();
  });
});
