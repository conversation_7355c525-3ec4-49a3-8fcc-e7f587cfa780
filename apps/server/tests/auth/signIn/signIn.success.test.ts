import { describe, expect, test, beforeAll, afterAll } from 'bun:test';
import { app } from '../../../src';
import {
  createTestStudio,
  deleteTestStudio,
  createTestUserByPayload,
  deleteTestUserByEmail,
  getRandomUserPayload,
} from '../../utils/auth-test-utils';

let testStudioId: string;
let parentPayload: any;

describe('Auth signIn - success', () => {
  beforeAll(async () => {
    try {
      const result = await createTestStudio();
      testStudioId = result.studioId;
    } catch (error) {
      throw new Error('Failed to create test studio');
    }
    parentPayload = getRandomUserPayload();
  });

  afterAll(async () => {
    try {
      await deleteTestUserByEmail(parentPayload.email, testStudioId);
      await deleteTestStudio(testStudioId);
    } catch (error) {
      throw new Error('Error in cleanup');
    }
  });

  test('should successfully handle signIn operation for parent', async () => {
    await createTestUserByPayload(parentPayload, testStudioId);

    console.log(`Testing signin with email: ${parentPayload.email}`);
    console.log(`Using studio ID: ${testStudioId}`);

    const res = await app.request(
      `api/v1/auth/signin?studio_id=${testStudioId}`,
      {
        method: 'POST',
        body: JSON.stringify({
          email: parentPayload.email,
          password: parentPayload.password,
        }),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );

    expect(res.status).toBe(200);
    const result = await res.json();

    expect(result.data).toBeDefined();
    expect(result.data.user).toBeObject();
    expect(result.data.user.email).toBe(parentPayload.email);
    expect(result.data.refreshToken).toBeString();
    expect(result.data.accessToken).toBeString();
    expect(result.error).toBeNull();
  });
});
