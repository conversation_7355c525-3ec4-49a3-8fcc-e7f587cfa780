import { describe, test, expect, beforeAll, afterAll } from 'bun:test';
import { app } from '../../../src';
import {
  createTestStudio,
  deleteTestStudio,
} from '../../utils/auth-test-utils';

describe('Auth signIn - validation', () => {
  let testStudioId: string;

  beforeAll(async () => {
    const studioResult = await createTestStudio();
    testStudioId = studioResult.studioId;
  });

  afterAll(async () => {
    if (testStudioId) {
      await deleteTestStudio(testStudioId);
    }
  });

  test('should return 400 for invalid email format', async () => {
    const invalidPayload = {
      email: 'not-an-email',
      password: 'password123',
    };

    const res = await app.request(
      `api/v1/auth/signin?studio_id=${testStudioId}`,
      {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );
    expect(res.status).toBe(400);
    const result = await res.json();
    const { success, error } = result;

    expect(success).toBeFalse();
    expect(error).toBeObject();
    expect(error.issues).toBeArray();
    expect(
      error.issues.some((issue: any) => issue.path.includes('email'))
    ).toBeTrue();
  });

  test('should return 400 for missing email field', async () => {
    const invalidPayload = {
      password: 'password123',
    };

    const res = await app.request(
      `api/v1/auth/signin?studio_id=${testStudioId}`,
      {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );
    expect(res.status).toBe(400);
    const result = await res.json();
    const { success, error } = result;

    expect(success).toBeFalse();
    expect(error).toBeObject();
    expect(error.issues).toBeArray();
    expect(
      error.issues.some((issue: any) => issue.path.includes('email'))
    ).toBeTrue();
  });

  test('should return 400 for missing password field', async () => {
    const invalidPayload = {
      email: '<EMAIL>',
    };

    const res = await app.request(
      `api/v1/auth/signin?studio_id=${testStudioId}`,
      {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );
    expect(res.status).toBe(400);
    const result = await res.json();
    const { success, error } = result;

    expect(success).toBeFalse();
    expect(error).toBeObject();
    expect(error.issues).toBeArray();
    expect(
      error.issues.some((issue: any) => issue.path.includes('password'))
    ).toBeTrue();
  });

  test('should return 400 for null email field', async () => {
    const invalidPayload = {
      email: null,
      password: 'password123',
    };

    const res = await app.request(
      `api/v1/auth/signin?studio_id=${testStudioId}`,
      {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );
    expect(res.status).toBe(400);
    const result = await res.json();
    const { success, error } = result;

    expect(success).toBeFalse();
    expect(error).toBeObject();
    expect(error.issues).toBeArray();
    expect(
      error.issues.some((issue: any) => issue.path.includes('email'))
    ).toBeTrue();
  });

  test('should return 400 for null password field', async () => {
    const invalidPayload = {
      email: '<EMAIL>',
      password: null,
    };

    const res = await app.request(
      `api/v1/auth/signin?studio_id=${testStudioId}`,
      {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );
    expect(res.status).toBe(400);
    const result = await res.json();
    const { success, error } = result;

    expect(success).toBeFalse();
    expect(error).toBeObject();
    expect(error.issues).toBeArray();
    expect(
      error.issues.some((issue: any) => issue.path.includes('password'))
    ).toBeTrue();
  });

  test('should return 400 for email with invalid format - missing @', async () => {
    const invalidPayload = {
      email: 'testexample.com',
      password: 'password123',
    };

    const res = await app.request(
      `api/v1/auth/signin?studio_id=${testStudioId}`,
      {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );
    expect(res.status).toBe(400);
    const result = await res.json();
    const { success, error } = result;

    expect(success).toBeFalse();
    expect(error).toBeObject();
    expect(error.issues).toBeArray();
    expect(
      error.issues.some((issue: any) => issue.path.includes('email'))
    ).toBeTrue();
  });

  test('should return 400 for email with invalid format - missing domain', async () => {
    const invalidPayload = {
      email: 'test@',
      password: 'password123',
    };

    const res = await app.request(
      `api/v1/auth/signin?studio_id=${testStudioId}`,
      {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );
    expect(res.status).toBe(400);
    const result = await res.json();
    const { success, error } = result;

    expect(success).toBeFalse();
    expect(error).toBeObject();
    expect(error.issues).toBeArray();
    expect(
      error.issues.some((issue: any) => issue.path.includes('email'))
    ).toBeTrue();
  });

  test('should return 400 for missing studio_id query parameter', async () => {
    const validPayload = {
      email: '<EMAIL>',
      password: 'password123',
    };

    const res = await app.request('api/v1/auth/signin', {
      method: 'POST',
      body: JSON.stringify(validPayload),
      headers: new Headers({ 'Content-Type': 'application/json' }),
    });
    expect(res.status).toBe(400);
    const result = await res.json();
    const { success, error } = result;

    expect(success).toBeFalse();
    expect(error).toBeObject();
    expect(error.message).toBe('Studio ID is required.');
  });

  test('should return 400 for non-existent studio_id', async () => {
    const validPayload = {
      email: '<EMAIL>',
      password: 'password123',
    };

    const nonExistentStudioId = 'non_existent_studio_123';
    const res = await app.request(
      `api/v1/auth/signin?studio_id=${nonExistentStudioId}`,
      {
        method: 'POST',
        body: JSON.stringify(validPayload),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );
    expect(res.status).toBe(400);
    const result = await res.json();
    const { success, error } = result;

    expect(success).toBeFalse();
    expect(error).toBeObject();
    expect(error.message).toBe(
      'Studio not found. The provided studio ID does not exist in our records.'
    );
  });

  test('should return 400 for completely empty request body', async () => {
    const res = await app.request(
      `api/v1/auth/signin?studio_id=${testStudioId}`,
      {
        method: 'POST',
        body: '',
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );
    expect(res.status).toBe(400);
  });
});
