import { describe, expect, test, beforeAll, afterAll } from 'bun:test';
import { app } from '../../../src';
import {
  createTestStudio,
  deleteTestStudio,
  deleteTestUserByEmail,
} from '../../utils/auth-test-utils';

describe('Auth signIn - error', () => {
  let testStudioId: string;
  let createdUserEmails: string[] = [];

  beforeAll(async () => {
    const studioResult = await createTestStudio();
    testStudioId = studioResult.studioId;
  });

  afterAll(async () => {
    for (const email of createdUserEmails) {
      await deleteTestUserByEmail(email, testStudioId);
    }

    if (testStudioId) {
      await deleteTestStudio(testStudioId);
    }
  });

  test('should return 400 for invalid login credentials', async () => {
    const res = await app.request(
      `api/v1/auth/signin?studio_id=${testStudioId}`,
      {
        method: 'POST',
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'anypassword123',
        }),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );
    expect(res.status).toBe(400);
    const result = await res.json();
    const { data, error } = result;

    expect(data).toBeNull();
    expect(error).toBeObject();
    expect(error.message).toBe('Invalid login credentials');
  });
});
