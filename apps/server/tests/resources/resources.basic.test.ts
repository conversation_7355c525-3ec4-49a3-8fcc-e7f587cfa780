import { describe, test, expect } from 'bun:test';
import { app } from '../../src';

describe('Resource APIs - Basic Tests', () => {
  test('should return 401 for unauthenticated request to get arenas', async () => {
    const response = await app.request('api/v1/arenas', {
      method: 'GET',
      headers: new Headers({ 'Content-Type': 'application/json' }),
    });

    expect(response.status).toBe(401);
    const responseData = await response.json();
    expect(responseData.error).toBeDefined();
    expect(responseData.error.message).toContain(
      'Missing Authorization header'
    );
  });

  test('should return 401 for unauthenticated request to get instructors', async () => {
    const response = await app.request('api/v1/instructors', {
      method: 'GET',
      headers: new Headers({ 'Content-Type': 'application/json' }),
    });

    expect(response.status).toBe(401);
    const responseData = await response.json();
    expect(responseData.error).toBeDefined();
    expect(responseData.error.message).toContain(
      'Missing or invalid Authorization header'
    );
  });

  test('should return 401 for unauthenticated request to get curriculum', async () => {
    const response = await app.request('api/v1/curriculum', {
      method: 'GET',
      headers: new Headers({ 'Content-Type': 'application/json' }),
    });

    expect(response.status).toBe(401);
    const responseData = await response.json();
    expect(responseData.error).toBeDefined();
    expect(responseData.error.message).toContain(
      'Missing Authorization header'
    );
  });

  test('should return 401 for invalid token on arenas endpoint', async () => {
    const response = await app.request('api/v1/arenas', {
      method: 'GET',
      headers: new Headers({
        'Content-Type': 'application/json',
        Authorization: 'Bearer fake-token',
      }),
    });

    expect(response.status).toBe(401);
    const responseData = await response.json();
    expect(responseData.error).toBeDefined();
    expect(responseData.error.message).toContain('Invalid or expired token');
  });

  test('should return 401 for invalid token on instructors endpoint', async () => {
    const response = await app.request('api/v1/instructors', {
      method: 'GET',
      headers: new Headers({
        'Content-Type': 'application/json',
        Authorization: 'Bearer fake-token',
      }),
    });

    expect(response.status).toBe(401);
    const responseData = await response.json();
    expect(responseData.error).toBeDefined();
    expect(responseData.error.message).toContain('Invalid or expired token');
  });

  test('should return 401 for invalid token on curriculum endpoint', async () => {
    const response = await app.request('api/v1/curriculum', {
      method: 'GET',
      headers: new Headers({
        'Content-Type': 'application/json',
        Authorization: 'Bearer fake-token',
      }),
    });

    expect(response.status).toBe(401);
    const responseData = await response.json();
    expect(responseData.error).toBeDefined();
    expect(responseData.error.message).toContain('Invalid or expired token');
  });
});
