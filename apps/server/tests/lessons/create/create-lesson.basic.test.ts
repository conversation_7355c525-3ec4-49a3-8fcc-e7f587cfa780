import { describe, test, expect, beforeAll, afterAll } from 'bun:test';
import { app } from '../../../src';
import postgres from 'postgres';
import { env } from '../../../src/config/env';

// Test database connection
const client = postgres(env.SUPABASE_DB_URI ?? '', { prepare: false });

let testUserId: string;
let testStudioId: string;
let testArenaId: number;
let authToken: string;
let createdLessonIds: number[] = [];

describe('Lesson Creation - CRUD Tests', () => {
  beforeAll(async () => {
    console.log('🔧 Setting up lesson creation tests...');

    // Get test user (UUID)
    const users = await client`SELECT id FROM users LIMIT 1;`;
    if (users.length === 0) {
      throw new Error(
        'No users found in database. Please ensure test data exists.'
      );
    }
    testUserId = users[0]?.id;

    // Get test studio
    const studios = await client`SELECT id FROM studios LIMIT 1;`;
    testStudioId = studios[0]?.id;

    // Create test arena
    const [arena] = await client`
      INSERT INTO arenas (name, location, capacity)
      VALUES ('Test Create Arena', 'Test Location', 8)
      RETURNING id;
    `;
    testArenaId = arena?.id;

    // Create auth token (mock - in real tests you'd get this from auth)
    authToken = 'Bearer test-token-for-create-tests';

    console.log(`✅ Test user: ${testUserId}`);
    console.log(`✅ Test arena: ${testArenaId}`);
  });

  afterAll(async () => {
    console.log('🧹 Cleaning up lesson creation tests...');

    // Clean up created lessons
    if (createdLessonIds.length > 0) {
      await client`DELETE FROM lesson_instructors WHERE lesson_id = ANY(${createdLessonIds});`;
      await client`DELETE FROM lessons WHERE id = ANY(${createdLessonIds});`;
    }

    // Clean up test arena
    if (testArenaId) {
      await client`DELETE FROM arenas WHERE id = ${testArenaId};`;
    }

    await client.end();
    console.log('✅ Cleanup complete');
  });
  test('should return 401 for unauthenticated request', async () => {
    const lessonPayload = {
      title: 'Test Lesson',
      lesson_type: 'single-lesson',
      date: '2025-12-01',
      start_time: '2025-12-01T10:00:00Z',
      end_time: '2025-12-01T11:00:00Z',
      duration_minutes: 60,
      max_students: 6,
      instructor_ids: ['00000000-0000-0000-0000-000000000000'], // Invalid UUID for unauthenticated test
    };

    const response = await app.request('/api/v1/lessons', {
      method: 'POST',
      body: JSON.stringify(lessonPayload),
      headers: { 'Content-Type': 'application/json' },
    });

    expect(response.status).toBe(401);
    const responseData = await response.json();
    expect(responseData.error).toBeDefined();
    expect(responseData.error.message).toContain(
      'Missing Authorization header'
    );
  });

  test('should return 401 for invalid token', async () => {
    const validPayload = {
      title: 'Test Lesson',
      lesson_type: 'single-lesson',
      date: '2025-12-01',
      start_time: '2025-12-01T10:00:00Z',
      end_time: '2025-12-01T11:00:00Z',
      duration_minutes: 60,
      max_students: 6,
      instructor_ids: ['00000000-0000-0000-0000-000000000000'], // Invalid UUID for invalid token test
    };

    const response = await app.request('/api/v1/lessons', {
      method: 'POST',
      body: JSON.stringify(validPayload),
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer fake-token',
      },
    });

    expect(response.status).toBe(401);
    const responseData = await response.json();
    expect(responseData.error).toBeDefined();
    expect(responseData.error.message).toContain('Invalid or expired token');
  });

  test('should validate required fields', async () => {
    const invalidPayload = {
      // Missing required fields
      lesson_type: 'single-lesson',
    };

    const response = await app.request('/api/v1/lessons', {
      method: 'POST',
      body: JSON.stringify(invalidPayload),
      headers: { 'Content-Type': 'application/json' },
    });

    expect(response.status).toBe(401); // Will be 401 due to missing auth, but validates structure
  });

  test('should validate UUID format for instructor_ids', async () => {
    const invalidUuidPayload = {
      title: 'Test Lesson',
      lesson_type: 'single-lesson',
      date: '2025-12-01',
      start_time: '2025-12-01T10:00:00Z',
      end_time: '2025-12-01T11:00:00Z',
      duration_minutes: 60,
      max_students: 6,
      instructor_ids: ['invalid-uuid', 'another-invalid-uuid'], // Invalid UUID format
    };

    const response = await app.request('/api/v1/lessons', {
      method: 'POST',
      body: JSON.stringify(invalidUuidPayload),
      headers: { 'Content-Type': 'application/json' },
    });

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });

  test('should validate date formats', async () => {
    const invalidDatePayload = {
      title: 'Test Lesson',
      lesson_type: 'single-lesson',
      date: 'invalid-date',
      start_time: 'invalid-time',
      end_time: 'invalid-time',
      duration_minutes: 60,
      max_students: 6,
      instructor_ids: [testUserId || '00000000-0000-0000-0000-000000000000'],
    };

    const response = await app.request('/api/v1/lessons', {
      method: 'POST',
      body: JSON.stringify(invalidDatePayload),
      headers: { 'Content-Type': 'application/json' },
    });

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });

  test('should validate numeric fields', async () => {
    const invalidNumericPayload = {
      title: 'Test Lesson',
      lesson_type: 'single-lesson',
      date: '2025-12-01',
      start_time: '2025-12-01T10:00:00Z',
      end_time: '2025-12-01T11:00:00Z',
      duration_minutes: 'invalid', // Should be number
      max_students: 'invalid', // Should be number
      instructor_ids: [testUserId || '00000000-0000-0000-0000-000000000000'],
    };

    const response = await app.request('/api/v1/lessons', {
      method: 'POST',
      body: JSON.stringify(invalidNumericPayload),
      headers: { 'Content-Type': 'application/json' },
    });

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });

  test('should validate lesson_type enum', async () => {
    const invalidLessonTypePayload = {
      title: 'Test Lesson',
      lesson_type: 'invalid-lesson-type', // Should be valid enum value
      date: '2025-12-01',
      start_time: '2025-12-01T10:00:00Z',
      end_time: '2025-12-01T11:00:00Z',
      duration_minutes: 60,
      max_students: 6,
      instructor_ids: [testUserId || '00000000-0000-0000-0000-000000000000'],
    };

    const response = await app.request('/api/v1/lessons', {
      method: 'POST',
      body: JSON.stringify(invalidLessonTypePayload),
      headers: { 'Content-Type': 'application/json' },
    });

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });

  test('should validate instructor_ids array is not empty', async () => {
    const emptyInstructorsPayload = {
      title: 'Test Lesson',
      lesson_type: 'single-lesson',
      date: '2025-12-01',
      start_time: '2025-12-01T10:00:00Z',
      end_time: '2025-12-01T11:00:00Z',
      duration_minutes: 60,
      max_students: 6,
      instructor_ids: [], // Empty array should be invalid
    };

    const response = await app.request('/api/v1/lessons', {
      method: 'POST',
      body: JSON.stringify(emptyInstructorsPayload),
      headers: { 'Content-Type': 'application/json' },
    });

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });
});
