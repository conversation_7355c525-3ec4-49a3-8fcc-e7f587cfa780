import { describe, test, expect, beforeAll, afterAll } from 'bun:test';
import { app } from '../../../src';
import postgres from 'postgres';
import { env } from '../../../src/config/env';

// Test database connection
const client = postgres(env.SUPABASE_DB_URI ?? '', { prepare: false });

let testUserId: string;
let testLessonId: number;
let testArenaId: number;

describe('Update Lesson - CRUD Tests', () => {
  beforeAll(async () => {
    console.log('🔧 Setting up update lesson tests...');

    // Get test user (UUID)
    const users = await client`SELECT id FROM users LIMIT 1;`;
    testUserId = users[0]?.id;

    // Create test arena
    const [arena] = await client`
      INSERT INTO arenas (name, location, capacity)
      VALUES ('Test Update Arena', 'Test Location', 8)
      RETURNING id;
    `;
    testArenaId = arena?.id;

    // Create test lesson
    const [lesson] = await client`
      INSERT INTO lessons (
        title, lesson_type, arena_id, date, start_time, end_time,
        duration_minutes, max_students, current_students, status,
        created_by, curriculum_items, attachments
      ) VALUES (
        'Test Update Lesson',
        'single-lesson',
        ${testArenaId},
        ${new Date(Date.now() + 24 * 60 * 60 * 1000)},
        ${new Date(Date.now() + 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000)},
        ${new Date(Date.now() + 24 * 60 * 60 * 1000 + 3 * 60 * 60 * 1000)},
        60, 6, 0, 'scheduled', ${testUserId}, '[]', '[]'
      ) RETURNING id;
    `;
    testLessonId = lesson?.id;

    console.log(`✅ Test lesson: ${testLessonId}`);
  });

  afterAll(async () => {
    console.log('🧹 Cleaning up update lesson tests...');

    if (testLessonId) {
      await client`DELETE FROM lessons WHERE id = ${testLessonId};`;
    }
    if (testArenaId) {
      await client`DELETE FROM arenas WHERE id = ${testArenaId};`;
    }

    await client.end();
    console.log('✅ Cleanup complete');
  });
  test('should return 401 for unauthenticated request', async () => {
    const updatePayload = {
      title: 'Updated Lesson Title',
      max_students: 8,
    };

    const response = await app.request('api/v1/lessons/1', {
      method: 'PUT',
      body: JSON.stringify(updatePayload),
      headers: new Headers({ 'Content-Type': 'application/json' }),
    });

    expect(response.status).toBe(401);
    const responseData = await response.json();
    expect(responseData.error).toBeDefined();
    expect(responseData.error.message).toContain(
      'Missing Authorization header'
    );
  });

  test('should return 401 for invalid token', async () => {
    const updatePayload = {
      title: 'Updated Lesson Title',
      max_students: 8,
    };

    const response = await app.request('api/v1/lessons/1', {
      method: 'PUT',
      body: JSON.stringify(updatePayload),
      headers: new Headers({
        'Content-Type': 'application/json',
        Authorization: 'Bearer fake-token',
      }),
    });

    expect(response.status).toBe(401);
    const responseData = await response.json();
    expect(responseData.error).toBeDefined();
    expect(responseData.error.message).toContain('Invalid or expired token');
  });

  test('should return 401 for invalid token with invalid lesson ID', async () => {
    const updatePayload = {
      title: 'Updated Lesson Title',
    };

    const response = await app.request('api/v1/lessons/invalid-id', {
      method: 'PUT',
      body: JSON.stringify(updatePayload),
      headers: new Headers({
        'Content-Type': 'application/json',
        Authorization: 'Bearer fake-token',
      }),
    });

    expect(response.status).toBe(401);
    const responseData = await response.json();
    expect(responseData.error).toBeDefined();
    expect(responseData.error.message).toContain('Invalid or expired token');
  });

  test('should validate update payload fields', async () => {
    const invalidUpdatePayload = {
      title: '', // Empty title should be invalid
      max_students: -1, // Negative number should be invalid
      duration_minutes: 'invalid', // Should be number
    };

    const response = await app.request(`/api/v1/lessons/${testLessonId || 1}`, {
      method: 'PUT',
      body: JSON.stringify(invalidUpdatePayload),
      headers: { 'Content-Type': 'application/json' },
    });

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });

  test('should validate instructor_ids UUID format in update', async () => {
    const invalidInstructorUpdate = {
      instructor_ids: ['invalid-uuid', 'another-invalid-uuid'],
    };

    const response = await app.request(`/api/v1/lessons/${testLessonId || 1}`, {
      method: 'PUT',
      body: JSON.stringify(invalidInstructorUpdate),
      headers: { 'Content-Type': 'application/json' },
    });

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });

  test('should validate date formats in update', async () => {
    const invalidDateUpdate = {
      date: 'invalid-date',
      start_time: 'invalid-time',
      end_time: 'invalid-time',
    };

    const response = await app.request(`/api/v1/lessons/${testLessonId || 1}`, {
      method: 'PUT',
      body: JSON.stringify(invalidDateUpdate),
      headers: { 'Content-Type': 'application/json' },
    });

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });

  test('should validate lesson status enum in update', async () => {
    const invalidStatusUpdate = {
      status: 'invalid-status', // Should be valid enum value
    };

    const response = await app.request(`/api/v1/lessons/${testLessonId || 1}`, {
      method: 'PUT',
      body: JSON.stringify(invalidStatusUpdate),
      headers: { 'Content-Type': 'application/json' },
    });

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });

  test('should validate arena_id format in update', async () => {
    const invalidArenaUpdate = {
      arena_id: 'invalid-arena-id', // Should be number
    };

    const response = await app.request(`/api/v1/lessons/${testLessonId || 1}`, {
      method: 'PUT',
      body: JSON.stringify(invalidArenaUpdate),
      headers: { 'Content-Type': 'application/json' },
    });

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });

  test('should handle partial updates', async () => {
    const partialUpdate = {
      title: 'Partially Updated Lesson',
      // Only updating title, other fields should remain unchanged
    };

    const response = await app.request(`/api/v1/lessons/${testLessonId || 1}`, {
      method: 'PUT',
      body: JSON.stringify(partialUpdate),
      headers: { 'Content-Type': 'application/json' },
    });

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });
});
