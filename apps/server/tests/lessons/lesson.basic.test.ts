import { describe, test, expect, beforeAll, afterAll } from 'bun:test';
import { app } from '../../src';
import postgres from 'postgres';
import { env } from '../../src/config/env';
import {
  createTestStudio,
  deleteTestStudio,
  createTestUserByPayload,
  deleteTestUserByEmail,
  getRandomUserPayload,
  signInTestUser,
} from '../utils/auth-test-utils';

// Test database connection
const client = postgres(env.SUPABASE_DB_URI ?? '', { prepare: false });

let testStudioId: string;
let testUserId: string;
let testArenaId: number;
let authToken: string;
let createdLessonIds: number[] = [];
let createdStudentIds: string[] = [];
let testInstructorPayload: any;

describe('Lesson Module - Basic CRUD Operations', () => {
  beforeAll(async () => {
    console.log('🔧 Setting up lesson basic tests...');

    // Create test studio
    const studioResult = await createTestStudio();
    testStudioId = studioResult.studioId;

    // Create test instructor user
    testInstructorPayload = getRandomUserPayload({
      email: `instructor-${Math.random().toFixed(6)}@test.com`,
    });

    const instructorResult = await createTestUserByPayload(
      testInstructorPayload,
      testStudioId,
      'instructor'
    );
    testUserId = instructorResult.userId;

    // Create test arena
    const [arena] = await client`
      INSERT INTO arenas (name, location, capacity)
      VALUES ('Test Basic Arena', 'Test Location', 8)
      RETURNING id;
    `;
    testArenaId = arena?.id;

    // Sign in to get real auth token
    const signInResult = await signInTestUser(
      testInstructorPayload.email,
      testInstructorPayload.password,
      testStudioId
    );
    authToken = `Bearer ${signInResult.accessToken}`;

    console.log(`✅ Test studio: ${testStudioId}`);
    console.log(`✅ Test instructor: ${testUserId}`);
    console.log(`✅ Test arena: ${testArenaId}`);
  });

  afterAll(async () => {
    console.log('🧹 Cleaning up lesson basic tests...');

    try {
      // Clean up in reverse order of creation to avoid foreign key constraints

      // 1. Clean up enrollments first
      if (createdLessonIds.length > 0) {
        await client`DELETE FROM lesson_enrollments WHERE lesson_id = ANY(${createdLessonIds});`;
        await client`DELETE FROM lesson_instructors WHERE lesson_id = ANY(${createdLessonIds});`;
      }

      // 2. Clean up lessons (now that enrollments/instructors are gone)
      if (createdLessonIds.length > 0) {
        await client`DELETE FROM lessons WHERE id = ANY(${createdLessonIds});`;
      }

      // 3. Clean up students
      if (createdStudentIds.length > 0) {
        await client`DELETE FROM students WHERE id = ANY(${createdStudentIds});`;
      }

      // 4. Force delete lesson_instructors before deleting lessons
      await client`DELETE FROM lesson_instructors WHERE lesson_id = ANY(${createdLessonIds});`;

      // 5. Force delete any remaining lessons that might reference the arena
      await client`DELETE FROM lessons WHERE arena_id = ${testArenaId};`;

      // 6. Clean up arena (now that lessons are gone)
      if (testArenaId) {
        await client`DELETE FROM arenas WHERE id = ${testArenaId};`;
      }

      // 7. Clean up test users
      if (testInstructorPayload?.email) {
        await deleteTestUserByEmail(testInstructorPayload.email, testStudioId);
      }

      // 8. Clean up studio
      await deleteTestStudio(testStudioId);
    } catch (error) {
      console.error('Cleanup error:', error);
    }

    await client.end();
    console.log('✅ Cleanup complete');
  });

  test('should create a single lesson successfully', async () => {
    const lessonPayload = {
      title: 'Basic Riding Lesson',
      lesson_type: 'single-lesson',
      arena_id: testArenaId,
      date: '2025-12-01',
      start_time: '2025-12-01T10:00:00Z',
      end_time: '2025-12-01T11:00:00Z',
      duration_minutes: 60,
      max_students: 6,
      notes: 'Basic riding lesson for beginners',
      require_form: true,
      require_payment: true,
      price: 50.0,
      instructor_ids: [testUserId],
    };

    const response = await app.request('/api/v1/lessons', {
      method: 'POST',
      body: JSON.stringify(lessonPayload),
      headers: {
        'Content-Type': 'application/json',
        Authorization: authToken,
      },
    });

    expect(response.status).toBe(201);
    const responseData = await response.json();

    expect(responseData.data).toBeDefined();
    expect(responseData.data.lesson).toBeDefined();
    expect(responseData.data.lesson.title).toBe(lessonPayload.title);
    expect(responseData.data.lesson.lesson_type).toBe(
      lessonPayload.lesson_type
    );
    expect(responseData.data.lesson.max_students).toBe(
      lessonPayload.max_students
    );
    expect(responseData.error).toBeNull();

    // Store for cleanup
    createdLessonIds.push(responseData.data.lesson.id);
  });

  test('should retrieve all lessons successfully', async () => {
    const response = await app.request('/api/v1/lessons', {
      method: 'GET',
      headers: {
        Authorization: authToken,
      },
    });

    expect(response.status).toBe(200);
    const responseData = await response.json();

    expect(responseData.data).toBeDefined();
    expect(responseData.data.lessons).toBeArray();
    expect(responseData.data.total).toBeNumber();
    expect(responseData.data.page).toBeNumber();
    expect(responseData.data.limit).toBeNumber();
    expect(responseData.error).toBeNull();
  });

  test('should retrieve lesson by ID successfully', async () => {
    // First create a lesson
    const lessonPayload = {
      title: 'Advanced Riding Lesson',
      lesson_type: 'single-lesson',
      arena_id: testArenaId,
      date: '2025-12-02',
      start_time: '2025-12-02T14:00:00Z',
      end_time: '2025-12-02T15:30:00Z',
      duration_minutes: 90,
      max_students: 4,
      instructor_ids: [testUserId],
    };

    const createResponse = await app.request('/api/v1/lessons', {
      method: 'POST',
      body: JSON.stringify(lessonPayload),
      headers: {
        'Content-Type': 'application/json',
        Authorization: authToken,
      },
    });

    const createdLesson = await createResponse.json();
    const lessonId = createdLesson.data.lesson.id;
    createdLessonIds.push(lessonId);

    // Now retrieve it by ID
    const response = await app.request(`/api/v1/lessons/${lessonId}`, {
      method: 'GET',
      headers: {
        Authorization: authToken,
      },
    });

    expect(response.status).toBe(200);
    const responseData = await response.json();

    expect(responseData.data).toBeDefined();
    expect(responseData.data.lesson).toBeDefined();
    expect(responseData.data.lesson.id).toBe(lessonId);
    expect(responseData.data.lesson.title).toBe(lessonPayload.title);
    expect(responseData.error).toBeNull();
  });

  test('should update lesson successfully', async () => {
    // First create a lesson
    const lessonPayload = {
      title: 'Lesson to Update',
      lesson_type: 'single-lesson',
      arena_id: testArenaId,
      date: '2025-12-03',
      start_time: '2025-12-03T16:00:00Z',
      end_time: '2025-12-03T17:00:00Z',
      duration_minutes: 60,
      max_students: 8,
      instructor_ids: [testUserId],
    };

    const createResponse = await app.request('/api/v1/lessons', {
      method: 'POST',
      body: JSON.stringify(lessonPayload),
      headers: {
        'Content-Type': 'application/json',
        Authorization: authToken,
      },
    });

    const createdLesson = await createResponse.json();
    const lessonId = createdLesson.data.lesson.id;
    createdLessonIds.push(lessonId);

    // Update the lesson
    const updatePayload = {
      title: 'Updated Lesson Title',
      max_students: 10,
      notes: 'Updated lesson notes',
    };

    const response = await app.request(`/api/v1/lessons/${lessonId}`, {
      method: 'PUT',
      body: JSON.stringify(updatePayload),
      headers: {
        'Content-Type': 'application/json',
        Authorization: authToken,
      },
    });

    expect(response.status).toBe(200);
    const responseData = await response.json();

    expect(responseData.data).toBeDefined();
    expect(responseData.data.lesson).toBeDefined();
    expect(responseData.data.lesson.title).toBe(updatePayload.title);
    expect(responseData.data.lesson.max_students).toBe(
      updatePayload.max_students
    );
    expect(responseData.data.lesson.notes).toBe(updatePayload.notes);
    expect(responseData.error).toBeNull();
  });

  test('should delete lesson successfully', async () => {
    // First create a lesson
    const lessonPayload = {
      title: 'Lesson to Delete',
      lesson_type: 'single-lesson',
      arena_id: testArenaId,
      date: '2025-12-04',
      start_time: '2025-12-04T18:00:00Z',
      end_time: '2025-12-04T19:00:00Z',
      duration_minutes: 60,
      max_students: 6,
      instructor_ids: [testUserId],
    };

    const createResponse = await app.request('/api/v1/lessons', {
      method: 'POST',
      body: JSON.stringify(lessonPayload),
      headers: {
        'Content-Type': 'application/json',
        Authorization: authToken,
      },
    });

    const createdLesson = await createResponse.json();
    const lessonId = createdLesson.data.lesson.id;

    // Delete the lesson
    const response = await app.request(`/api/v1/lessons/${lessonId}`, {
      method: 'DELETE',
      headers: {
        Authorization: authToken,
      },
    });

    expect(response.status).toBe(200);
    const responseData = await response.json();

    expect(responseData.data).toBeDefined();
    expect(responseData.data.message).toContain('deleted');
    expect(responseData.error).toBeNull();

    // Verify lesson is deleted by trying to fetch it
    const fetchResponse = await app.request(`/api/v1/lessons/${lessonId}`, {
      method: 'GET',
      headers: {
        Authorization: authToken,
      },
    });

    expect(fetchResponse.status).toBe(404);
  });

  test('should create enrollment successfully', async () => {
    // First create a lesson
    const lessonPayload = {
      title: 'Lesson for Enrollment',
      lesson_type: 'single-lesson',
      arena_id: testArenaId,
      date: '2025-12-05',
      start_time: '2025-12-05T10:00:00Z',
      end_time: '2025-12-05T11:00:00Z',
      duration_minutes: 60,
      max_students: 6,
      instructor_ids: [testUserId],
    };

    const createResponse = await app.request('/api/v1/lessons', {
      method: 'POST',
      body: JSON.stringify(lessonPayload),
      headers: {
        'Content-Type': 'application/json',
        Authorization: authToken,
      },
    });

    const createdLesson = await createResponse.json();
    const lessonId = createdLesson.data.lesson.id;
    createdLessonIds.push(lessonId);

    // Create a test student using the actual students table structure
    const [student] = await client`
      INSERT INTO students (first_name, last_name, date_of_birth, gender, parent_id, email, phone)
      VALUES ('Test', 'Student', '2010-01-01', 'male', ${testUserId}, '<EMAIL>', '1234567890')
      RETURNING id;
    `;
    const studentId = student?.id;
    createdStudentIds.push(studentId);

    // Create enrollment - use testUserId since foreign key constraint points to users table
    const enrollmentPayload = {
      student_id: testUserId, // Use user ID due to foreign key constraint to users table
    };

    const response = await app.request(
      `/api/v1/lessons/${lessonId}/enrollments`,
      {
        method: 'POST',
        body: JSON.stringify(enrollmentPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      }
    );

    expect(response.status).toBe(201);
    const responseData = await response.json();

    expect(responseData.data).toBeDefined();
    expect(responseData.data.enrollment).toBeDefined();
    expect(responseData.data.enrollment.student_id).toBe(testUserId);
    expect(responseData.data.enrollment.lesson_id).toBe(lessonId);
    expect(responseData.error).toBeNull();

    // Student will be cleaned up in afterAll
  });

  test('should retrieve enrollments for lesson successfully', async () => {
    // First create a lesson
    const lessonPayload = {
      title: 'Lesson with Enrollments',
      lesson_type: 'single-lesson',
      arena_id: testArenaId,
      date: '2025-12-06',
      start_time: '2025-12-06T10:00:00Z',
      end_time: '2025-12-06T11:00:00Z',
      duration_minutes: 60,
      max_students: 6,
      instructor_ids: [testUserId],
    };

    const createResponse = await app.request('/api/v1/lessons', {
      method: 'POST',
      body: JSON.stringify(lessonPayload),
      headers: {
        'Content-Type': 'application/json',
        Authorization: authToken,
      },
    });

    const createdLesson = await createResponse.json();
    const lessonId = createdLesson.data.lesson.id;
    createdLessonIds.push(lessonId);

    // Get enrollments
    const response = await app.request(
      `/api/v1/lessons/${lessonId}/enrollments`,
      {
        method: 'GET',
        headers: {
          Authorization: authToken,
        },
      }
    );

    expect(response.status).toBe(200);
    const responseData = await response.json();

    expect(responseData.data).toBeDefined();
    expect(responseData.data.enrollments).toBeArray();
    expect(responseData.error).toBeNull();
  });
});
