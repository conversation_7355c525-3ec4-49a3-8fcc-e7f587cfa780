import { describe, test, expect, beforeAll, afterAll } from 'bun:test';
import { app } from '../../src';
import postgres from 'postgres';
import { env } from '../../src/config/env';
import {
  createTestStudio,
  deleteTestStudio,
  createTestUserByPayload,
  deleteTestUserByEmail,
  getRandomUserPayload,
} from '../utils/auth-test-utils';

// Test database connection
const client = postgres(env.SUPABASE_DB_URI ?? '', { prepare: false });

let testStudioId: string;
let testUserId: string;
let testArenaId: number;
let authToken: string;
let testInstructorPayload: any;

describe('Lesson Module - Input Validation Tests', () => {
  beforeAll(async () => {
    console.log('🔧 Setting up lesson validation tests...');

    // Create test studio
    const studioResult = await createTestStudio();
    testStudioId = studioResult.studioId;

    // Create test instructor user
    testInstructorPayload = getRandomUserPayload({
      email: `instructor-validation-${Math.random().toFixed(6)}@test.com`,
    });

    const instructorResult = await createTestUserByPayload(
      testInstructorPayload,
      testStudioId
    );
    testUserId = instructorResult.userId;

    // Update user role to instructor
    await client`
      UPDATE users 
      SET role = 'instructor' 
      WHERE id = ${testUserId}
    `;

    // Create test arena
    const [arena] = await client`
      INSERT INTO arenas (name, location, capacity)
      VALUES ('Test Validation Arena', 'Test Location', 8)
      RETURNING id;
    `;
    testArenaId = arena?.id;

    // Create auth token
    authToken = `Bearer test-token-${testUserId}`;

    console.log(`✅ Test studio: ${testStudioId}`);
    console.log(`✅ Test instructor: ${testUserId}`);
    console.log(`✅ Test arena: ${testArenaId}`);
  });

  afterAll(async () => {
    console.log('🧹 Cleaning up lesson validation tests...');

    // Clean up arena
    if (testArenaId) {
      await client`DELETE FROM arenas WHERE id = ${testArenaId};`;
    }

    // Clean up test users
    if (testInstructorPayload?.email) {
      await deleteTestUserByEmail(testInstructorPayload.email, testStudioId);
    }

    // Clean up studio
    await deleteTestStudio(testStudioId);

    await client.end();
    console.log('✅ Cleanup complete');
  });

  describe('Create Lesson Validation', () => {
    test('should reject missing required fields', async () => {
      const invalidPayload = {
        // Missing required fields: title, lesson_type, date, start_time, end_time, etc.
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.code).toBe('VALIDATION_ERROR');
      expect(responseData.error.message).toContain('Validation failed');
      expect(responseData.data).toBeNull();
    });

    test('should reject empty title', async () => {
      const invalidPayload = {
        title: '', // Empty title
        lesson_type: 'single-lesson',
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error.code).toBe('VALIDATION_ERROR');
      expect(responseData.error.message).toContain('Title is required');
    });

    test('should reject title that is too long', async () => {
      const invalidPayload = {
        title: 'A'.repeat(256), // Too long title (>255 chars)
        lesson_type: 'single-lesson',
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error.code).toBe('VALIDATION_ERROR');
      expect(responseData.error.message).toContain(
        'Title must be less than 255 characters'
      );
    });

    test('should reject invalid lesson_type', async () => {
      const invalidPayload = {
        title: 'Test Lesson',
        lesson_type: 'invalid-lesson-type', // Invalid enum value
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error.code).toBe('VALIDATION_ERROR');
      expect(responseData.error.message).toContain('lesson_type');
    });

    test('should reject past dates', async () => {
      const invalidPayload = {
        title: 'Test Lesson',
        lesson_type: 'single-lesson',
        date: '2020-01-01', // Past date
        start_time: '2020-01-01T10:00:00Z',
        end_time: '2020-01-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error.code).toBe('VALIDATION_ERROR');
      expect(responseData.error.message).toContain(
        'Date cannot be in the past'
      );
    });

    test('should reject invalid date format', async () => {
      const invalidPayload = {
        title: 'Test Lesson',
        lesson_type: 'single-lesson',
        date: 'invalid-date', // Invalid date format
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error.code).toBe('VALIDATION_ERROR');
    });

    test('should reject invalid time formats', async () => {
      const invalidPayload = {
        title: 'Test Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-01',
        start_time: 'invalid-time', // Invalid time format
        end_time: 'invalid-time', // Invalid time format
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error.code).toBe('VALIDATION_ERROR');
      expect(responseData.error.message).toContain('Invalid');
    });

    test('should reject end_time before start_time', async () => {
      const invalidPayload = {
        title: 'Test Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-01',
        start_time: '2025-12-01T11:00:00Z',
        end_time: '2025-12-01T10:00:00Z', // End before start
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error.code).toBe('VALIDATION_ERROR');
      expect(responseData.error.message).toContain(
        'end_time must be after start_time'
      );
    });

    test('should reject duration_minutes below minimum', async () => {
      const invalidPayload = {
        title: 'Test Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T10:10:00Z',
        duration_minutes: 10, // Below minimum of 15
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error.code).toBe('VALIDATION_ERROR');
      expect(responseData.error.message).toContain(
        'Duration must be at least 15 minutes'
      );
    });

    test('should reject duration_minutes above maximum', async () => {
      const invalidPayload = {
        title: 'Test Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T18:30:00Z',
        duration_minutes: 500, // Above maximum of 480 (8 hours)
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error.code).toBe('VALIDATION_ERROR');
      expect(responseData.error.message).toContain(
        'Duration cannot exceed 8 hours'
      );
    });

    test('should reject mismatched duration_minutes and time difference', async () => {
      const invalidPayload = {
        title: 'Test Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z', // 60 minute difference
        duration_minutes: 90, // Mismatched duration
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error.code).toBe('VALIDATION_ERROR');
      expect(responseData.error.message).toContain(
        'duration_minutes must match'
      );
    });

    test('should reject max_students below minimum', async () => {
      const invalidPayload = {
        title: 'Test Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 0, // Below minimum of 1
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error.code).toBe('VALIDATION_ERROR');
      expect(responseData.error.message).toContain(
        'At least 1 student slot required'
      );
    });

    test('should reject max_students above maximum', async () => {
      const invalidPayload = {
        title: 'Test Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 51, // Above maximum of 50
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error.code).toBe('VALIDATION_ERROR');
      expect(responseData.error.message).toContain(
        'Maximum 50 students allowed'
      );
    });

    test('should reject notes that are too long', async () => {
      const invalidPayload = {
        title: 'Test Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        notes: 'A'.repeat(2001), // Above maximum of 2000 characters
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error.code).toBe('VALIDATION_ERROR');
      expect(responseData.error.message).toContain(
        'Notes must be less than 2000 characters'
      );
    });

    test('should reject negative price', async () => {
      const invalidPayload = {
        title: 'Test Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        price: -10.0, // Negative price
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error.code).toBe('VALIDATION_ERROR');
      expect(responseData.error.message).toContain('Price cannot be negative');
    });

    test('should reject price above maximum', async () => {
      const invalidPayload = {
        title: 'Test Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        price: 10000.0, // Above maximum of $9999.99
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error.code).toBe('VALIDATION_ERROR');
      expect(responseData.error.message).toContain(
        'Price cannot exceed $9999.99'
      );
    });

    test('should reject empty instructor_ids array', async () => {
      const invalidPayload = {
        title: 'Test Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [], // Empty array
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error.code).toBe('VALIDATION_ERROR');
      expect(responseData.error.message).toContain(
        'At least one instructor is required'
      );
    });

    test('should reject invalid UUID format in instructor_ids', async () => {
      const invalidPayload = {
        title: 'Test Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: ['invalid-uuid', 'another-invalid-uuid'], // Invalid UUID format
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error.code).toBe('VALIDATION_ERROR');
      expect(responseData.error.message).toContain(
        'Invalid instructor_id format'
      );
    });

    test('should require recurrence_pattern for recurring lessons', async () => {
      const invalidPayload = {
        title: 'Recurring Lesson',
        lesson_type: 'recurring-lesson', // Recurring lesson
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
        // Missing recurrence_pattern
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error.code).toBe('VALIDATION_ERROR');
      expect(responseData.error.message).toContain(
        'recurrence_pattern is required for recurring lessons'
      );
    });

    test('should validate recurrence_pattern structure', async () => {
      const invalidPayload = {
        title: 'Recurring Lesson',
        lesson_type: 'recurring-lesson',
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
        recurrence_pattern: {
          type: 'invalid-type', // Invalid recurrence type
          interval: 1,
          daysOfWeek: [1, 3, 5],
          occurrences: 10,
        },
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(invalidPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('Update Lesson Validation', () => {
    test('should reject invalid lesson ID format', async () => {
      const updatePayload = {
        title: 'Updated Title',
      };

      const response = await app.request('/api/v1/lessons/invalid-id', {
        method: 'PUT',
        body: JSON.stringify(updatePayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.code).toBe('VALIDATION_ERROR');
    });

    test('should reject empty title in update', async () => {
      const updatePayload = {
        title: '', // Empty title
      };

      const response = await app.request('/api/v1/lessons/123', {
        method: 'PUT',
        body: JSON.stringify(updatePayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('Get Lessons Query Validation', () => {
    test('should reject invalid date format in filters', async () => {
      const response = await app.request(
        '/api/v1/lessons?date_from=invalid-date',
        {
          method: 'GET',
          headers: {
            Authorization: authToken,
          },
        }
      );

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.code).toBe('VALIDATION_ERROR');
    });

    test('should reject invalid pagination parameters', async () => {
      const response = await app.request('/api/v1/lessons?page=-1&limit=0', {
        method: 'GET',
        headers: {
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.code).toBe('VALIDATION_ERROR');
    });

    test('should reject invalid UUID format in instructor_id filter', async () => {
      const response = await app.request(
        '/api/v1/lessons?instructor_id=invalid-uuid',
        {
          method: 'GET',
          headers: {
            Authorization: authToken,
          },
        }
      );

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('Enrollment Validation', () => {
    test('should reject missing student_id in enrollment', async () => {
      const enrollmentPayload = {
        // Missing student_id
      };

      const response = await app.request('/api/v1/lessons/123/enrollments', {
        method: 'POST',
        body: JSON.stringify(enrollmentPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.code).toBe('VALIDATION_ERROR');
    });

    test('should reject invalid lesson ID in enrollment endpoint', async () => {
      const enrollmentPayload = {
        student_id: 123,
      };

      const response = await app.request(
        '/api/v1/lessons/invalid-id/enrollments',
        {
          method: 'POST',
          body: JSON.stringify(enrollmentPayload),
          headers: {
            'Content-Type': 'application/json',
            Authorization: authToken,
          },
        }
      );

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.code).toBe('VALIDATION_ERROR');
    });
  });
});
