import { describe, test, expect, beforeAll, afterAll } from 'bun:test';
import { app } from '../../src';
import postgres from 'postgres';
import { env } from '../../src/config/env';
import {
  createTestStudio,
  deleteTestStudio,
  createTestUserByPayload,
  deleteTestUserByEmail,
  getRandomUserPayload,
} from '../utils/auth-test-utils';

// Test database connection
const client = postgres(env.SUPABASE_DB_URI ?? '', { prepare: false });

let testStudioId: string;
let testUserId: string;
let testArenaId: number;
let testArenaId2: number;
let authToken: string;
let createdLessonIds: number[] = [];
let createdStudentIds: number[] = [];
let testInstructorPayload: any;
let testInstructor2Payload: any;
let testInstructor2Id: string;

describe('Lesson Module - Success Scenario Tests', () => {
  beforeAll(async () => {
    console.log('🔧 Setting up lesson success tests...');

    // Create test studio
    const studioResult = await createTestStudio();
    testStudioId = studioResult.studioId;

    // Create test instructor user
    testInstructorPayload = getRandomUserPayload({
      email: `instructor-success-${Math.random().toFixed(6)}@test.com`,
    });

    const instructorResult = await createTestUserByPayload(
      testInstructorPayload,
      testStudioId
    );
    testUserId = instructorResult.userId;

    // Create second instructor
    testInstructor2Payload = getRandomUserPayload({
      email: `instructor2-success-${Math.random().toFixed(6)}@test.com`,
    });

    const instructor2Result = await createTestUserByPayload(
      testInstructor2Payload,
      testStudioId
    );
    testInstructor2Id = instructor2Result.userId;

    // Update user roles to instructor
    await client`
      UPDATE users 
      SET role = 'instructor' 
      WHERE id IN (${testUserId}, ${testInstructor2Id})
    `;

    // Create test arenas
    const [arena1] = await client`
      INSERT INTO arenas (name, location, capacity)
      VALUES ('Test Success Arena 1', 'Test Location 1', 8)
      RETURNING id;
    `;
    testArenaId = arena1?.id;

    const [arena2] = await client`
      INSERT INTO arenas (name, location, capacity)
      VALUES ('Test Success Arena 2', 'Test Location 2', 12)
      RETURNING id;
    `;
    testArenaId2 = arena2?.id;

    // Create auth token
    authToken = `Bearer test-token-${testUserId}`;

    console.log(`✅ Test studio: ${testStudioId}`);
    console.log(`✅ Test instructor: ${testUserId}`);
    console.log(`✅ Test instructor 2: ${testInstructor2Id}`);
    console.log(`✅ Test arenas: ${testArenaId}, ${testArenaId2}`);
  });

  afterAll(async () => {
    console.log('🧹 Cleaning up lesson success tests...');

    // Clean up enrollments and lessons
    if (createdLessonIds.length > 0) {
      await client`DELETE FROM lesson_enrollments WHERE lesson_id = ANY(${createdLessonIds});`;
      await client`DELETE FROM lesson_instructors WHERE lesson_id = ANY(${createdLessonIds});`;
      await client`DELETE FROM lessons WHERE id = ANY(${createdLessonIds});`;
    }

    // Clean up students
    if (createdStudentIds.length > 0) {
      await client`DELETE FROM students WHERE id = ANY(${createdStudentIds});`;
    }

    // Clean up arenas
    if (testArenaId) {
      await client`DELETE FROM arenas WHERE id IN (${testArenaId}, ${testArenaId2});`;
    }

    // Clean up test users
    if (testInstructorPayload?.email) {
      await deleteTestUserByEmail(testInstructorPayload.email, testStudioId);
    }
    if (testInstructor2Payload?.email) {
      await deleteTestUserByEmail(testInstructor2Payload.email, testStudioId);
    }

    // Clean up studio
    await deleteTestStudio(testStudioId);

    await client.end();
    console.log('✅ Cleanup complete');
  });

  describe('Lesson Creation Success Scenarios', () => {
    test('should create lesson with minimal required fields', async () => {
      const lessonPayload = {
        title: 'Minimal Required Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 1, // Minimum value
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(201);
      const responseData = await response.json();

      expect(responseData.data.lesson).toBeDefined();
      expect(responseData.data.lesson.title).toBe(lessonPayload.title);
      expect(responseData.data.lesson.require_form).toBe(false); // Default value
      expect(responseData.data.lesson.require_payment).toBe(true); // Default value
      expect(responseData.error).toBeNull();

      createdLessonIds.push(responseData.data.lesson.id);
    });

    test('should create lesson with all optional fields', async () => {
      const lessonPayload = {
        title: 'Complete Lesson with All Fields',
        lesson_type: 'single-lesson',
        arena_id: testArenaId,
        date: '2025-12-02',
        start_time: '2025-12-02T14:00:00Z',
        end_time: '2025-12-02T16:00:00Z',
        duration_minutes: 120,
        max_students: 8,
        notes:
          'This is a comprehensive lesson with detailed notes and instructions.',
        require_form: true,
        require_payment: false,
        price: 75.5,
        curriculum_items: [1, 2], // Assuming curriculum items exist
        instructor_ids: [testUserId, testInstructor2Id], // Multiple instructors
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(201);
      const responseData = await response.json();

      expect(responseData.data.lesson).toBeDefined();
      expect(responseData.data.lesson.title).toBe(lessonPayload.title);
      expect(responseData.data.lesson.arena_id).toBe(lessonPayload.arena_id);
      expect(responseData.data.lesson.notes).toBe(lessonPayload.notes);
      expect(responseData.data.lesson.require_form).toBe(
        lessonPayload.require_form
      );
      expect(responseData.data.lesson.require_payment).toBe(
        lessonPayload.require_payment
      );
      expect(responseData.data.lesson.price).toBe(lessonPayload.price);
      expect(responseData.error).toBeNull();

      createdLessonIds.push(responseData.data.lesson.id);
    });

    test('should create recurring lesson with valid recurrence pattern', async () => {
      const lessonPayload = {
        title: 'Weekly Recurring Lesson',
        lesson_type: 'recurring-lesson',
        arena_id: testArenaId,
        date: '2025-12-03',
        start_time: '2025-12-03T09:00:00Z',
        end_time: '2025-12-03T10:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
        recurrence_pattern: {
          type: 'weekly',
          interval: 1,
          daysOfWeek: [2, 4], // Tuesday and Thursday
          occurrences: 8,
        },
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(201);
      const responseData = await response.json();

      expect(responseData.data.lesson).toBeDefined();
      expect(responseData.data.lesson.lesson_type).toBe('recurring-lesson');
      expect(responseData.data.lesson.recurrence_pattern).toBeDefined();
      expect(responseData.data.lesson.recurrence_pattern.type).toBe('weekly');
      expect(responseData.error).toBeNull();

      createdLessonIds.push(responseData.data.lesson.id);
    });

    test('should create camp lesson successfully', async () => {
      const lessonPayload = {
        title: 'Summer Horse Riding Camp',
        lesson_type: 'camp',
        arena_id: testArenaId2,
        date: '2025-12-15',
        start_time: '2025-12-15T08:00:00Z',
        end_time: '2025-12-15T16:00:00Z',
        duration_minutes: 480, // 8 hours max
        max_students: 20,
        notes: 'Full-day camp with multiple activities',
        price: 200.0,
        instructor_ids: [testUserId, testInstructor2Id],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(201);
      const responseData = await response.json();

      expect(responseData.data.lesson).toBeDefined();
      expect(responseData.data.lesson.lesson_type).toBe('camp');
      expect(responseData.data.lesson.duration_minutes).toBe(480);
      expect(responseData.data.lesson.max_students).toBe(20);
      expect(responseData.error).toBeNull();

      createdLessonIds.push(responseData.data.lesson.id);
    });

    test('should create lesson with edge case valid duration (15 minutes)', async () => {
      const lessonPayload = {
        title: 'Short 15-Minute Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-05',
        start_time: '2025-12-05T12:00:00Z',
        end_time: '2025-12-05T12:15:00Z',
        duration_minutes: 15, // Minimum duration
        max_students: 1,
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(201);
      const responseData = await response.json();

      expect(responseData.data.lesson.duration_minutes).toBe(15);
      expect(responseData.error).toBeNull();

      createdLessonIds.push(responseData.data.lesson.id);
    });

    test('should create lesson with maximum valid students (50)', async () => {
      const lessonPayload = {
        title: 'Large Group Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-06',
        start_time: '2025-12-06T10:00:00Z',
        end_time: '2025-12-06T11:00:00Z',
        duration_minutes: 60,
        max_students: 50, // Maximum value
        instructor_ids: [testUserId, testInstructor2Id],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(201);
      const responseData = await response.json();

      expect(responseData.data.lesson.max_students).toBe(50);
      expect(responseData.error).toBeNull();

      createdLessonIds.push(responseData.data.lesson.id);
    });
  });

  describe('Lesson Filtering and Querying Success Scenarios', () => {
    test('should filter lessons by instructor_id', async () => {
      const response = await app.request(
        `/api/v1/lessons?instructor_id=${testUserId}`,
        {
          method: 'GET',
          headers: {
            Authorization: authToken,
          },
        }
      );

      expect(response.status).toBe(200);
      const responseData = await response.json();

      expect(responseData.data.lessons).toBeArray();
      expect(responseData.data.total).toBeNumber();
      expect(responseData.error).toBeNull();
    });

    test('should filter lessons by arena_id', async () => {
      const response = await app.request(
        `/api/v1/lessons?arena_id=${testArenaId}`,
        {
          method: 'GET',
          headers: {
            Authorization: authToken,
          },
        }
      );

      expect(response.status).toBe(200);
      const responseData = await response.json();

      expect(responseData.data.lessons).toBeArray();
      expect(responseData.error).toBeNull();
    });

    test('should filter lessons by status', async () => {
      const response = await app.request('/api/v1/lessons?status=scheduled', {
        method: 'GET',
        headers: {
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(200);
      const responseData = await response.json();

      expect(responseData.data.lessons).toBeArray();
      expect(responseData.error).toBeNull();
    });

    test('should filter lessons by lesson_type', async () => {
      const response = await app.request('/api/v1/lessons?lesson_type=camp', {
        method: 'GET',
        headers: {
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(200);
      const responseData = await response.json();

      expect(responseData.data.lessons).toBeArray();
      expect(responseData.error).toBeNull();
    });

    test('should filter lessons by date range', async () => {
      const response = await app.request(
        '/api/v1/lessons?date_from=2025-12-01&date_to=2025-12-31',
        {
          method: 'GET',
          headers: {
            Authorization: authToken,
          },
        }
      );

      expect(response.status).toBe(200);
      const responseData = await response.json();

      expect(responseData.data.lessons).toBeArray();
      expect(responseData.error).toBeNull();
    });

    test('should handle pagination correctly', async () => {
      const response = await app.request('/api/v1/lessons?page=1&limit=5', {
        method: 'GET',
        headers: {
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(200);
      const responseData = await response.json();

      expect(responseData.data.lessons).toBeArray();
      expect(responseData.data.page).toBe(1);
      expect(responseData.data.limit).toBe(5);
      expect(responseData.data.total).toBeNumber();
      expect(responseData.data.total_pages).toBeNumber();
      expect(responseData.error).toBeNull();
    });
  });

  describe('Lesson Update Success Scenarios', () => {
    test('should update lesson with single field', async () => {
      // Create a lesson first
      const createPayload = {
        title: 'Lesson to Update Single Field',
        lesson_type: 'single-lesson',
        date: '2025-12-07',
        start_time: '2025-12-07T10:00:00Z',
        end_time: '2025-12-07T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const createResponse = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(createPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      const createdLesson = await createResponse.json();
      const lessonId = createdLesson.data.lesson.id;
      createdLessonIds.push(lessonId);

      // Update only the title
      const updatePayload = {
        title: 'Updated Single Field Title',
      };

      const response = await app.request(`/api/v1/lessons/${lessonId}`, {
        method: 'PUT',
        body: JSON.stringify(updatePayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(200);
      const responseData = await response.json();

      expect(responseData.data.lesson.title).toBe(updatePayload.title);
      expect(responseData.data.lesson.max_students).toBe(6); // Unchanged
      expect(responseData.error).toBeNull();
    });

    test('should update lesson with multiple fields', async () => {
      // Create a lesson first
      const createPayload = {
        title: 'Lesson to Update Multiple Fields',
        lesson_type: 'single-lesson',
        date: '2025-12-08',
        start_time: '2025-12-08T10:00:00Z',
        end_time: '2025-12-08T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const createResponse = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(createPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      const createdLesson = await createResponse.json();
      const lessonId = createdLesson.data.lesson.id;
      createdLessonIds.push(lessonId);

      // Update multiple fields
      const updatePayload = {
        title: 'Updated Multiple Fields Title',
        max_students: 10,
        notes: 'Updated comprehensive notes',
        require_form: false,
        price: 85.0,
      };

      const response = await app.request(`/api/v1/lessons/${lessonId}`, {
        method: 'PUT',
        body: JSON.stringify(updatePayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(200);
      const responseData = await response.json();

      expect(responseData.data.lesson.title).toBe(updatePayload.title);
      expect(responseData.data.lesson.max_students).toBe(
        updatePayload.max_students
      );
      expect(responseData.data.lesson.notes).toBe(updatePayload.notes);
      expect(responseData.data.lesson.require_form).toBe(
        updatePayload.require_form
      );
      expect(responseData.data.lesson.price).toBe(updatePayload.price);
      expect(responseData.error).toBeNull();
    });

    test('should update lesson status', async () => {
      // Create a lesson first
      const createPayload = {
        title: 'Lesson to Update Status',
        lesson_type: 'single-lesson',
        date: '2025-12-09',
        start_time: '2025-12-09T10:00:00Z',
        end_time: '2025-12-09T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const createResponse = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(createPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      const createdLesson = await createResponse.json();
      const lessonId = createdLesson.data.lesson.id;
      createdLessonIds.push(lessonId);

      // Update status to completed
      const updatePayload = {
        status: 'completed',
      };

      const response = await app.request(`/api/v1/lessons/${lessonId}`, {
        method: 'PUT',
        body: JSON.stringify(updatePayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(200);
      const responseData = await response.json();

      expect(responseData.data.lesson.status).toBe('completed');
      expect(responseData.error).toBeNull();
    });
  });

  describe('Enrollment Success Scenarios', () => {
    test('should create and manage enrollments successfully', async () => {
      // Create a lesson first
      const lessonPayload = {
        title: 'Lesson for Enrollment Management',
        lesson_type: 'single-lesson',
        date: '2025-12-10',
        start_time: '2025-12-10T10:00:00Z',
        end_time: '2025-12-10T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const createResponse = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      const createdLesson = await createResponse.json();
      const lessonId = createdLesson.data.lesson.id;
      createdLessonIds.push(lessonId);

      // Create test students
      const [student1] = await client`
        INSERT INTO students (first_name, last_name, email, phone, studio_id)
        VALUES ('Student', 'One', '<EMAIL>', '1234567890', ${testStudioId})
        RETURNING id;
      `;
      const [student2] = await client`
        INSERT INTO students (first_name, last_name, email, phone, studio_id)
        VALUES ('Student', 'Two', '<EMAIL>', '1234567891', ${testStudioId})
        RETURNING id;
      `;

      createdStudentIds.push(student1.id, student2.id);

      // Create first enrollment
      const enrollment1Payload = {
        student_id: student1.id,
      };

      const enrollment1Response = await app.request(
        `/api/v1/lessons/${lessonId}/enrollments`,
        {
          method: 'POST',
          body: JSON.stringify(enrollment1Payload),
          headers: {
            'Content-Type': 'application/json',
            Authorization: authToken,
          },
        }
      );

      expect(enrollment1Response.status).toBe(201);
      const enrollment1Data = await enrollment1Response.json();
      expect(enrollment1Data.data.enrollment.student_id).toBe(student1.id);

      // Create second enrollment
      const enrollment2Payload = {
        student_id: student2.id,
      };

      const enrollment2Response = await app.request(
        `/api/v1/lessons/${lessonId}/enrollments`,
        {
          method: 'POST',
          body: JSON.stringify(enrollment2Payload),
          headers: {
            'Content-Type': 'application/json',
            Authorization: authToken,
          },
        }
      );

      expect(enrollment2Response.status).toBe(201);

      // Get all enrollments
      const getEnrollmentsResponse = await app.request(
        `/api/v1/lessons/${lessonId}/enrollments`,
        {
          method: 'GET',
          headers: {
            Authorization: authToken,
          },
        }
      );

      expect(getEnrollmentsResponse.status).toBe(200);
      const enrollmentsData = await getEnrollmentsResponse.json();
      expect(enrollmentsData.data.enrollments).toHaveLength(2);

      // Update enrollment
      const updateEnrollmentPayload = {
        status: 'confirmed',
        notes: 'Enrollment confirmed',
      };

      const updateEnrollmentResponse = await app.request(
        `/api/v1/lessons/${lessonId}/enrollments/${student1.id}`,
        {
          method: 'PUT',
          body: JSON.stringify(updateEnrollmentPayload),
          headers: {
            'Content-Type': 'application/json',
            Authorization: authToken,
          },
        }
      );

      expect(updateEnrollmentResponse.status).toBe(200);
      const updatedEnrollmentData = await updateEnrollmentResponse.json();
      expect(updatedEnrollmentData.data.enrollment.status).toBe('confirmed');

      // Delete enrollment
      const deleteEnrollmentResponse = await app.request(
        `/api/v1/lessons/${lessonId}/enrollments/${student2.id}`,
        {
          method: 'DELETE',
          headers: {
            Authorization: authToken,
          },
        }
      );

      expect(deleteEnrollmentResponse.status).toBe(200);

      // Verify enrollment was deleted
      const finalEnrollmentsResponse = await app.request(
        `/api/v1/lessons/${lessonId}/enrollments`,
        {
          method: 'GET',
          headers: {
            Authorization: authToken,
          },
        }
      );

      const finalEnrollmentsData = await finalEnrollmentsResponse.json();
      expect(finalEnrollmentsData.data.enrollments).toHaveLength(1);
    });
  });

  describe('Edge Case Success Scenarios', () => {
    test('should handle lesson with maximum duration (8 hours)', async () => {
      const lessonPayload = {
        title: 'Maximum Duration Lesson',
        lesson_type: 'camp',
        date: '2025-12-11',
        start_time: '2025-12-11T08:00:00Z',
        end_time: '2025-12-11T16:00:00Z',
        duration_minutes: 480, // 8 hours
        max_students: 15,
        instructor_ids: [testUserId, testInstructor2Id],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(201);
      const responseData = await response.json();

      expect(responseData.data.lesson.duration_minutes).toBe(480);
      expect(responseData.error).toBeNull();

      createdLessonIds.push(responseData.data.lesson.id);
    });

    test('should handle lesson with maximum price ($9999.99)', async () => {
      const lessonPayload = {
        title: 'Maximum Price Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-12',
        start_time: '2025-12-12T10:00:00Z',
        end_time: '2025-12-12T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        price: 9999.99, // Maximum price
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(201);
      const responseData = await response.json();

      expect(responseData.data.lesson.price).toBe(9999.99);
      expect(responseData.error).toBeNull();

      createdLessonIds.push(responseData.data.lesson.id);
    });

    test('should handle lesson with maximum notes length (2000 characters)', async () => {
      const maxNotes = 'A'.repeat(2000); // Exactly 2000 characters

      const lessonPayload = {
        title: 'Maximum Notes Length Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-13',
        start_time: '2025-12-13T10:00:00Z',
        end_time: '2025-12-13T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        notes: maxNotes,
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(201);
      const responseData = await response.json();

      expect(responseData.data.lesson.notes).toBe(maxNotes);
      expect(responseData.data.lesson.notes.length).toBe(2000);
      expect(responseData.error).toBeNull();

      createdLessonIds.push(responseData.data.lesson.id);
    });

    test('should create recurring lesson with end date instead of occurrences', async () => {
      const lessonPayload = {
        title: 'Recurring Lesson with End Date',
        lesson_type: 'recurring-lesson',
        date: '2025-12-14',
        start_time: '2025-12-14T10:00:00Z',
        end_time: '2025-12-14T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
        recurrence_pattern: {
          type: 'weekly',
          interval: 2, // Every 2 weeks
          daysOfWeek: [6], // Saturday
          endDate: '2026-03-14', // End date instead of occurrences
        },
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
      });

      expect(response.status).toBe(201);
      const responseData = await response.json();

      expect(responseData.data.lesson.recurrence_pattern.endDate).toBe(
        '2026-03-14'
      );
      expect(responseData.data.lesson.recurrence_pattern.interval).toBe(2);
      expect(responseData.error).toBeNull();

      createdLessonIds.push(responseData.data.lesson.id);
    });
  });
});
