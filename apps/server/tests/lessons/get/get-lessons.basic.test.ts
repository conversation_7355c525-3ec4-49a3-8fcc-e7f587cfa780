import { describe, test, expect, beforeAll, afterAll } from 'bun:test';
import { app } from '../../../src';
import postgres from 'postgres';
import { env } from '../../../src/config/env';

// Test database connection
const client = postgres(env.SUPABASE_DB_URI ?? '', { prepare: false });

let testUserId: string;
let testLessonId: number;
let testArenaId: number;

describe('Get Lessons - CRUD Tests', () => {
  beforeAll(async () => {
    console.log('🔧 Setting up get lessons tests...');

    // Get test user (UUID)
    const users = await client`SELECT id FROM users LIMIT 1;`;
    testUserId = users[0]?.id;

    // Create test arena
    const [arena] = await client`
      INSERT INTO arenas (name, location, capacity)
      VALUES ('Test Get Arena', 'Test Location', 8)
      RETURNING id;
    `;
    testArenaId = arena?.id;

    // Create test lesson
    const [lesson] = await client`
      INSERT INTO lessons (
        title, lesson_type, arena_id, date, start_time, end_time,
        duration_minutes, max_students, current_students, status,
        created_by, curriculum_items, attachments
      ) VALUES (
        'Test Get Lesson',
        'single-lesson',
        ${testArenaId},
        ${new Date(Date.now() + 24 * 60 * 60 * 1000)},
        ${new Date(Date.now() + 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000)},
        ${new Date(Date.now() + 24 * 60 * 60 * 1000 + 3 * 60 * 60 * 1000)},
        60, 6, 0, 'scheduled', ${testUserId}, '[]', '[]'
      ) RETURNING id;
    `;
    testLessonId = lesson?.id;

    console.log(`✅ Test lesson: ${testLessonId}`);
  });

  afterAll(async () => {
    console.log('🧹 Cleaning up get lessons tests...');

    if (testLessonId) {
      await client`DELETE FROM lessons WHERE id = ${testLessonId};`;
    }
    if (testArenaId) {
      await client`DELETE FROM arenas WHERE id = ${testArenaId};`;
    }

    await client.end();
    console.log('✅ Cleanup complete');
  });
  test('should return 401 for unauthenticated request to list lessons', async () => {
    const response = await app.request('api/v1/lessons', {
      method: 'GET',
      headers: new Headers({ 'Content-Type': 'application/json' }),
    });

    expect(response.status).toBe(401);
    const responseData = await response.json();
    expect(responseData.error).toBeDefined();
    expect(responseData.error.message).toContain(
      'Missing Authorization header'
    );
  });

  test('should return 401 for unauthenticated request to get lesson by ID', async () => {
    const response = await app.request('api/v1/lessons/1', {
      method: 'GET',
      headers: new Headers({ 'Content-Type': 'application/json' }),
    });

    expect(response.status).toBe(401);
    const responseData = await response.json();
    expect(responseData.error).toBeDefined();
    expect(responseData.error.message).toContain(
      'Missing Authorization header'
    );
  });

  test('should return 401 for invalid token on list lessons', async () => {
    const response = await app.request('api/v1/lessons', {
      method: 'GET',
      headers: new Headers({
        'Content-Type': 'application/json',
        Authorization: 'Bearer fake-token',
      }),
    });

    expect(response.status).toBe(401);
    const responseData = await response.json();
    expect(responseData.error).toBeDefined();
    expect(responseData.error.message).toContain('Invalid or expired token');
  });

  test('should return 401 for invalid token on get lesson by ID', async () => {
    const response = await app.request('api/v1/lessons/1', {
      method: 'GET',
      headers: new Headers({
        'Content-Type': 'application/json',
        Authorization: 'Bearer fake-token',
      }),
    });

    expect(response.status).toBe(401);
    const responseData = await response.json();
    expect(responseData.error).toBeDefined();
    expect(responseData.error.message).toContain('Invalid or expired token');
  });

  test('should validate query parameters for list lessons', async () => {
    // Test with invalid query parameters
    const response = await app.request(
      '/api/v1/lessons?page=invalid&limit=invalid',
      {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      }
    );

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });

  test('should validate UUID format in query filters', async () => {
    // Test with invalid UUID in instructor_id filter
    const response = await app.request(
      '/api/v1/lessons?instructor_id=invalid-uuid',
      {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      }
    );

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });

  test('should validate UUID format in student_id filter', async () => {
    // Test with invalid UUID in student_id filter
    const response = await app.request(
      '/api/v1/lessons?student_id=invalid-uuid',
      {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      }
    );

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });

  test('should validate lesson ID parameter format', async () => {
    // Test with invalid lesson ID format
    const response = await app.request('/api/v1/lessons/invalid-id', {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
    });

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });

  test('should handle non-existent lesson ID', async () => {
    // Test with non-existent but valid lesson ID
    const response = await app.request('/api/v1/lessons/99999', {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
    });

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });

  test('should validate date range filters', async () => {
    // Test with invalid date formats
    const response = await app.request(
      '/api/v1/lessons?start_date=invalid-date&end_date=invalid-date',
      {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      }
    );

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });

  test('should validate status filter', async () => {
    // Test with invalid status value
    const response = await app.request(
      '/api/v1/lessons?status=invalid-status',
      {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      }
    );

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });
});
