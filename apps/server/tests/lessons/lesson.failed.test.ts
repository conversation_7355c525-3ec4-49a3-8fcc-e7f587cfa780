import { describe, test, expect, beforeAll, afterAll } from 'bun:test';
import { app } from '../../src';
import postgres from 'postgres';
import { env } from '../../src/config/env';
import {
  createTestStudio,
  deleteTestStudio,
  createTestUserByPayload,
  deleteTestUserByEmail,
  getRandomUserPayload,
} from '../utils/auth-test-utils';

// Test database connection
const client = postgres(env.SUPABASE_DB_URI ?? '', { prepare: false });

let testStudioId: string;
let testUserId: string;
let testStudentUserId: string;
let testParentUserId: string;
let testArenaId: number;
let instructorAuthToken: string;
let studentAuthToken: string;
let parentAuthToken: string;
let createdLessonIds: number[] = [];
let createdStudentIds: number[] = [];
let testInstructorPayload: any;
let testStudentPayload: any;
let testParentPayload: any;

describe('Lesson Module - Failure Scenario Tests', () => {
  beforeAll(async () => {
    console.log('🔧 Setting up lesson failure tests...');

    // Create test studio
    const studioResult = await createTestStudio();
    testStudioId = studioResult.studioId;

    // Create test instructor user
    testInstructorPayload = getRandomUserPayload({
      email: `instructor-failure-${Math.random().toFixed(6)}@test.com`,
    });

    const instructorResult = await createTestUserByPayload(
      testInstructorPayload,
      testStudioId
    );
    testUserId = instructorResult.userId;

    // Create test student user
    testStudentPayload = getRandomUserPayload({
      email: `student-failure-${Math.random().toFixed(6)}@test.com`,
    });

    const studentResult = await createTestUserByPayload(
      testStudentPayload,
      testStudioId
    );
    testStudentUserId = studentResult.userId;

    // Create test parent user
    testParentPayload = getRandomUserPayload({
      email: `parent-failure-${Math.random().toFixed(6)}@test.com`,
    });

    const parentResult = await createTestUserByPayload(
      testParentPayload,
      testStudioId
    );
    testParentUserId = parentResult.userId;

    // Update user roles
    await client`
      UPDATE users 
      SET role = 'instructor' 
      WHERE id = ${testUserId}
    `;

    await client`
      UPDATE users 
      SET role = 'student' 
      WHERE id = ${testStudentUserId}
    `;

    await client`
      UPDATE users 
      SET role = 'parent' 
      WHERE id = ${testParentUserId}
    `;

    // Create test arena
    const [arena] = await client`
      INSERT INTO arenas (name, location, capacity)
      VALUES ('Test Failure Arena', 'Test Location', 8)
      RETURNING id;
    `;
    testArenaId = arena?.id;

    // Create auth tokens
    instructorAuthToken = `Bearer test-token-${testUserId}`;
    studentAuthToken = `Bearer test-token-${testStudentUserId}`;
    parentAuthToken = `Bearer test-token-${testParentUserId}`;

    console.log(`✅ Test studio: ${testStudioId}`);
    console.log(`✅ Test instructor: ${testUserId}`);
    console.log(`✅ Test student: ${testStudentUserId}`);
    console.log(`✅ Test parent: ${testParentUserId}`);
    console.log(`✅ Test arena: ${testArenaId}`);
  });

  afterAll(async () => {
    console.log('🧹 Cleaning up lesson failure tests...');

    // Clean up enrollments and lessons
    if (createdLessonIds.length > 0) {
      await client`DELETE FROM lesson_enrollments WHERE lesson_id = ANY(${createdLessonIds});`;
      await client`DELETE FROM lesson_instructors WHERE lesson_id = ANY(${createdLessonIds});`;
      await client`DELETE FROM lessons WHERE id = ANY(${createdLessonIds});`;
    }

    // Clean up students
    if (createdStudentIds.length > 0) {
      await client`DELETE FROM students WHERE id = ANY(${createdStudentIds});`;
    }

    // Clean up arena
    if (testArenaId) {
      await client`DELETE FROM arenas WHERE id = ${testArenaId};`;
    }

    // Clean up test users
    if (testInstructorPayload?.email) {
      await deleteTestUserByEmail(testInstructorPayload.email, testStudioId);
    }
    if (testStudentPayload?.email) {
      await deleteTestUserByEmail(testStudentPayload.email, testStudioId);
    }
    if (testParentPayload?.email) {
      await deleteTestUserByEmail(testParentPayload.email, testStudioId);
    }

    // Clean up studio
    await deleteTestStudio(testStudioId);

    await client.end();
    console.log('✅ Cleanup complete');
  });

  describe('Authentication and Authorization Failures', () => {
    test('should return 401 for unauthenticated requests', async () => {
      const lessonPayload = {
        title: 'Test Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: { 'Content-Type': 'application/json' },
        // No Authorization header
      });

      expect(response.status).toBe(401);
      const responseData = await response.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.message).toContain(
        'Missing Authorization header'
      );
      expect(responseData.data).toBeNull();
    });

    test('should return 401 for invalid auth token', async () => {
      const lessonPayload = {
        title: 'Test Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer invalid-token-123',
        },
      });

      expect(response.status).toBe(401);
      const responseData = await response.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.message).toContain('Invalid or expired token');
      expect(responseData.data).toBeNull();
    });

    test('should return 403 for student trying to create lesson', async () => {
      const lessonPayload = {
        title: 'Test Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: studentAuthToken,
        },
      });

      expect(response.status).toBe(403);
      const responseData = await response.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.code).toBe('INSUFFICIENT_PERMISSIONS');
      expect(responseData.error.message).toContain('Insufficient permissions');
      expect(responseData.data).toBeNull();
    });

    test('should return 403 for parent trying to create lesson', async () => {
      const lessonPayload = {
        title: 'Test Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: parentAuthToken,
        },
      });

      expect(response.status).toBe(403);
      const responseData = await response.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.code).toBe('INSUFFICIENT_PERMISSIONS');
      expect(responseData.data).toBeNull();
    });
  });

  describe('Resource Not Found Failures', () => {
    test('should return 404 for non-existent lesson by ID', async () => {
      const response = await app.request('/api/v1/lessons/999999', {
        method: 'GET',
        headers: {
          Authorization: instructorAuthToken,
        },
      });

      expect(response.status).toBe(404);
      const responseData = await response.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.message).toContain('Lesson not found');
      expect(responseData.data).toBeNull();
    });

    test('should return 404 when updating non-existent lesson', async () => {
      const updatePayload = {
        title: 'Updated Title',
      };

      const response = await app.request('/api/v1/lessons/999999', {
        method: 'PUT',
        body: JSON.stringify(updatePayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: instructorAuthToken,
        },
      });

      expect(response.status).toBe(404);
      const responseData = await response.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.message).toContain('Lesson not found');
      expect(responseData.data).toBeNull();
    });

    test('should return 404 when deleting non-existent lesson', async () => {
      const response = await app.request('/api/v1/lessons/999999', {
        method: 'DELETE',
        headers: {
          Authorization: instructorAuthToken,
        },
      });

      expect(response.status).toBe(404);
      const responseData = await response.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.message).toContain('Lesson not found');
      expect(responseData.data).toBeNull();
    });

    test('should return 404 for non-existent arena_id', async () => {
      const lessonPayload = {
        title: 'Test Lesson with Invalid Arena',
        lesson_type: 'single-lesson',
        arena_id: 999999, // Non-existent arena
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: instructorAuthToken,
        },
      });

      expect(response.status).toBe(404);
      const responseData = await response.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.message).toContain('Arena not found');
      expect(responseData.data).toBeNull();
    });

    test('should return 404 for non-existent instructor_id', async () => {
      const lessonPayload = {
        title: 'Test Lesson with Invalid Instructor',
        lesson_type: 'single-lesson',
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: ['00000000-0000-0000-0000-000000000000'], // Non-existent instructor
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: instructorAuthToken,
        },
      });

      expect(response.status).toBe(404);
      const responseData = await response.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.message).toContain('Instructor not found');
      expect(responseData.data).toBeNull();
    });
  });

  describe('Business Logic Failures', () => {
    test('should fail when lesson capacity is exceeded', async () => {
      // First create a lesson with max_students = 1
      const lessonPayload = {
        title: 'Single Student Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-01',
        start_time: '2025-12-01T10:00:00Z',
        end_time: '2025-12-01T11:00:00Z',
        duration_minutes: 60,
        max_students: 1, // Only 1 student allowed
        instructor_ids: [testUserId],
      };

      const createResponse = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: instructorAuthToken,
        },
      });

      const createdLesson = await createResponse.json();
      const lessonId = createdLesson.data.lesson.id;
      createdLessonIds.push(lessonId);

      // Create two test students
      const [student1] = await client`
        INSERT INTO students (first_name, last_name, email, phone, studio_id)
        VALUES ('Student', 'One', '<EMAIL>', '1234567890', ${testStudioId})
        RETURNING id;
      `;
      const [student2] = await client`
        INSERT INTO students (first_name, last_name, email, phone, studio_id)
        VALUES ('Student', 'Two', '<EMAIL>', '1234567891', ${testStudioId})
        RETURNING id;
      `;

      createdStudentIds.push(student1.id, student2.id);

      // Enroll first student (should succeed)
      const enrollment1Response = await app.request(
        `/api/v1/lessons/${lessonId}/enrollments`,
        {
          method: 'POST',
          body: JSON.stringify({ student_id: student1.id }),
          headers: {
            'Content-Type': 'application/json',
            Authorization: instructorAuthToken,
          },
        }
      );

      expect(enrollment1Response.status).toBe(201);

      // Try to enroll second student (should fail due to capacity)
      const enrollment2Response = await app.request(
        `/api/v1/lessons/${lessonId}/enrollments`,
        {
          method: 'POST',
          body: JSON.stringify({ student_id: student2.id }),
          headers: {
            'Content-Type': 'application/json',
            Authorization: instructorAuthToken,
          },
        }
      );

      expect(enrollment2Response.status).toBe(409);
      const responseData = await enrollment2Response.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.code).toBe('LESSON_FULL');
      expect(responseData.error.message).toContain('capacity');
      expect(responseData.data).toBeNull();
    });

    test('should fail when trying to enroll same student twice', async () => {
      // Create a lesson
      const lessonPayload = {
        title: 'Duplicate Enrollment Test Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-02',
        start_time: '2025-12-02T10:00:00Z',
        end_time: '2025-12-02T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const createResponse = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: instructorAuthToken,
        },
      });

      const createdLesson = await createResponse.json();
      const lessonId = createdLesson.data.lesson.id;
      createdLessonIds.push(lessonId);

      // Create test student
      const [student] = await client`
        INSERT INTO students (first_name, last_name, email, phone, studio_id)
        VALUES ('Duplicate', 'Student', '<EMAIL>', '1234567892', ${testStudioId})
        RETURNING id;
      `;
      createdStudentIds.push(student.id);

      // First enrollment (should succeed)
      const firstEnrollmentResponse = await app.request(
        `/api/v1/lessons/${lessonId}/enrollments`,
        {
          method: 'POST',
          body: JSON.stringify({ student_id: student.id }),
          headers: {
            'Content-Type': 'application/json',
            Authorization: instructorAuthToken,
          },
        }
      );

      expect(firstEnrollmentResponse.status).toBe(201);

      // Second enrollment of same student (should fail)
      const secondEnrollmentResponse = await app.request(
        `/api/v1/lessons/${lessonId}/enrollments`,
        {
          method: 'POST',
          body: JSON.stringify({ student_id: student.id }),
          headers: {
            'Content-Type': 'application/json',
            Authorization: instructorAuthToken,
          },
        }
      );

      expect(secondEnrollmentResponse.status).toBe(409);
      const responseData = await secondEnrollmentResponse.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.code).toBe('DUPLICATE_ENROLLMENT');
      expect(responseData.error.message).toContain('already enrolled');
      expect(responseData.data).toBeNull();
    });

    test('should fail when trying to delete lesson with existing enrollments', async () => {
      // Create a lesson
      const lessonPayload = {
        title: 'Lesson with Enrollments to Delete',
        lesson_type: 'single-lesson',
        date: '2025-12-03',
        start_time: '2025-12-03T10:00:00Z',
        end_time: '2025-12-03T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const createResponse = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: instructorAuthToken,
        },
      });

      const createdLesson = await createResponse.json();
      const lessonId = createdLesson.data.lesson.id;
      createdLessonIds.push(lessonId);

      // Create test student
      const [student] = await client`
        INSERT INTO students (first_name, last_name, email, phone, studio_id)
        VALUES ('Enrolled', 'Student', '<EMAIL>', '1234567893', ${testStudioId})
        RETURNING id;
      `;
      createdStudentIds.push(student.id);

      // Enroll student
      await app.request(`/api/v1/lessons/${lessonId}/enrollments`, {
        method: 'POST',
        body: JSON.stringify({ student_id: student.id }),
        headers: {
          'Content-Type': 'application/json',
          Authorization: instructorAuthToken,
        },
      });

      // Try to delete lesson with enrollments
      const deleteResponse = await app.request(`/api/v1/lessons/${lessonId}`, {
        method: 'DELETE',
        headers: {
          Authorization: instructorAuthToken,
        },
      });

      expect(deleteResponse.status).toBe(409);
      const responseData = await deleteResponse.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.code).toBe('LESSON_HAS_ENROLLMENTS');
      expect(responseData.error.message).toContain('enrollments');
      expect(responseData.data).toBeNull();
    });

    test('should fail when scheduling conflicting lessons for same instructor', async () => {
      // Create first lesson
      const lesson1Payload = {
        title: 'First Conflicting Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-04',
        start_time: '2025-12-04T10:00:00Z',
        end_time: '2025-12-04T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const createResponse1 = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lesson1Payload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: instructorAuthToken,
        },
      });

      const createdLesson1 = await createResponse1.json();
      createdLessonIds.push(createdLesson1.data.lesson.id);

      // Try to create conflicting lesson
      const lesson2Payload = {
        title: 'Second Conflicting Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-04',
        start_time: '2025-12-04T10:30:00Z', // Overlaps with first lesson
        end_time: '2025-12-04T11:30:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId], // Same instructor
      };

      const createResponse2 = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lesson2Payload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: instructorAuthToken,
        },
      });

      expect(createResponse2.status).toBe(409);
      const responseData = await createResponse2.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.code).toBe('INSTRUCTOR_CONFLICT');
      expect(responseData.error.message).toContain('conflict');
      expect(responseData.data).toBeNull();
    });

    test('should fail when scheduling conflicting lessons for same arena', async () => {
      // Create first lesson
      const lesson1Payload = {
        title: 'First Arena Conflict Lesson',
        lesson_type: 'single-lesson',
        arena_id: testArenaId,
        date: '2025-12-05',
        start_time: '2025-12-05T14:00:00Z',
        end_time: '2025-12-05T15:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const createResponse1 = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lesson1Payload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: instructorAuthToken,
        },
      });

      const createdLesson1 = await createResponse1.json();
      createdLessonIds.push(createdLesson1.data.lesson.id);

      // Try to create conflicting lesson in same arena
      const lesson2Payload = {
        title: 'Second Arena Conflict Lesson',
        lesson_type: 'single-lesson',
        arena_id: testArenaId, // Same arena
        date: '2025-12-05',
        start_time: '2025-12-05T14:30:00Z', // Overlaps with first lesson
        end_time: '2025-12-05T15:30:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const createResponse2 = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lesson2Payload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: instructorAuthToken,
        },
      });

      expect(createResponse2.status).toBe(409);
      const responseData = await createResponse2.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.code).toBe('ARENA_CONFLICT');
      expect(responseData.error.message).toContain('arena');
      expect(responseData.data).toBeNull();
    });
  });

  describe('Database Constraint Failures', () => {
    test('should fail when trying to enroll non-existent student', async () => {
      // Create a lesson
      const lessonPayload = {
        title: 'Lesson for Non-existent Student',
        lesson_type: 'single-lesson',
        date: '2025-12-06',
        start_time: '2025-12-06T10:00:00Z',
        end_time: '2025-12-06T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const createResponse = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: instructorAuthToken,
        },
      });

      const createdLesson = await createResponse.json();
      const lessonId = createdLesson.data.lesson.id;
      createdLessonIds.push(lessonId);

      // Try to enroll non-existent student
      const enrollmentResponse = await app.request(
        `/api/v1/lessons/${lessonId}/enrollments`,
        {
          method: 'POST',
          body: JSON.stringify({ student_id: 999999 }), // Non-existent student
          headers: {
            'Content-Type': 'application/json',
            Authorization: instructorAuthToken,
          },
        }
      );

      expect(enrollmentResponse.status).toBe(404);
      const responseData = await enrollmentResponse.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.message).toContain('Student not found');
      expect(responseData.data).toBeNull();
    });

    test('should fail when updating enrollment for non-existent student', async () => {
      // Create a lesson
      const lessonPayload = {
        title: 'Lesson for Update Test',
        lesson_type: 'single-lesson',
        date: '2025-12-07',
        start_time: '2025-12-07T10:00:00Z',
        end_time: '2025-12-07T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const createResponse = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: instructorAuthToken,
        },
      });

      const createdLesson = await createResponse.json();
      const lessonId = createdLesson.data.lesson.id;
      createdLessonIds.push(lessonId);

      // Try to update enrollment for non-existent student
      const updateResponse = await app.request(
        `/api/v1/lessons/${lessonId}/enrollments/999999`,
        {
          method: 'PUT',
          body: JSON.stringify({ status: 'confirmed' }),
          headers: {
            'Content-Type': 'application/json',
            Authorization: instructorAuthToken,
          },
        }
      );

      expect(updateResponse.status).toBe(404);
      const responseData = await updateResponse.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.message).toContain('Enrollment not found');
      expect(responseData.data).toBeNull();
    });
  });

  describe('Server Error Simulation', () => {
    test('should handle malformed JSON request body', async () => {
      const malformedJson =
        '{ "title": "Test Lesson", "lesson_type": "single-lesson"'; // Missing closing brace

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: malformedJson,
        headers: {
          'Content-Type': 'application/json',
          Authorization: instructorAuthToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.message).toContain('Invalid JSON');
      expect(responseData.data).toBeNull();
    });

    test('should handle missing Content-Type header', async () => {
      const lessonPayload = {
        title: 'Test Lesson',
        lesson_type: 'single-lesson',
        date: '2025-12-08',
        start_time: '2025-12-08T10:00:00Z',
        end_time: '2025-12-08T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          // Missing Content-Type header
          Authorization: instructorAuthToken,
        },
      });

      expect(response.status).toBe(400);
      const responseData = await response.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.data).toBeNull();
    });

    test('should handle extremely large request payload', async () => {
      const hugeNotes = 'A'.repeat(100000); // 100KB of text

      const lessonPayload = {
        title: 'Test Lesson with Huge Notes',
        lesson_type: 'single-lesson',
        date: '2025-12-09',
        start_time: '2025-12-09T10:00:00Z',
        end_time: '2025-12-09T11:00:00Z',
        duration_minutes: 60,
        max_students: 6,
        notes: hugeNotes,
        instructor_ids: [testUserId],
      };

      const response = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: instructorAuthToken,
        },
      });

      expect(response.status).toBe(413);
      const responseData = await response.json();

      expect(responseData.error).toBeDefined();
      expect(responseData.error.message).toContain('Payload too large');
      expect(responseData.data).toBeNull();
    });
  });

  describe('Rate Limiting and Quota Failures', () => {
    test('should fail when exceeding maximum lessons per day for instructor', async () => {
      const basePayload = {
        lesson_type: 'single-lesson',
        date: '2025-12-10',
        duration_minutes: 60,
        max_students: 6,
        instructor_ids: [testUserId],
      };

      // Try to create many lessons on the same day
      const promises = [];
      for (let i = 0; i < 25; i++) {
        // Assuming limit is 20 lessons per day
        const lessonPayload = {
          ...basePayload,
          title: `Lesson ${i + 1}`,
          start_time: `2025-12-10T${(8 + i).toString().padStart(2, '0')}:00:00Z`,
          end_time: `2025-12-10T${(9 + i).toString().padStart(2, '0')}:00:00Z`,
        };

        promises.push(
          app.request('/api/v1/lessons', {
            method: 'POST',
            body: JSON.stringify(lessonPayload),
            headers: {
              'Content-Type': 'application/json',
              Authorization: instructorAuthToken,
            },
          })
        );
      }

      const responses = await Promise.all(promises);
      const failedResponses = responses.filter((r) => r.status === 429);

      expect(failedResponses.length).toBeGreaterThan(0);

      // Check one of the failed responses
      if (failedResponses.length > 0) {
        const responseData = await failedResponses[0].json();
        expect(responseData.error).toBeDefined();
        expect(responseData.error.code).toBe('DAILY_LESSON_LIMIT_EXCEEDED');
        expect(responseData.error.message).toContain('daily limit');
      }
    });
  });

  describe('Concurrency and Race Condition Failures', () => {
    test('should handle concurrent enrollment attempts gracefully', async () => {
      // Create a lesson with only 1 spot
      const lessonPayload = {
        title: 'Concurrent Enrollment Test',
        lesson_type: 'single-lesson',
        date: '2025-12-11',
        start_time: '2025-12-11T10:00:00Z',
        end_time: '2025-12-11T11:00:00Z',
        duration_minutes: 60,
        max_students: 1, // Only 1 spot available
        instructor_ids: [testUserId],
      };

      const createResponse = await app.request('/api/v1/lessons', {
        method: 'POST',
        body: JSON.stringify(lessonPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: instructorAuthToken,
        },
      });

      const createdLesson = await createResponse.json();
      const lessonId = createdLesson.data.lesson.id;
      createdLessonIds.push(lessonId);

      // Create multiple students
      const studentPromises = [];
      for (let i = 0; i < 5; i++) {
        studentPromises.push(
          client`
            INSERT INTO students (first_name, last_name, email, phone, studio_id)
            VALUES ('Concurrent', 'Student${i}', 'concurrent${i}@test.com', '123456789${i}', ${testStudioId})
            RETURNING id;
          `
        );
      }

      const students = await Promise.all(studentPromises);
      const studentIds = students.map((s) => s[0].id);
      createdStudentIds.push(...studentIds);

      // Try to enroll all students concurrently
      const enrollmentPromises = studentIds.map((studentId) =>
        app.request(`/api/v1/lessons/${lessonId}/enrollments`, {
          method: 'POST',
          body: JSON.stringify({ student_id: studentId }),
          headers: {
            'Content-Type': 'application/json',
            Authorization: instructorAuthToken,
          },
        })
      );

      const enrollmentResponses = await Promise.all(enrollmentPromises);

      // Only one should succeed (201), others should fail (409)
      const successResponses = enrollmentResponses.filter(
        (r) => r.status === 201
      );
      const failedResponses = enrollmentResponses.filter(
        (r) => r.status === 409
      );

      expect(successResponses).toHaveLength(1);
      expect(failedResponses).toHaveLength(4);

      // Check that failed responses have correct error message
      const failedResponseData = await failedResponses[0].json();
      expect(failedResponseData.error.code).toBe('LESSON_FULL');
    });
  });
});
