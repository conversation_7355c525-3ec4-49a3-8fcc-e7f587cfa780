import { describe, test, expect, beforeAll, afterAll } from 'bun:test';
import { app } from '../../../src';
import postgres from 'postgres';
import { env } from '../../../src/config/env';

// Test database connection
const client = postgres(env.SUPABASE_DB_URI ?? '', { prepare: false });

let testUserId: string;
let testLessonId: number;
let testArenaId: number;
let testEnrollmentId: number;

describe('Lesson Enrollments - CRUD Tests', () => {
  beforeAll(async () => {
    console.log('🔧 Setting up enrollment tests...');

    // Get test user (UUID)
    const users = await client`SELECT id FROM users LIMIT 1;`;
    testUserId = users[0]?.id;

    // Create test arena
    const [arena] = await client`
      INSERT INTO arenas (name, location, capacity)
      VALUES ('Test Enrollment Arena', 'Test Location', 8)
      RETURNING id;
    `;
    testArenaId = arena?.id;

    // Create test lesson
    const [lesson] = await client`
      INSERT INTO lessons (
        title, lesson_type, arena_id, date, start_time, end_time,
        duration_minutes, max_students, current_students, status,
        created_by, curriculum_items, attachments
      ) VALUES (
        'Test Enrollment Lesson',
        'single-lesson',
        ${testArenaId},
        ${new Date(Date.now() + 24 * 60 * 60 * 1000)},
        ${new Date(Date.now() + 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000)},
        ${new Date(Date.now() + 24 * 60 * 60 * 1000 + 3 * 60 * 60 * 1000)},
        60, 6, 0, 'scheduled', ${testUserId}, '[]', '[]'
      ) RETURNING id;
    `;
    testLessonId = lesson?.id;

    console.log(`✅ Test lesson: ${testLessonId}`);
  });

  afterAll(async () => {
    console.log('🧹 Cleaning up enrollment tests...');

    // Clean up test data
    if (testEnrollmentId) {
      await client`DELETE FROM lesson_enrollments WHERE id = ${testEnrollmentId};`;
    }
    if (testLessonId) {
      await client`DELETE FROM lessons WHERE id = ${testLessonId};`;
    }
    if (testArenaId) {
      await client`DELETE FROM arenas WHERE id = ${testArenaId};`;
    }

    await client.end();
    console.log('✅ Cleanup complete');
  });

  test('should return 401 for unauthenticated request to enroll student', async () => {
    const enrollmentPayload = {
      student_id: testUserId || '00000000-0000-0000-0000-000000000000',
      payment_amount: 50.0,
      assigned_horse: 'Test Horse',
      notes: 'Test enrollment',
    };

    const response = await app.request(
      `/api/v1/lessons/${testLessonId || 1}/enrollments`,
      {
        method: 'POST',
        body: JSON.stringify(enrollmentPayload),
        headers: { 'Content-Type': 'application/json' },
      }
    );

    expect(response.status).toBe(401);
    const responseData = await response.json();
    expect(responseData.error).toBeDefined();
    expect(responseData.error.message).toContain(
      'Missing Authorization header'
    );
  });

  test('should return 401 for unauthenticated request to get enrollments', async () => {
    const response = await app.request(
      `/api/v1/lessons/${testLessonId || 1}/enrollments`,
      {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      }
    );

    expect(response.status).toBe(401);
    const responseData = await response.json();
    expect(responseData.error).toBeDefined();
    expect(responseData.error.message).toContain(
      'Missing Authorization header'
    );
  });

  test('should return 401 for unauthenticated request to update enrollment', async () => {
    const updatePayload = {
      payment_amount: 75.0,
      assigned_horse: 'Updated Horse',
    };

    const response = await app.request(
      `/api/v1/lessons/${testLessonId || 1}/enrollments/${testUserId || '00000000-0000-0000-0000-000000000000'}`,
      {
        method: 'PUT',
        body: JSON.stringify(updatePayload),
        headers: { 'Content-Type': 'application/json' },
      }
    );

    expect(response.status).toBe(401);
    const responseData = await response.json();
    expect(responseData.error).toBeDefined();
    expect(responseData.error.message).toContain(
      'Missing Authorization header'
    );
  });

  test('should return 401 for unauthenticated request to unenroll student', async () => {
    const response = await app.request(
      `/api/v1/lessons/${testLessonId || 1}/enrollments/${testUserId || '00000000-0000-0000-0000-000000000000'}`,
      {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
      }
    );

    expect(response.status).toBe(401);
    const responseData = await response.json();
    expect(responseData.error).toBeDefined();
    expect(responseData.error.message).toContain(
      'Missing Authorization header'
    );
  });

  test('should return 401 for invalid token on enrollment endpoints', async () => {
    const enrollmentPayload = {
      student_id: testUserId || '00000000-0000-0000-0000-000000000000',
      payment_amount: 50.0,
    };

    const response = await app.request(
      `/api/v1/lessons/${testLessonId || 1}/enrollments`,
      {
        method: 'POST',
        body: JSON.stringify(enrollmentPayload),
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer fake-token',
        },
      }
    );

    expect(response.status).toBe(401);
    const responseData = await response.json();
    expect(responseData.error).toBeDefined();
    expect(responseData.error.message).toContain('Invalid or expired token');
  });

  test('should validate UUID format for student_id', async () => {
    const invalidUuidPayload = {
      student_id: 'invalid-uuid-format',
      payment_amount: 50.0,
      assigned_horse: 'Test Horse',
    };

    const response = await app.request(
      `/api/v1/lessons/${testLessonId || 1}/enrollments`,
      {
        method: 'POST',
        body: JSON.stringify(invalidUuidPayload),
        headers: { 'Content-Type': 'application/json' },
      }
    );

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });

  test('should validate payment_amount format', async () => {
    const invalidPaymentPayload = {
      student_id: testUserId || '00000000-0000-0000-0000-000000000000',
      payment_amount: 'invalid-amount', // Should be number
      assigned_horse: 'Test Horse',
    };

    const response = await app.request(
      `/api/v1/lessons/${testLessonId || 1}/enrollments`,
      {
        method: 'POST',
        body: JSON.stringify(invalidPaymentPayload),
        headers: { 'Content-Type': 'application/json' },
      }
    );

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });

  test('should validate lesson ID parameter format', async () => {
    const enrollmentPayload = {
      student_id: testUserId || '00000000-0000-0000-0000-000000000000',
      payment_amount: 50.0,
    };

    const response = await app.request(
      '/api/v1/lessons/invalid-id/enrollments',
      {
        method: 'POST',
        body: JSON.stringify(enrollmentPayload),
        headers: { 'Content-Type': 'application/json' },
      }
    );

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });

  test('should validate required fields for enrollment', async () => {
    const incompletePayload = {
      // Missing required student_id
      payment_amount: 50.0,
    };

    const response = await app.request(
      `/api/v1/lessons/${testLessonId || 1}/enrollments`,
      {
        method: 'POST',
        body: JSON.stringify(incompletePayload),
        headers: { 'Content-Type': 'application/json' },
      }
    );

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });

  test('should validate negative payment amounts', async () => {
    const negativePaymentPayload = {
      student_id: testUserId || '00000000-0000-0000-0000-000000000000',
      payment_amount: -50.0, // Negative amount should be invalid
      assigned_horse: 'Test Horse',
    };

    const response = await app.request(
      `/api/v1/lessons/${testLessonId || 1}/enrollments`,
      {
        method: 'POST',
        body: JSON.stringify(negativePaymentPayload),
        headers: { 'Content-Type': 'application/json' },
      }
    );

    expect(response.status).toBe(401); // Will be 401 due to missing auth
  });
});
