import { afterAll, beforeAll, describe, expect, test } from 'bun:test';
import {
  createTestStudio,
  createTestUserByPayload,
  deleteTestStudio,
  deleteTestUserByEmail,
  getRandomUserPayload,
} from '../../utils/auth-test-utils';
import { app } from '../../../src';

describe('Admin Studio - validation', () => {
  let testStudioId: string;
  let parentPayload: any;
  beforeAll(async () => {
    try {
      const studio = await createTestStudio();
      testStudioId = studio.studioId;
      parentPayload = getRandomUserPayload();
      await createTestUserByPayload(parentPayload, testStudioId);
    } catch (error) {
      throw new Error('Failed while creating dummy data');
    }
  });
  afterAll(async () => {
    try {
      await deleteTestUserByEmail(parentPayload.email, testStudioId);
      await deleteTestStudio(testStudioId);
    } catch (error) {
      throw new Error('Error in cleanup');
    }
  });
  test('Should fail if unauthorized user', async () => {
    const user_response = await app.request(
      `api/v1/auth/signin?studio_id=${testStudioId}`,
      {
        method: 'POST',
        body: JSON.stringify({
          email: parentPayload.email,
          password: parentPayload.password,
        }),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );
    const user_data = await user_response.json();
    const accessToken = user_data.data.accessToken;
    const studio_response = await app.request(`api/v1/admin/studios`, {
      method: 'GET',
      headers: new Headers({
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      }),
    });

    expect(studio_response.status).toBe(403);
    const response = await studio_response.json();
    expect(response.data).toBeNull();
    expect(response.error).toBeObject();
    expect(response.error.message).toBe('Forbidden: Only owner roles allowed');
  });
});
