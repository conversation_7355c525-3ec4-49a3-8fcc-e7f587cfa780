import { describe, expect, test } from 'bun:test';
import { app } from '../../../src';

const companyId = 'adminxyxuysdfjsd';
const ownerEmail = '<EMAIL>';
const ownerPassword = 'securePassword123';

describe('Admin - studio success test', async () => {
  test('Should return lists of installed studios', async () => {
    const user_response = await app.request(
      `api/v1/auth/signin?studio_id=${companyId}`,
      {
        method: 'POST',
        body: JSON.stringify({
          email: ownerEmail,
          password: ownerPassword,
        }),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      }
    );
    const user_data = await user_response.json();
    const accessToken = user_data.data.accessToken;

    const studio_response = await app.request(`api/v1/admin/studios`, {
      method: 'GET',
      headers: new Headers({
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      }),
    });

    expect(studio_response.status).toBe(200);
    const response = await studio_response.json();
    expect(response.error).toBeNull();
    expect(response.data).toBeObject();
    expect(response.data.studios).toBeArray();
  });
});
