{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    // Environment settings
    "lib": ["ESNext"],
    "target": "ESNext",
    "module": "ESNext",
    "jsx": "react-jsx",
    "jsxImportSource": "hono/jsx",

    // Types
    "types": ["bun-types"],

    // Output settings
    "declaration": true,
    "outDir": "dist",
    "noEmit": false,
    "emitDecoratorMetadata": true,

    // Module resolution
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": false
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
