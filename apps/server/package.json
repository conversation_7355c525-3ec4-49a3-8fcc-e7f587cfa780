{"name": "server", "version": "0.0.1", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "bun --watch run src/index.ts && tsc --watch", "test": "bun test", "test:watch": "bun test --watch", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:setup": "bun run db:generate && bun run db:migrate", "db:reset": "echo 'WARNING: This will drop all tables. Are you sure?' && read && drizzle-kit drop && bun run db:migrate", "db:check": "drizzle-kit check", "db:push": "drizzle-kit push"}, "dependencies": {"@hono/zod-validator": "^0.7.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "hono": "^4.7.7", "postgres": "^3.4.7", "rate-limit-redis": "^4.2.1", "resend": "^4.7.0", "shared": "workspace:*", "stripe": "^18.0.0", "zod": "^3.25.56"}, "devDependencies": {"@types/bun": "latest", "@types/bcryptjs": "^3.0.0", "drizzle-kit": "^0.31.1", "tsx": "^4.19.4"}}