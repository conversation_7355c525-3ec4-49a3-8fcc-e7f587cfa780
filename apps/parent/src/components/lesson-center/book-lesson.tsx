import { useNavigate } from '@tanstack/react-router';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

export function BookLessonPromo() {
  const navigate = useNavigate();
  return (
    <Card className='overflow-hidden rounded-2xl border-0 shadow-sm'>
      <CardContent className='p-0'>
        <div className='relative rounded-4xl bg-[#FDD36B] p-8 text-gray-900'>
          <div className='relative z-10 max-w-md'>
            <h2 className='mb-2 text-4xl font-bold'>
              Book a new Horse <br /> Riding lesson
            </h2>
            <p className='mb-6 text-lg opacity-90'>
              Because little cowboys and cowgirls deserve big adventures!
            </p>

            <Button
              className='bg-gray-900 px-8 py-3 text-white hover:bg-gray-800'
              onClick={() => navigate({ to: '/booking-lesson' })}
            >
              Book Now
            </Button>
          </div>

          {/* Horse riders illustration */}
          {/* <div className='absolute top-0 right-0 h-full w-1/2 opacity-80'>
            <img
              src='/images/lessons-page.png'
              alt='Horse riders'
              className='object-cover object-right'
            />
          </div> */}
        </div>
      </CardContent>
    </Card>
  );
}
