import { MoreHorizontal } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

const specialEvents = [
  {
    id: 1,
    title: 'Basic Trotting',
    date: 'April 18th',
    time: '4pm - 5pm',
    location: 'Ground Arena',
    illustration: '🐴',

    instructors: [
      { name: '<PERSON>', avatar: '👩‍🏫' },
      { name: '<PERSON>', avatar: '👦' },
      { name: '<PERSON>', avatar: '👨‍🏫' },
    ],
  },
  {
    id: 2,
    title: 'Advanced Jumping',
    date: 'April 30th',
    time: '4pm - 5pm',
    location: 'Ground Arena',
    illustration: '🏇',

    instructors: [
      { name: '<PERSON>', avatar: '👩‍🏫' },
      { name: '<PERSON>', avatar: '👦' },
      { name: '<PERSON>', avatar: '👨‍🏫' },
    ],
  },
];

export function SpecialEvents() {
  return (
    <Card className='border-0 shadow-sm'>
      <CardHeader>
        <CardTitle className='text-xl font-semibold text-gray-800'>
          Special Events
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className='grid w-[700px] grid-cols-1 gap-6 md:grid-cols-2'>
          {specialEvents.map((event) => (
            <div
              key={event.id}
              className={`rounded-xl border bg-[#FDD36B33] p-6`}
            >
              <div className='mb-4 flex items-start justify-between'>
                <div>
                  <h3 className='mb-1 text-lg font-semibold text-gray-900'>
                    {event.title}
                  </h3>
                  <p className='text-sm text-gray-600'>
                    {event.date} • {event.time} • {event.location}
                  </p>
                </div>
                <div className='text-3xl'>{event.illustration}</div>
              </div>

              {/* Instructors */}
              <div className='mb-6'>
                <span className='mb-3 block text-sm font-medium text-gray-700'>
                  Instructor
                </span>
                <div className='flex items-center gap-2'>
                  {event.instructors.map((instructor, index) => (
                    <div
                      key={index}
                      className='border-grey-200 flex items-center gap-2 rounded-full border bg-[#FDD36B33] px-3 py-1'
                    >
                      <div className='flex h-6 w-6 items-center justify-center rounded-full bg-gray-100 text-xs'>
                        {instructor.avatar}
                      </div>
                      <span className='text-sm font-medium'>
                        {instructor.name}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Action Buttons */}
              <div className='flex items-center gap-2'>
                <Button className='flex-1 bg-gray-900 text-white hover:bg-gray-800'>
                  Register Now
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant='outline'
                      size='icon'
                      className='border-gray-300 bg-transparent'
                    >
                      <MoreHorizontal className='h-4 w-4' />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align='end'>
                    <DropdownMenuItem>View Details</DropdownMenuItem>
                    <DropdownMenuItem>Share Event</DropdownMenuItem>
                    <DropdownMenuItem>Add to Calendar</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
