import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const pastLessons = [
  {
    id: 1,
    studentName: 'Emma',
    studentAvatar: '👧',
    lessonType: 'Advanced Jumping',
    date: '18 April 2025, 8am - 9am',
    status: 'Lesson Completed',
    statusColor: 'bg-green-100 text-green-700',
  },
  {
    id: 2,
    studentName: 'John',
    studentAvatar: '👦',
    lessonType: 'Advanced Jumping',
    date: '17 April 2025, 8am - 9am',
    status: 'Lesson Completed',
    statusColor: 'bg-green-100 text-green-700',
  },
  {
    id: 3,
    studentName: 'Emma',
    studentAvatar: '👧',
    lessonType: 'Advanced Jumping',
    date: '16 April 2025, 8am - 9am',
    status: 'Lesson Missed',
    statusColor: 'bg-red-100 text-red-700',
  },
];

export function PastLessons() {
  return (
    <Card className='border-0 shadow-sm'>
      <CardHeader>
        <div className='flex items-center justify-between'>
          <CardTitle className='text-xl font-semibold text-gray-800'>
            Past Lessons
          </CardTitle>
          <Button variant='ghost' size='sm' className='text-gray-500'>
            View All →
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div>
          {pastLessons.map((lesson) => (
            <div
              key={lesson.id}
              className='flex items-center rounded-lg p-2 transition-colors hover:bg-gray-50'
            >
              <div className='flex h-12 w-12 items-center justify-center rounded-full bg-gray-100 text-lg'>
                {lesson.studentAvatar}
              </div>

              <div className='min-w-0 flex-1'>
                <div className='mb-1 flex items-center justify-between gap-2'>
                  <h4 className='px-2 font-semibold text-gray-900'>
                    {lesson.studentName}
                  </h4>
                  <p className='text-xs text-gray-500'>{lesson.date}</p>
                </div>

                <div className='flex justify-between gap-2'>
                  <p className='mb-1 px-2 text-sm font-medium text-gray-700'>
                    {lesson.lessonType}
                  </p>
                  <Badge className={`px-2 py-1 text-xs ${lesson.statusColor}`}>
                    {lesson.status}
                  </Badge>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
