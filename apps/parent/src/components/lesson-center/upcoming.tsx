import { MoreHorizontal } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

const upcomingLessons = [
  {
    id: 1,
    title: 'Advanced Jumping',
    date: 'April 18th',
    time: '4pm - 5pm',
    location: 'Ground Arena',
    image: '/horse1.png',
    student: { name: '<PERSON>', avatar: '👦' },
    horse: { name: '<PERSON>', avatar: '🐎' },
    instructor: { name: '<PERSON>', avatar: '👩‍🏫' },
  },
  {
    id: 2,
    title: 'Advanced Jumping',
    date: 'April 18th',
    time: '4pm - 5pm',
    location: 'Ground Arena',
    image: '/horse1.png',
    student: { name: '<PERSON>', avatar: '👦' },
    horse: { name: '<PERSON>', avatar: '🐴' },
    instructor: { name: '<PERSON>', avatar: '👩‍🏫' },
  },
  {
    id: 3,
    title: 'Basic Trotting',
    date: 'April 18th',
    time: '4pm - 5pm',
    location: 'Ground Arena',
    image: '/horse1.png',
    student: { name: '<PERSON>', avatar: '👦' },
    horse: { name: 'Hunter', avatar: '🐎' },
    instructor: { name: 'Jessica', avatar: '👩‍🏫' },
  },
  {
    id: 4,
    title: 'Basic Trotting',
    date: 'April 18th',
    time: '4pm - 5pm',
    location: 'Ground Arena',
    image: '/horse1.png',
    student: { name: 'John', avatar: '👦' },
    horse: { name: 'Max', avatar: '🐴' },
    instructor: { name: 'Jessica', avatar: '👩‍🏫' },
  },
];

export function UpcomingLessons() {
  return (
    <Card className='border-0 shadow-sm'>
      <CardHeader>
        <CardTitle className='text-xl font-semibold text-gray-800'>
          Upcoming Lessons
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
          {upcomingLessons.map((lesson) => (
            <div
              key={lesson.id}
              className='overflow-hidden rounded-xl border bg-[#FDD36B33] p-6'
            >
              <div className='mb-4 flex items-start justify-between'>
                <div>
                  <h3 className='mb-1 text-lg font-semibold text-gray-900'>
                    {lesson.title}
                  </h3>
                  <p className='text-sm text-gray-600'>
                    {lesson.date} • {lesson.time} • {lesson.location}
                  </p>
                </div>

                <div className='relative h-20 w-20'>
                  <div className='absolute top-[-65px] z-10 size-30 overflow-hidden rounded-full bg-purple-500'>
                    <img
                      src={lesson.image}
                      alt={lesson.title}
                      className='absolute top-[59px] left-[14px] h-full w-full scale-[150%] object-cover'
                    />
                  </div>
                </div>
              </div>

              {/* Participants */}
              <div className='mb-6 space-y-3'>
                <div className='flex items-center justify-between'>
                  <span className='text-sm font-medium text-gray-700'>
                    Student
                  </span>
                  <span className='text-sm font-medium text-gray-700'>
                    Horse
                  </span>
                  <span className='text-sm font-medium text-gray-700'>
                    Instructor
                  </span>
                </div>
                <div className='flex items-center justify-between'>
                  <div className='b-2 flex items-center gap-2 rounded-full border-2 border-gray-300 px-2'>
                    <div className='flex h-8 w-8 items-center justify-center rounded-full bg-white text-sm'>
                      {lesson.student.avatar}
                    </div>
                    <span className='text-sm font-medium'>
                      {lesson.student.name}
                    </span>
                  </div>
                  <div className='flex items-center gap-2 rounded-full border-2 border-gray-300 px-2'>
                    <div className='flex h-8 w-8 items-center justify-center rounded-full bg-white text-sm'>
                      {lesson.horse.avatar}
                    </div>
                    <span className='text-sm font-medium'>
                      {lesson.horse.name}
                    </span>
                  </div>
                  <div className='flex items-center gap-2 rounded-full border-2 border-gray-300 px-2'>
                    <div className='flex h-8 w-8 items-center justify-center rounded-full bg-white text-sm'>
                      {lesson.instructor.avatar}
                    </div>
                    <span className='text-sm font-medium'>
                      {lesson.instructor.name}
                    </span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className='flex items-center gap-2'>
                <Button className='flex-1 text-white hover:bg-gray-800'>
                  Reschedule
                </Button>
                <Button
                  variant='outline'
                  className='flex-1 border-gray-300 bg-transparent'
                >
                  Cancel
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant='outline'
                      size='icon'
                      className='border-gray-300 bg-transparent'
                    >
                      <MoreHorizontal className='h-4 w-4' />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align='end'>
                    <DropdownMenuItem>View Details</DropdownMenuItem>
                    <DropdownMenuItem>Edit Lesson</DropdownMenuItem>
                    <DropdownMenuItem>Contact Instructor</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
