'use client';

import { Search, X } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';

interface Lesson {
  id: string;
  title: string;
  date: string;
  time: string;
  location: string;
  horse: { name: string; avatar: string };
  instructor: { name: string; avatar: string };
  illustration: string;
}

const lessons: Array<Lesson> = [
  {
    id: '1',
    title: 'Advanced Jumping',
    date: 'April 18th',
    time: '4pm - 5pm',
    location: 'Ground Arena',
    horse: { name: '<PERSON>', avatar: '🐎' },
    instructor: { name: '<PERSON>', avatar: '👩‍🏫' },
    illustration: '🏇',
  },
  {
    id: '2',
    title: 'Advanced Jumping',
    date: 'April 18th',
    time: '4pm - 5pm',
    location: 'Ground Arena',
    horse: { name: '<PERSON>', avatar: '🐴' },
    instructor: { name: '<PERSON>', avatar: '👩‍🏫' },
    illustration: '🏇',
  },
  {
    id: '3',
    title: 'Advanced Jumping',
    date: 'April 18th',
    time: '4pm - 5pm',
    location: 'Ground Arena',
    horse: { name: 'Hunter', avatar: '🐎' },
    instructor: { name: '<PERSON>', avatar: '👩‍🏫' },
    illustration: '🏇',
  },
  {
    id: '4',
    title: 'Advanced Jumping',
    date: 'April 18th',
    time: '4pm - 5pm',
    location: 'Ground Arena',
    horse: { name: 'Max', avatar: '🐴' },
    instructor: { name: 'Jessica', avatar: '👩‍🏫' },
    illustration: '🏇',
  },
  {
    id: '5',
    title: 'Advanced Jumping',
    date: 'April 18th',
    time: '4pm - 5pm',
    location: 'Ground Arena',
    horse: { name: 'Hunter', avatar: '🐎' },
    instructor: { name: 'Jessica', avatar: '👩‍🏫' },
    illustration: '🏇',
  },
  {
    id: '6',
    title: 'Advanced Jumping',
    date: 'April 18th',
    time: '4pm - 5pm',
    location: 'Ground Arena',
    horse: { name: 'Max', avatar: '🐴' },
    instructor: { name: 'Jessica', avatar: '👩‍🏫' },
    illustration: '🏇',
  },
  {
    id: '7',
    title: 'Advanced Jumping',
    date: 'April 18th',
    time: '4pm - 5pm',
    location: 'Ground Arena',
    horse: { name: 'Hunter', avatar: '🐎' },
    instructor: { name: 'Jessica', avatar: '👩‍🏫' },
    illustration: '🏇',
  },
  {
    id: '8',
    title: 'Advanced Jumping',
    date: 'April 18th',
    time: '4pm - 5pm',
    location: 'Ground Arena',
    horse: { name: 'Max', avatar: '🐴' },
    instructor: { name: 'Jessica', avatar: '👩‍🏫' },
    illustration: '🏇',
  },
  {
    id: '9',
    title: 'Advanced Jumping',
    date: 'April 18th',
    time: '4pm - 5pm',
    location: 'Ground Arena',
    horse: { name: 'Hunter', avatar: '🐎' },
    instructor: { name: 'Jessica', avatar: '👩‍🏫' },
    illustration: '🏇',
  },
  {
    id: '10',
    title: 'Advanced Jumping',
    date: 'April 18th',
    time: '4pm - 5pm',
    location: 'Ground Arena',
    horse: { name: 'Hunter', avatar: '🐎' },
    instructor: { name: 'Jessica', avatar: '👩‍🏫' },
    illustration: '🏇',
  },
];

export const LessonBookingPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLessons, setSelectedLessons] = useState<Array<string>>([]);

  // Filter lessons based on search term
  const filteredLessons = lessons.filter(
    (lesson) =>
      lesson.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lesson.horse.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lesson.instructor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lesson.location.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  // Handle lesson selection
  const handleLessonSelect = (lessonId: string, checked: boolean) => {
    if (checked) {
      setSelectedLessons([...selectedLessons, lessonId]);
    } else {
      setSelectedLessons(selectedLessons.filter((id) => id !== lessonId));
    }
  };

  // Clear all selections
  const clearSelections = () => {
    setSelectedLessons([]);
  };

  // Handle booking selected lessons
  const handleBookLessons = () => {
    if (selectedLessons.length > 0) {
      alert(`Booking ${selectedLessons.length} lesson(s)`);
      // Here you would typically make an API call to book the lessons
      setSelectedLessons([]);
    }
  };

  // Handle individual lesson booking
  const handleBookSingleLesson = (lessonId: string) => {
    alert(`Booking lesson ${lessonId}`);
    // Here you would typically make an API call to book the single lesson
  };

  return (
    <div className='min-h-screen bg-gray-50 p-4 md:p-6'>
      <div className='mx-auto max-w-7xl'>
        {/* Header with Search and Actions */}
        <div className='mb-8 flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center'>
          <div className='relative w-full max-w-md flex-1'>
            <Search className='absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400' />
            <Input
              placeholder='Search Lesson'
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
              }}
              className='h-12 border-gray-200 bg-white pl-10'
            />
          </div>

          <div className='flex items-center gap-4'>
            {selectedLessons.length > 0 && (
              <div className='flex items-center gap-2'>
                <span className='text-sm text-gray-600'>
                  {selectedLessons.length} Selected
                </span>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={clearSelections}
                  className='h-8 w-8 p-0'
                >
                  <X className='h-4 w-4' />
                </Button>
              </div>
            )}

            <Button
              onClick={handleBookLessons}
              disabled={selectedLessons.length === 0}
              className='bg-amber-400 px-6 font-medium text-gray-900 hover:bg-amber-500'
            >
              Book Lesson
            </Button>
          </div>
        </div>

        {/* Lessons Grid */}
        <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:gap-6 lg:grid-cols-3 xl:grid-cols-3'>
          {filteredLessons.map((lesson) => (
            <Card
              key={lesson.id}
              className={`border-2 bg-[#FDD36B33] shadow-sm`}
            >
              <CardContent className='p-4 md:p-6'>
                {/* Header with Checkbox and Illustration */}
                <div className='mb-4 flex items-start justify-between'>
                  <div className='flex min-w-0 flex-1 items-start gap-3'>
                    <Checkbox
                      checked={selectedLessons.includes(lesson.id)}
                      onCheckedChange={(checked) => {
                        handleLessonSelect(lesson.id, checked as boolean);
                      }}
                      className='boder-2 mt-1 border-black'
                    />
                    <div className='min-w-0 flex-1'>
                      <h3 className='mb-1 text-base font-semibold text-gray-900 md:text-lg'>
                        {lesson.title}
                      </h3>
                      <p className='text-xs text-gray-600 md:text-sm'>
                        {lesson.date} • {lesson.time} • {lesson.location}
                      </p>
                    </div>
                  </div>
                  <div className='ml-2 text-2xl md:text-3xl'>
                    {lesson.illustration}
                  </div>
                </div>

                {/* Horse and Instructor Info */}
                <div className='mb-6 space-y-3'>
                  {/* Labels */}
                  <div className='flex items-center gap-4'>
                    <div className='flex flex-col'>
                      <span className='text-sm font-medium text-gray-700'>
                        Horse
                      </span>
                      <div className='flex items-center gap-2 rounded-full border-2 border-black px-2'>
                        <div className='flex h-8 w-8 items-center justify-center rounded-full text-sm'>
                          {lesson.instructor.avatar}
                        </div>
                        <span className='text-sm font-medium'>
                          {lesson.instructor.name}
                        </span>
                      </div>
                    </div>

                    <div>
                      <span className='text-sm font-medium text-gray-700'>
                        Invigilator
                      </span>
                      <div className='flex gap-10'>
                        <div className='flex items-center gap-2 rounded-full border-2 border-black px-2'>
                          <div className='flex h-8 w-8 items-center justify-center rounded-full text-sm'>
                            {lesson.horse.avatar}
                          </div>

                          <span className='text-sm font-medium'>
                            {lesson.horse.name}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Horse & Invigilator Cards */}
                </div>

                {/* Book Button */}
                <Button
                  onClick={() => {
                    handleBookSingleLesson(lesson.id);
                  }}
                  className='w-full bg-gray-900 text-white hover:bg-gray-800'
                >
                  Book
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* No Results Message */}
        {filteredLessons.length === 0 && (
          <div className='py-12 text-center'>
            <p className='text-lg text-gray-500'>
              No lessons found matching your search.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
