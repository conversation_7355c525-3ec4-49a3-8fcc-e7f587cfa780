import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface StudentDetailPageProps {
  studentId: string;
}

export function StudentDetail({ studentId }: StudentDetailPageProps) {
  const studentData = {
    name: studentId === '1' ? '<PERSON>' : '<PERSON>',
    studentId: studentId === '1' ? '#12345' : '#12346',
    avatar: studentId === '1' ? '👧' : '👦',
    personalInfo: {
      dateOfBirth: studentId === '1' ? 'January 15, 2013' : 'March 22, 2013',
      gender: studentId === '1' ? 'Female' : 'Male',
      horsemanshipLevel: 'green',
      joinedDate: 'March 10, 2025',
    },
    emergencyContacts: [
      {
        name: '<PERSON>',
        relation: 'Father',
        contact: '+1 234-567-9800',
        avatar: '👨',
      },
      {
        name: '<PERSON>',
        relation: 'Mother',
        contact: '+1 234-567-9800',
        avatar: '👩',
      },
    ],
    bookedLessons: [
      {
        title: 'Advanced Jumping',
        date: 'April 18th',
        time: '4pm - 5pm',
        location: 'Ground Arena',
        student: {
          name: studentId === '1' ? 'Emily' : 'John',
          avatar: studentId === '1' ? '👧' : '👦',
        },
        horse: { name: 'Hunter', avatar: '🐎' },
        instructor: { name: 'Jessica', avatar: '👩‍🏫' },
        dateRange: { from: '08/04/2025', to: '08/05/2025' },
        bgColor: 'bg-purple-100',
        illustration: '🏇',
      },
      {
        title: 'Basic Trotting',
        date: 'April 18th',
        time: '4pm - 5pm',
        location: 'Ground Arena',
        student: {
          name: studentId === '1' ? 'Emily' : 'John',
          avatar: studentId === '1' ? '👧' : '👦',
        },
        horse: { name: 'Hunter', avatar: '🐎' },
        instructor: { name: 'Jessica', avatar: '👩‍🏫' },
        dateRange: { from: '08/04/2025', to: '08/05/2025' },
        bgColor: 'bg-green-100',
        illustration: '🐴',
      },
    ],
  };

  return (
    <div className='min-h-screen bg-gray-50 p-6'>
      <div className='mx-auto space-y-8'>
        <Card className='border-0 shadow-sm'>
          <CardHeader className='flex items-center justify-between'>
            <CardTitle className='flex items-center gap-4'>
              <Avatar className='size-20'>
                <AvatarImage src={studentData.avatar} />
                <AvatarFallback>{studentData.avatar}</AvatarFallback>
              </Avatar>
              <div className='flex flex-col gap-1'>
                <h1 className='text-3xl font-bold text-gray-900'>
                  {studentData.name}
                </h1>
                <p className='font-medium text-gray-600'>
                  Student ID: {studentData.studentId}
                </p>
              </div>
            </CardTitle>
            <Button variant='outline' className='bg-white'>
              Edit Student Profile
            </Button>
          </CardHeader>
          <div className='h-px w-full bg-[#2828281A]' />

          <CardContent className='space-y-8 px-8'>
            <div className='flex justify-between'>
              <div>
                <h2 className='mb-6 text-xl font-semibold text-gray-800'>
                  Personal Information
                </h2>
                <div className='flex gap-4'>
                  <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                    <div className='rounded-3xl bg-[#FDD36B33] px-4 py-3'>
                      <p className='mb-1 text-sm text-[#282828]'>
                        Date of Birth
                      </p>
                      <p className='font-semibold text-gray-900'>
                        {studentData.personalInfo.dateOfBirth}
                      </p>
                    </div>
                    <div className='rounded-3xl bg-[#FDD36B33] px-4 py-3'>
                      <p className='mb-1 text-sm text-gray-600'>Gender</p>
                      <p className='font-semibold text-gray-900'>
                        {studentData.personalInfo.gender}
                      </p>
                    </div>
                    <div className='flex items-center justify-between rounded-3xl bg-[#FDD36B33]'>
                      <div className='p-4'>
                        <p className='mb-1 text-sm text-gray-600'>
                          Horsemanship Level
                        </p>
                        <p className='font-semibold text-gray-900'>
                          {studentData.personalInfo.horsemanshipLevel
                            .charAt(0)
                            .toUpperCase() +
                            studentData.personalInfo.horsemanshipLevel.slice(1)}
                        </p>
                      </div>
                      <div className=''>
                        <img
                          src={`/learning-levels/${studentData.personalInfo.horsemanshipLevel}.png`}
                          alt={`${studentData.personalInfo.horsemanshipLevel} Level Badge`}
                          className='-mt-3 size-[4.5rem] object-contain'
                        />
                      </div>
                    </div>
                    <div className='rounded-3xl bg-[#FDD36B33] px-4 py-3'>
                      <p className='mb-1 text-sm text-gray-600'>Joined Date</p>
                      <p className='font-semibold text-gray-900'>
                        {studentData.personalInfo.joinedDate}
                      </p>
                    </div>
                  </div>
                  <div className='flex flex-col gap-2'>
                    <h4 className='text-sm font-medium text-gray-800'>
                      Emergency Contacts
                    </h4>
                    <div className='flex gap-3'>
                      {studentData.emergencyContacts.map((contact, index) => (
                        <div
                          key={index}
                          className='flex gap-4 rounded-3xl border bg-white p-3'
                        >
                          <Avatar className='size-10'>
                            <AvatarImage src={contact.avatar} />
                            <AvatarFallback>{contact.avatar}</AvatarFallback>
                          </Avatar>
                          <div>
                            <h3 className='font-semibold text-gray-900'>
                              {contact.name}
                            </h3>
                            <p className='text-sm text-gray-600'>
                              Relation: {contact.relation}
                            </p>
                            <p className='text-sm text-gray-600'>
                              Contact: {contact.contact}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className='h-px w-full bg-[#2828281A]' />
            {/* Booked Lessons Section - Full Width */}
            <div>
              <h2 className='mb-6 text-xl font-semibold text-gray-800'>
                Booked Lessons
              </h2>
              <div className='flex flex-wrap gap-6'>
                {studentData.bookedLessons.map((lesson, index) => (
                  <div
                    key={index}
                    className={`relative max-w-96 rounded-xl bg-[#FDD36B33] p-5`}
                  >
                    <div className='absolute top-0 right-0'>
                      <div className='size-10 overflow-hidden'>
                        <img
                          src='/card-circle-gradient.svg'
                          alt=''
                          className='size-full'
                        />
                      </div>
                      {/* <div className='size-10 overflow-hidden'>
                        <img
                          src='/horses/jumping.png'
                          alt=''
                          className='object-cover'
                        />
                      </div> */}
                    </div>
                    <div className='mb-4 flex items-start justify-between'>
                      <div>
                        <h3 className='mb-1 text-lg font-semibold text-gray-900'>
                          {lesson.title}
                        </h3>
                        <p className='text-sm text-gray-600'>
                          {lesson.date} • {lesson.time} • {lesson.location}
                        </p>
                      </div>
                      <div className='text-3xl'>{lesson.illustration}</div>
                    </div>

                    {/* Participants */}
                    <div className='mb-4 space-y-1.5'>
                      <div className='flex items-center justify-between'>
                        <div>
                          <h6 className='text-sm font-semibold text-[#282828]'>
                            Student
                          </h6>
                          <div className='flex items-center gap-2 rounded-l-full rounded-r-full border border-[#28282866] p-1.5 pr-3'>
                            <Avatar className='size-5'>
                              <AvatarImage src={lesson.student.avatar} />
                              <AvatarFallback>
                                {lesson.student.avatar}
                              </AvatarFallback>
                            </Avatar>
                            <p className='text-xs'>{lesson.student.name}</p>
                          </div>
                        </div>
                        <div>
                          <h6 className='text-sm font-semibold text-[#282828]'>
                            Horse
                          </h6>
                          <div className='flex items-center gap-2 rounded-l-full rounded-r-full border border-[#28282866] p-1.5 pr-3'>
                            <Avatar className='size-5'>
                              <AvatarImage src={lesson.horse.avatar} />
                              <AvatarFallback>
                                {lesson.horse.avatar}
                              </AvatarFallback>
                            </Avatar>
                            <p className='text-xs'>{lesson.horse.name}</p>
                          </div>
                        </div>
                        <div>
                          <h6 className='text-sm font-semibold text-[#282828]'>
                            Instructor
                          </h6>
                          <div className='flex items-center gap-2 rounded-l-full rounded-r-full border border-[#28282866] p-1.5 pr-3'>
                            <Avatar className='size-5'>
                              <AvatarImage src={lesson.instructor.avatar} />
                              <AvatarFallback>
                                {lesson.instructor.avatar}
                              </AvatarFallback>
                            </Avatar>
                            <p className='text-xs'>{lesson.instructor.name}</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Date Range */}
                    <div className='flex items-center justify-between text-sm'>
                      <div>
                        <h6 className='text-sm font-semibold text-[#282828]'>
                          From
                        </h6>
                        <p className='text-sm text-[#282828]'>
                          {lesson.dateRange.from}
                        </p>
                      </div>
                      <div>
                        <h6 className='text-sm font-semibold text-[#282828]'>
                          To
                        </h6>
                        <p className='text-sm text-[#282828]'>
                          {lesson.dateRange.to}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
