import { useNavigate } from '@tanstack/react-router';
import { MoreHorizontal, Search, UserPlus } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';

export const StudentsPage = () => {
  const navigate = useNavigate();
  const students = [
    {
      id: 1,
      name: '<PERSON>',
      age: 12,
      level: 'Intermediate',
      avatar: '👧',
      badgeImage: '/learning-levels/green.png',
      skills: ['Advanced Jumping', 'Trotting'],
      cardColor: 'bg-[#FDD36B33]',
    },
    {
      id: 2,
      name: '<PERSON>',
      age: 12,
      level: 'Intermediate',
      avatar: '👦',
      badgeImage: '/learning-levels/blue.png',
      skills: ['Advanced Jumping', 'Trotting'],
      cardColor: 'bg-[#FDD36B33]',
    },
  ];

  return (
    <div className='min-h-screen bg-gray-50 p-6'>
      <div className='mx-auto'>
        {/* Header with Search and Add Button */}
        <div className='mb-8 flex items-center justify-between'>
          <div className='relative max-w-md flex-1'>
            <Search className='absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400' />
            <Input
              placeholder='Search Student'
              className='h-12 rounded-l-full rounded-r-full border-gray-200 bg-[#F1F3F9] pl-10'
            />
          </div>

          <Button className='h-12 bg-gray-900 px-6 text-white hover:bg-gray-800'>
            <UserPlus className='size-4 text-[#FDD36B]' />
            Add New Student
          </Button>
        </div>

        {/* Students Grid */}
        <div className='grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3'>
          {students.map((student) => (
            <Card
              key={student.id}
              className={`border-2 ${student.cardColor} relative shadow-sm`}
            >
              <img
                src='/card-circle-gradient.svg'
                alt=''
                className='absolute top-0 right-0'
              />
              <CardContent className='px-6'>
                {/* Student Header */}
                <div className='mb-4 flex items-start justify-between'>
                  <div className='flex items-center gap-3'>
                    <div className='flex h-12 w-12 items-center justify-center rounded-full bg-gray-100 text-xl'>
                      {student.avatar}
                    </div>
                    <div>
                      <h3 className='text-lg font-semibold text-gray-900'>
                        {student.name}
                      </h3>
                      <p className='text-sm text-gray-600'>
                        Age: {student.age}
                      </p>
                      <p className='text-sm text-gray-600'>
                        Level: {student.level}
                      </p>
                    </div>
                  </div>

                  {/* Achievement Badge */}
                  <div className='relative h-12 w-8 transform'>
                    <img
                      src={student.badgeImage || '/placeholder.svg'}
                      alt='Achievement Badge'
                      className='absolute top-[-25px] left-[-2px] object-contain'
                    />
                  </div>
                </div>

                {/* Skills */}
                <div className='mb-6 flex flex-wrap gap-2'>
                  {student.skills.map((skill, index) => (
                    <Badge
                      key={index}
                      variant='outline'
                      className='border-gray-300 bg-[#FDD36B33] p-2 text-gray-700 hover:bg-gray-50'
                    >
                      {skill}
                    </Badge>
                  ))}
                  <div className='mt-2 h-px w-[800px] bg-gray-300'></div>
                </div>

                {/* Action Buttons */}
                <div className='flex items-center gap-2'>
                  <Button className='flex-1 bg-gray-900 text-white hover:bg-gray-800'>
                    Book Lesson
                  </Button>

                  <Button
                    onClick={() => {
                      void navigate({ to: '/student-details' });
                    }}
                    variant='outline'
                    className='flex-1 border-gray-300 bg-transparent'
                  >
                    View Details
                  </Button>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant='outline'
                        size='icon'
                        className='border-gray-300 bg-transparent'
                      >
                        <MoreHorizontal className='h-4 w-4' />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align='end'>
                      <DropdownMenuItem>Edit Student</DropdownMenuItem>
                      <DropdownMenuItem>View Progress</DropdownMenuItem>
                      <DropdownMenuItem>Send Message</DropdownMenuItem>
                      <DropdownMenuItem className='text-red-600'>
                        Remove Student
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};
