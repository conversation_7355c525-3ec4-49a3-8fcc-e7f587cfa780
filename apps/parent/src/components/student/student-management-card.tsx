import { useNavigate } from '@tanstack/react-router';
import { MoreHorizontal } from 'lucide-react';

import { type Student } from '@/pages/student-management';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface StudentManagementCardProps {
  student: Student;
}

export const StudentManagementCard = ({
  student,
}: StudentManagementCardProps) => {
  const navigate = useNavigate();

  return (
    <Card
      key={student.id}
      className={`border-2 ${student.cardColor} relative shadow-sm`}
    >
      <img
        src='/card-circle-gradient.svg'
        alt=''
        className='absolute top-0 right-0'
      />
      <CardContent className='px-6'>
        {/* Student Header */}
        <div className='mb-4 flex items-start justify-between'>
          <div className='flex items-center gap-3'>
            <div className='flex h-12 w-12 items-center justify-center rounded-full bg-gray-100 text-xl'>
              {student.avatar}
            </div>
            <div>
              <h3 className='text-lg font-semibold text-gray-900'>
                {student.name}
              </h3>
              <p className='text-sm text-gray-600'>Age: {student.age}</p>
              <p className='text-sm text-gray-600'>Level: {student.level}</p>
            </div>
          </div>

          {/* Achievement Badge */}
          <div className='relative h-12 w-8 transform'>
            <img
              src={student.badgeImage || '/placeholder.svg'}
              alt='Achievement Badge'
              className='absolute top-[-25px] left-[-2px] object-contain'
            />
          </div>
        </div>

        {/* Skills */}
        <div className='mb-6 flex flex-wrap gap-2'>
          {student.skills.map((skill, index) => (
            <Badge
              key={index}
              variant='outline'
              className='border-gray-300 bg-[#FDD36B33] p-2 text-gray-700 hover:bg-gray-50'
            >
              {skill}
            </Badge>
          ))}
          <div className='mt-2 h-px w-[800px] bg-gray-300' />
        </div>

        {/* Action Buttons */}
        <div className='flex items-center gap-2'>
          <Button className='flex-1 bg-gray-900 text-white hover:bg-gray-800'>
            Book Lesson
          </Button>

          <Button
            onClick={() => {
              void navigate({
                to: '/student-management/$id',
                params: { id: student.id.toString() },
              });
            }}
            variant='outline'
            className='flex-1 border-gray-300 bg-transparent'
          >
            View Details
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant='outline'
                size='icon'
                className='border-gray-300 bg-transparent'
              >
                <MoreHorizontal className='h-4 w-4' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              <DropdownMenuItem>Edit Student</DropdownMenuItem>
              <DropdownMenuItem>View Progress</DropdownMenuItem>
              <DropdownMenuItem>Send Message</DropdownMenuItem>
              <DropdownMenuItem className='text-red-600'>
                Remove Student
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  );
};
