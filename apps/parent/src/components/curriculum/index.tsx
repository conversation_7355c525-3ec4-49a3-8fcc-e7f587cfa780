import { useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';

import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { LevelCardSection } from './level-card-section';
import { LevelDialog } from './level-dialog';

export interface Task {
  id: number;
  name: string;
  completed: boolean;
  hasVideo: boolean;
  date: string;
  time: string;
  location: string;
}

interface Checklist {
  id: number;
  name: string;
  tasks: Array<Task>;
}

interface Attachment {
  id: number;
  name: string;
  date: string;
  time: string;
  location: string;
  type: 'image' | 'video' | 'document';
}

export interface LevelData {
  name: string;
  color: string;
  attachments: Array<Attachment>;
  checklists: Array<Checklist>;
}

export const Curriculum = () => {
  const [selectedStudent, setSelectedStudent] = useState('Emily');
  const [selectedLevel, setSelectedLevel] = useState<LevelData | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [hideChecked, setHideChecked] = useState<Record<number, boolean>>({});
  const [newChecklistName, setNewChecklistName] = useState('');
  const [isAddingChecklist, setIsAddingChecklist] = useState(false);
  const [editingTask, setEditingTask] = useState<{
    checklistId: number;
    taskId: number;
  } | null>(null);
  const [newTaskName, setNewTaskName] = useState('');

  const students = [
    { name: 'Emily', avatar: '/placeholder.svg?height=32&width=32' },
    { name: 'John', avatar: '/placeholder.svg?height=32&width=32' },
  ];

  const currentLevel = {
    name: 'Green Level',
    color: '#519c66',
    bgColor: 'bg-[#519c66]',
    badge: '/placeholder.svg?height=200&width=160&text=Green+Badge',
  };

  const completedLevels = [
    {
      name: 'Yellow Level',
      color: '#fdd36b',
      bgColor: 'bg-[#fdd36b]',
      badge: '/placeholder.svg?height=200&width=160&text=Yellow+Badge',
    },
    {
      name: 'Red Level',
      color: '#e45b5b',
      bgColor: 'bg-[#e45b5b]',
      badge: '/placeholder.svg?height=200&width=160&text=Red+Badge',
    },
    {
      name: 'Rainbow Level',
      color: '#9747ff',
      bgColor: 'bg-gradient-to-br from-[#9747ff] to-[#e45b5b]',
      badge: '/placeholder.svg?height=200&width=160&text=Rainbow+Badge',
    },
  ];

  const upcomingLevels = [
    {
      name: 'Blue Level',
      color: '#2463eb',
      bgColor: 'bg-[#2463eb]',
      badge: '/placeholder.svg?height=200&width=160&text=Blue+Badge',
    },
    {
      name: 'Black Level',
      color: '#282828',
      bgColor: 'bg-[#282828]',
      badge: '/placeholder.svg?height=200&width=160&text=Black+Badge',
    },
    {
      name: 'White Level',
      color: '#ffffff',
      bgColor: 'bg-[#ffffff] border-2 border-[#e7e7e8]',
      badge: '/placeholder.svg?height=200&width=160&text=White+Badge',
    },
  ];

  const [levelData, setLevelData] = useState<Record<string, LevelData>>({
    'Green Level': {
      name: 'Green Level',
      color: '#519c66',
      attachments: [
        {
          id: 1,
          name: 'Image.jpeg',
          date: 'April 18th',
          time: '5pm',
          location: 'Ground Arena',
          type: 'image',
        },
        {
          id: 2,
          name: 'Image.jpeg',
          date: 'April 18th',
          time: '5pm',
          location: 'Ground Arena',
          type: 'image',
        },
      ],
      checklists: [
        {
          id: 1,
          name: 'Basic Trotting Checklist',
          tasks: [
            {
              id: 1,
              name: 'Task 1',
              completed: true,
              hasVideo: true,
              date: 'April 18th',
              time: '4pm - 5pm',
              location: 'Ground Arena',
            },
            {
              id: 2,
              name: 'Task 2',
              completed: true,
              hasVideo: true,
              date: 'April 18th',
              time: '4pm - 5pm',
              location: 'Ground Arena',
            },
            {
              id: 3,
              name: 'Task 3',
              completed: true,
              hasVideo: true,
              date: 'April 18th',
              time: '4pm - 5pm',
              location: 'Ground Arena',
            },
            {
              id: 4,
              name: 'Task 4',
              completed: true,
              hasVideo: true,
              date: 'April 18th',
              time: '4pm - 5pm',
              location: 'Ground Arena',
            },
            {
              id: 5,
              name: 'Task 5',
              completed: false,
              hasVideo: false,
              date: 'April 18th',
              time: '4pm - 5pm',
              location: 'Ground Arena',
            },
            {
              id: 6,
              name: 'Task 6',
              completed: false,
              hasVideo: false,
              date: 'April 18th',
              time: '4pm - 5pm',
              location: 'Ground Arena',
            },
            {
              id: 7,
              name: 'Task 7',
              completed: false,
              hasVideo: false,
              date: 'April 18th',
              time: '4pm - 5pm',
              location: 'Ground Arena',
            },
          ],
        },
        {
          id: 2,
          name: 'Advanced Jumping Checklist',
          tasks: [
            {
              id: 1,
              name: 'Task 1',
              completed: true,
              hasVideo: true,
              date: 'April 18th',
              time: '4pm - 5pm',
              location: 'Ground Arena',
            },
            {
              id: 2,
              name: 'Task 1',
              completed: true,
              hasVideo: true,
              date: 'April 18th',
              time: '4pm - 5pm',
              location: 'Ground Arena',
            },
            {
              id: 3,
              name: 'Task 1',
              completed: true,
              hasVideo: true,
              date: 'April 18th',
              time: '4pm - 5pm',
              location: 'Ground Arena',
            },
            {
              id: 4,
              name: 'Task 1',
              completed: true,
              hasVideo: true,
              date: 'April 18th',
              time: '4pm - 5pm',
              location: 'Ground Arena',
            },
            {
              id: 5,
              name: 'Task 1',
              completed: false,
              hasVideo: false,
              date: 'April 18th',
              time: '4pm - 5pm',
              location: 'Ground Arena',
            },
            {
              id: 6,
              name: 'Task 1',
              completed: false,
              hasVideo: false,
              date: 'April 18th',
              time: '4pm - 5pm',
              location: 'Ground Arena',
            },
          ],
        },
      ],
    },
  });

  const getLevelDetails = (levelName: string): LevelData => {
    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    if (levelData[levelName]?.name) {
      return levelData[levelName];
    }

    // Create default data for levels that don't exist yet
    const defaultLevel: LevelData = {
      name: levelName,
      color: levelName.includes('Green')
        ? '#519c66'
        : levelName.includes('Yellow')
          ? '#fdd36b'
          : levelName.includes('Red')
            ? '#e45b5b'
            : levelName.includes('Rainbow')
              ? '#9747ff'
              : levelName.includes('Blue')
                ? '#2463eb'
                : levelName.includes('Black')
                  ? '#282828'
                  : '#ffffff',
      attachments: [],
      checklists: [],
    };

    setLevelData((prev) => ({ ...prev, [levelName]: defaultLevel }));
    return defaultLevel;
  };

  const calculateProgress = (tasks: Array<Task>) => {
    if (tasks.length === 0) return 0;
    const completedTasks = tasks.filter((task) => task.completed).length;
    return Math.round((completedTasks / tasks.length) * 100);
  };

  const toggleTask = (checklistId: number, taskId: number) => {
    if (!selectedLevel) return;

    const updatedLevel = { ...selectedLevel };
    const checklist = updatedLevel.checklists.find((c) => c.id === checklistId);
    if (checklist) {
      const task = checklist.tasks.find((t) => t.id === taskId);
      if (task) {
        task.completed = !task.completed;
        if (task.completed) {
          task.hasVideo = true; // Simulate video being added when task is completed
          toast.success(`${task.name} has been marked as complete.`);
        } else {
          task.hasVideo = false;
        }
      }
    }

    setSelectedLevel(updatedLevel);
    setLevelData((prev) => ({ ...prev, [selectedLevel.name]: updatedLevel }));
  };

  const toggleHideChecked = (checklistId: number) => {
    setHideChecked((prev) => ({ ...prev, [checklistId]: !prev[checklistId] }));
  };

  const deleteChecklist = (checklistId: number) => {
    if (!selectedLevel) return;

    const updatedLevel = { ...selectedLevel };
    updatedLevel.checklists = updatedLevel.checklists.filter(
      (c) => c.id !== checklistId,
    );

    setSelectedLevel(updatedLevel);
    setLevelData((prev) => ({ ...prev, [selectedLevel.name]: updatedLevel }));

    toast.success('The checklist has been removed successfully.');
  };

  const addChecklist = () => {
    if (!selectedLevel || !newChecklistName.trim()) return;

    const updatedLevel = { ...selectedLevel };
    const newChecklist: Checklist = {
      id: Date.now(),
      name: newChecklistName,
      tasks: [],
    };

    updatedLevel.checklists.push(newChecklist);
    setSelectedLevel(updatedLevel);
    setLevelData((prev) => ({ ...prev, [selectedLevel.name]: updatedLevel }));
    setNewChecklistName('');
    setIsAddingChecklist(false);

    toast.success(`${newChecklistName} has been created successfully.`);
  };

  const addTask = (checklistId: number) => {
    if (!selectedLevel) return;

    const updatedLevel = { ...selectedLevel };
    const checklist = updatedLevel.checklists.find((c) => c.id === checklistId);
    if (checklist) {
      const newTask: Task = {
        id: Date.now(),
        name: `Task ${checklist.tasks.length + 1}`,
        completed: false,
        hasVideo: false,
        date: 'April 18th',
        time: '4pm - 5pm',
        location: 'Ground Arena',
      };
      checklist.tasks.push(newTask);
    }

    setSelectedLevel(updatedLevel);
    setLevelData((prev) => ({ ...prev, [selectedLevel.name]: updatedLevel }));

    toast.success('New task has been added to the checklist.');
  };

  const updateTaskName = (
    checklistId: number,
    taskId: number,
    newName: string,
  ) => {
    if (!selectedLevel) return;

    const updatedLevel = { ...selectedLevel };
    const checklist = updatedLevel.checklists.find((c) => c.id === checklistId);
    if (checklist) {
      const task = checklist.tasks.find((t) => t.id === taskId);
      if (task) {
        task.name = newName;
      }
    }

    setSelectedLevel(updatedLevel);
    setLevelData((prev) => ({ ...prev, [selectedLevel.name]: updatedLevel }));
    setEditingTask(null);
    setNewTaskName('');
  };

  const deleteTask = (checklistId: number, taskId: number) => {
    if (!selectedLevel) return;

    const updatedLevel = { ...selectedLevel };
    const checklist = updatedLevel.checklists.find((c) => c.id === checklistId);
    if (checklist) {
      checklist.tasks = checklist.tasks.filter((t) => t.id !== taskId);
    }

    setSelectedLevel(updatedLevel);
    setLevelData((prev) => ({ ...prev, [selectedLevel.name]: updatedLevel }));

    toast.success('Task has been removed from the checklist.');
  };

  const simulateVideoUpload = (checklistId: number, taskId: number) => {
    if (!selectedLevel) return;

    const updatedLevel = { ...selectedLevel };
    const checklist = updatedLevel.checklists.find((c) => c.id === checklistId);
    if (checklist) {
      const task = checklist.tasks.find((t) => t.id === taskId);
      if (task) {
        task.hasVideo = true;
        toast.success('Video has been successfully uploaded for this task.');
      }
    }

    setSelectedLevel(updatedLevel);
    setLevelData((prev) => ({ ...prev, [selectedLevel.name]: updatedLevel }));
  };

  const addAttachment = () => {
    if (!selectedLevel) return;

    const updatedLevel = { ...selectedLevel };
    const newAttachment: Attachment = {
      id: Date.now(),
      name: 'New_Image.jpeg',
      date: 'April 18th',
      time: '5pm',
      location: 'Ground Arena',
      type: 'image',
    };

    updatedLevel.attachments.push(newAttachment);
    setSelectedLevel(updatedLevel);
    setLevelData((prev) => ({ ...prev, [selectedLevel.name]: updatedLevel }));

    toast.success('New file has been attached successfully.');
  };

  const deleteAttachment = (attachmentId: number) => {
    if (!selectedLevel) return;

    const updatedLevel = { ...selectedLevel };
    updatedLevel.attachments = updatedLevel.attachments.filter(
      (a) => a.id !== attachmentId,
    );

    setSelectedLevel(updatedLevel);
    setLevelData((prev) => ({ ...prev, [selectedLevel.name]: updatedLevel }));

    toast.success('File has been removed successfully.');
  };

  return (
    <div className='mx-auto w-full bg-[#ffffff] p-6'>
      {/* Student Selection */}
      <div className='mb-8 flex gap-4'>
        {students.map((student) => (
          <Button
            key={student.name}
            variant='ghost'
            onClick={() => {
              setSelectedStudent(student.name);
            }}
            className={`flex items-center gap-3 rounded-full border py-5 pr-4 pl-1 transition-all ${
              selectedStudent === student.name
                ? 'border-[#282828] bg-[#282828] text-white'
                : 'border-[#e7e7e8] bg-[#f4f4f5] text-[#8b8d97] hover:bg-[#e7e7e8]'
            }`}
          >
            <Avatar>
              <AvatarImage src={student.avatar} />
              <AvatarFallback>{student.name.charAt(0)}</AvatarFallback>
            </Avatar>
            {student.name}
          </Button>
        ))}
      </div>

      {/* Current Level */}
      <LevelCardSection
        title='Current Horsemanship Level'
        levels={[currentLevel]}
        getLevelDetails={getLevelDetails}
        setSelectedLevel={setSelectedLevel}
        setIsModalOpen={setIsModalOpen}
        type='current'
      />

      {/* Completed Levels */}
      <LevelCardSection
        title='Completed'
        levels={completedLevels}
        getLevelDetails={getLevelDetails}
        setSelectedLevel={setSelectedLevel}
        setIsModalOpen={setIsModalOpen}
        type='completed'
      />

      {/* Upcoming Levels */}
      <LevelCardSection
        title='Up Next'
        levels={upcomingLevels}
        getLevelDetails={getLevelDetails}
        setSelectedLevel={setSelectedLevel}
        setIsModalOpen={setIsModalOpen}
        type='upcoming'
      />

      {/* Level Details Modal */}
      <LevelDialog
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        selectedLevel={selectedLevel}
        newChecklistName={newChecklistName}
        setNewChecklistName={setNewChecklistName}
        isAddingChecklist={isAddingChecklist}
        setIsAddingChecklist={setIsAddingChecklist}
        addChecklist={addChecklist}
        deleteChecklist={deleteChecklist}
        addTask={addTask}
        toggleTask={toggleTask}
        updateTaskName={updateTaskName}
        deleteTask={deleteTask}
        simulateVideoUpload={simulateVideoUpload}
        addAttachment={addAttachment}
        deleteAttachment={deleteAttachment}
        hideChecked={hideChecked}
        toggleHideChecked={toggleHideChecked}
        newTaskName={newTaskName}
        setNewTaskName={setNewTaskName}
        editingTask={editingTask}
        setEditingTask={setEditingTask}
        calculateProgress={calculateProgress}
      />
    </div>
  );
};
