import {
  Camera,
  Edit,
  Paperclip,
  Play,
  Plus,
  Settings,
  Trash2,
} from 'lucide-react';
import { type Dispatch, type SetStateAction } from 'react';

import { type LevelData, type Task } from '@/components/curriculum';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';

interface EditingTask {
  checklistId: number;
  taskId: number;
}

export interface LevelDialogProps {
  isModalOpen: boolean;
  setIsModalOpen: Dispatch<SetStateAction<boolean>>;

  selectedLevel: LevelData | null;

  newChecklistName: string;
  setNewChecklistName: Dispatch<SetStateAction<string>>;

  isAddingChecklist: boolean;
  setIsAddingChecklist: Dispatch<SetStateAction<boolean>>;

  hideChecked: Record<string, boolean>;
  toggleHideChecked: (checklistId: number) => void;

  newTaskName: string;
  setNewTaskName: Dispatch<SetStateAction<string>>;

  editingTask: EditingTask | null;
  setEditingTask: Dispatch<SetStateAction<EditingTask | null>>;

  addChecklist: () => void;
  deleteChecklist: (checklistId: number) => void;

  addTask: (checklistId: number) => void;
  toggleTask: (checklistId: number, taskId: number) => void;
  updateTaskName: (checklistId: number, taskId: number, name: string) => void;
  deleteTask: (checklistId: number, taskId: number) => void;

  simulateVideoUpload: (checklistId: number, taskId: number) => void;

  addAttachment: () => void;
  deleteAttachment: (id: number) => void;

  calculateProgress: (tasks: Array<Task>) => number;
}

export function LevelDialog({
  isModalOpen,
  setIsModalOpen,
  selectedLevel,
  newChecklistName,
  setNewChecklistName,
  isAddingChecklist,
  setIsAddingChecklist,
  addChecklist,
  deleteChecklist,
  addTask,
  toggleTask,
  updateTaskName,
  deleteTask,
  simulateVideoUpload,
  addAttachment,
  deleteAttachment,
  hideChecked,
  toggleHideChecked,
  newTaskName,
  setNewTaskName,
  editingTask,
  setEditingTask,
  calculateProgress,
}: LevelDialogProps) {
  return (
    <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
      <DialogContent className='max-h-[90vh] max-w-2xl overflow-y-auto'>
        <DialogHeader className='flex flex-row items-center justify-between'>
          <DialogTitle className='flex items-center gap-2'>
            <div
              className='h-6 w-4 rounded-sm'
              style={{ backgroundColor: selectedLevel?.color }}
            ></div>
            {selectedLevel?.name}
          </DialogTitle>
        </DialogHeader>

        {selectedLevel && (
          <div className='space-y-6'>
            {/* Action Buttons */}
            <div className='flex gap-3'>
              {isAddingChecklist ? (
                <div className='flex items-center gap-2'>
                  <Input
                    placeholder='Checklist name'
                    value={newChecklistName}
                    onChange={(e) => {
                      setNewChecklistName(e.target.value);
                    }}
                    className='w-48'
                  />
                  <Button onClick={addChecklist} size='sm'>
                    Add
                  </Button>
                  <Button
                    variant='outline'
                    onClick={() => {
                      setIsAddingChecklist(false);
                    }}
                    size='sm'
                  >
                    Cancel
                  </Button>
                </div>
              ) : (
                <Button
                  variant='outline'
                  className='flex items-center gap-2 bg-transparent'
                  onClick={() => {
                    setIsAddingChecklist(true);
                  }}
                >
                  <Plus className='h-4 w-4' />
                  Add Checklist
                </Button>
              )}

              <Button
                variant='outline'
                className='flex items-center gap-2 bg-transparent'
                onClick={addAttachment}
              >
                <Paperclip className='h-4 w-4' />
                Attach
              </Button>
            </div>

            {/* Attachments */}
            {selectedLevel.attachments.length > 0 && (
              <div className='space-y-3'>
                {selectedLevel.attachments.map((attachment) => (
                  <div
                    key={attachment.id}
                    className='flex items-center gap-3 rounded-lg bg-gray-50 p-3'
                  >
                    <img
                      src='/placeholder.svg?height=40&width=40&text=IMG'
                      alt='Attachment'
                      className='size-10 rounded'
                    />
                    <div className='flex-1'>
                      <div className='font-medium'>{attachment.name}</div>
                      <div className='text-sm text-gray-500'>
                        {attachment.date} • {attachment.time} •{' '}
                        {attachment.location}
                      </div>
                    </div>
                    <Button
                      variant='ghost'
                      size='icon'
                      onClick={() => {
                        deleteAttachment(attachment.id);
                      }}
                    >
                      <Trash2 className='h-4 w-4' />
                    </Button>
                  </div>
                ))}
              </div>
            )}

            {/* Checklists */}
            {selectedLevel.checklists.map((checklist) => {
              const progress = calculateProgress(checklist.tasks);
              const visibleTasks = hideChecked[checklist.id]
                ? checklist.tasks.filter((task) => !task.completed)
                : checklist.tasks;

              return (
                <div key={checklist.id} className='space-y-4'>
                  <div className='flex items-center justify-between'>
                    <h3 className='text-lg font-semibold'>{checklist.name}</h3>
                    <div className='flex gap-2'>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => {
                          toggleHideChecked(checklist.id);
                        }}
                      >
                        {hideChecked[checklist.id]
                          ? 'Show Checked'
                          : 'Hide Checked'}
                      </Button>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => {
                          deleteChecklist(checklist.id);
                        }}
                      >
                        Delete
                      </Button>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => {
                          addTask(checklist.id);
                        }}
                      >
                        <Plus className='h-4 w-4' />
                      </Button>
                    </div>
                  </div>

                  <div className='space-y-2'>
                    <div className='flex items-center gap-2'>
                      <span className='text-sm font-medium'>{progress}%</span>
                      <Progress value={progress} className='flex-1' />
                    </div>
                  </div>

                  <div className='space-y-3'>
                    {visibleTasks.map((task) => (
                      <div
                        key={task.id}
                        className='flex items-center gap-3 rounded-lg border p-3'
                      >
                        <Checkbox
                          checked={task.completed}
                          onCheckedChange={() => {
                            toggleTask(checklist.id, task.id);
                          }}
                        />
                        <div className='flex-1'>
                          {editingTask?.checklistId === checklist.id &&
                          editingTask.taskId === task.id ? (
                            <div className='space-y-2'>
                              <Input
                                value={newTaskName || task.name}
                                onChange={(e) => {
                                  setNewTaskName(e.target.value);
                                }}
                                onKeyDown={(e) => {
                                  if (e.key === 'Enter') {
                                    updateTaskName(
                                      checklist.id,
                                      task.id,
                                      newTaskName || task.name,
                                    );
                                  }
                                  if (e.key === 'Escape') {
                                    setEditingTask(null);
                                    setNewTaskName('');
                                  }
                                }}
                                className='font-medium'
                              />
                              <div className='flex gap-2'>
                                <Button
                                  size='sm'
                                  onClick={() => {
                                    updateTaskName(
                                      checklist.id,
                                      task.id,
                                      newTaskName || task.name,
                                    );
                                  }}
                                >
                                  Save
                                </Button>
                                <Button
                                  variant='outline'
                                  size='sm'
                                  onClick={() => {
                                    setEditingTask(null);
                                    setNewTaskName('');
                                  }}
                                >
                                  Cancel
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <>
                              <div className='font-medium'>{task.name}</div>
                              <div className='text-sm text-gray-500'>
                                {task.date} • {task.time} • {task.location}
                              </div>
                            </>
                          )}
                        </div>

                        {task.completed && task.hasVideo ? (
                          <div className='flex items-center gap-2'>
                            <div className='relative'>
                              <img
                                src='/placeholder.svg?height=40&width=40&text=Video'
                                alt='Video thumbnail'
                                className='size-10 rounded'
                              />
                              <Play className='absolute inset-0 m-auto h-4 w-4 text-white' />
                            </div>
                            <img
                              src='/placeholder.svg?height=24&width=24&text=User'
                              alt='Instructor'
                              className='size-6 rounded-full'
                            />
                          </div>
                        ) : (
                          <div className='flex items-center gap-2'>
                            <Button
                              variant='ghost'
                              size='icon'
                              onClick={() => {
                                simulateVideoUpload(checklist.id, task.id);
                              }}
                            >
                              <Camera className='h-4 w-4' />
                            </Button>
                            <Button variant='ghost' size='icon'>
                              <Settings className='h-4 w-4' />
                            </Button>
                          </div>
                        )}

                        <div className='flex items-center gap-1'>
                          <Button
                            variant='ghost'
                            size='icon'
                            onClick={() => {
                              setEditingTask({
                                checklistId: checklist.id,
                                taskId: task.id,
                              });
                              setNewTaskName(task.name);
                            }}
                          >
                            <Edit className='h-4 w-4' />
                          </Button>
                          <Button
                            variant='ghost'
                            size='icon'
                            onClick={() => {
                              deleteTask(checklist.id, task.id);
                            }}
                          >
                            <Trash2 className='h-4 w-4' />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}

            {selectedLevel.checklists.length === 0 && (
              <div className='py-8 text-center text-gray-500'>
                <p>
                  No checklists yet. Click &quot;Add Checklist&quot; to get
                  started!
                </p>
              </div>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
