'use client';

import { Check } from 'lucide-react';

import { type LevelData } from '@/components/curriculum/index';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface LevelItem {
  name: string;
  bgColor: string;
}

interface LevelCardSectionProps {
  title: string;
  levels: Array<LevelItem>;
  getLevelDetails: (levelName: string) => LevelData;
  setSelectedLevel: (level: LevelData) => void;
  setIsModalOpen: (value: boolean) => void;
  type: 'current' | 'completed' | 'upcoming';
}

export function LevelCardSection({
  title,
  levels,
  getLevelDetails,
  setSelectedLevel,
  setIsModalOpen,
  type,
}: LevelCardSectionProps) {
  return (
    <div className='mb-8'>
      <h2 className='mb-6 text-2xl font-semibold text-[#282828]'>{title}</h2>
      <div className='flex flex-wrap gap-6'>
        {levels.map((level) => (
          <Card
            key={level.name}
            className='w-72 cursor-pointer gap-2 overflow-hidden rounded-2xl px-6 transition-transform hover:scale-101'
            onClick={() => {
              const levelDetails = getLevelDetails(level.name);
              setSelectedLevel(levelDetails);
              setIsModalOpen(true);
            }}
          >
            <CardHeader className='p-0'>
              <CardTitle className='flex items-center justify-between'>
                <h3 className='text-lg font-medium text-[#282828]'>
                  {level.name}
                </h3>

                {type === 'current' && (
                  <div className='flex h-6 w-6 items-center justify-center rounded-full bg-[#519c66]'>
                    <div className='h-3 w-3 rounded-full bg-white'></div>
                  </div>
                )}

                {type === 'completed' && (
                  <div className='flex size-6 items-center justify-center rounded-full bg-[#2463eb]'>
                    <Check className='size-4 text-white' />
                  </div>
                )}
                {/* type === 'upcoming' does not render an icon */}
              </CardTitle>
            </CardHeader>

            <CardContent className={`${level.bgColor} rounded-2xl pb-2`}>
              <div className='flex justify-center'>
                <img
                  src={`/learning-levels/${level.name
                    .toLowerCase()
                    .replace(' level', '')}.png`}
                  alt={level.name}
                  className='h-44 w-auto rotate-12'
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
