import { useAuthStore } from '@/stores/authStore';

export const DashboardBanner = () => {
  const { user } = useAuthStore();
  return (
    <div className='relative overflow-hidden rounded-2xl bg-gradient-to-r from-amber-400 via-yellow-400 to-amber-300 p-8 text-white'>
      <div className='relative z-10'>
        <h1 className='mb-2 text-4xl font-bold'>
          Welcome back, {user?.first_name}!
        </h1>
        <p className='text-lg opacity-90'>
          Track your children&apos;s riding progress
        </p>
      </div>

      {/* Decorative elements */}
      <div className='absolute top-4 right-20 text-white/30'>
        <div className='text-2xl'>✦</div>
      </div>
      <div className='absolute top-8 right-32 text-white/20'>
        <div className='text-lg'>✦</div>
      </div>
      <div className='absolute top-12 right-48 text-white/25'>
        <div className='text-xl'>✦</div>
      </div>

      {/* Horse riders illustration */}
      <div className='absolute top-0 right-0 h-full w-1/2 opacity-80'>
        {/* <Image
          src='/images/horse-riders.png'
          alt='Horse riders'
          fill
          className='object-cover object-right' */}
        {/* /> */}
      </div>
    </div>
  );
};
