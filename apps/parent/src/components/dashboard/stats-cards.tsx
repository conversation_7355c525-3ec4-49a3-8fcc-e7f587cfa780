import { BookOpen, Calendar, Users } from 'lucide-react';

import { Card, CardContent } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

export const StatsCards = () => {
  const stats = [
    {
      title: 'Upcoming lessons',
      value: '4',
      subtitle: 'This Week',
      icon: Calendar,
      iconColor: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Total Lessons Booked',
      value: '6',
      subtitle: '',
      icon: BookOpen,
      iconColor: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Total Students',
      value: '2',
      subtitle: '',
      icon: Users,
      iconColor: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
  ];

  return (
    <div className='grid grid-cols-1 gap-6 md:grid-cols-3'>
      {stats.map((stat, index) => (
        <Card key={index} className='border-0 shadow-sm'>
          <CardContent className=''>
            <div className='flex items-center justify-between'>
              <div className='mb-4 flex items-center gap-3'>
                <div className={`rounded-lg p-2 ${stat.bgColor}`}>
                  <stat.icon className={`size-5 ${stat.iconColor}`} />
                </div>
                <div>
                  <p className='text-sm font-medium text-gray-600'>
                    {stat.title}
                  </p>
                </div>
              </div>
              {index === 0 && (
                <Select>
                  <SelectTrigger className='border-none focus-visible:ring-0'>
                    <SelectValue placeholder='Select a week' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='week'>This Week</SelectItem>
                    <SelectItem value='month'>This Month</SelectItem>
                    <SelectItem value='year'>This Year</SelectItem>
                  </SelectContent>
                </Select>
              )}
            </div>
            <p className='text-4xl font-bold text-gray-900'>{stat.value}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
