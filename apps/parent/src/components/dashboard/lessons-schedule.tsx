import { ArrowR<PERSON>, ChevronLeft, ChevronRight } from 'lucide-react';
import { useMemo, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export const LessonsSchedule = () => {
  const [selectedFilter, setSelectedFilter] = useState('Emily');
  const [currentWeekStart, setCurrentWeekStart] = useState(() => {
    // Start with April 13, 2025 (Sunday)
    return new Date(2025, 3, 13); // Month is 0-indexed
  });
  const [selectedDate, setSelectedDate] = useState<number | null>(17);

  const filters = [
    { name: 'All', avatar: null },
    { name: '<PERSON>', avatar: '/placeholder.svg?height=32&width=32' },
    { name: 'John', avatar: '/placeholder.svg?height=32&width=32' },
  ];

  // Generate week days based on current week start
  const weekDays = useMemo(() => {
    const days = [];
    const dayNames = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];

    for (let i = 0; i < 7; i++) {
      const date = new Date(currentWeekStart);
      date.setDate(currentWeekStart.getDate() + i);
      days.push({
        day: dayNames[i],
        date: date.getDate(),
        fullDate: new Date(date),
        hasEvent: i === 4 || i === 5, // Thursday and Friday have events
      });
    }
    return days;
  }, [currentWeekStart]);

  // Format date range for display
  const dateRangeText = useMemo(() => {
    const endDate = new Date(currentWeekStart);
    endDate.setDate(currentWeekStart.getDate() + 6);

    const formatDate = (date: Date) => {
      const day = date.getDate();
      const suffix =
        day === 1 || day === 21 || day === 31
          ? 'st'
          : day === 2 || day === 22
            ? 'nd'
            : day === 3 || day === 23
              ? 'rd'
              : 'th';
      return `${day}${suffix}`;
    };

    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    const startMonth = months[currentWeekStart.getMonth()];
    const endMonth = months[endDate.getMonth()];
    const year = endDate.getFullYear().toString().slice(-2);

    if (startMonth === endMonth) {
      return `${formatDate(currentWeekStart)} - ${formatDate(endDate)} ${startMonth} ${year}`;
    } else {
      return `${formatDate(currentWeekStart)} ${startMonth} - ${formatDate(endDate)} ${endMonth} ${year}`;
    }
  }, [currentWeekStart]);

  // Navigate to previous week
  const goToPreviousWeek = () => {
    const newDate = new Date(currentWeekStart);
    newDate.setDate(currentWeekStart.getDate() - 7);
    setCurrentWeekStart(newDate);
    setSelectedDate(null); // Clear selection when changing weeks
  };

  // Navigate to next week
  const goToNextWeek = () => {
    const newDate = new Date(currentWeekStart);
    newDate.setDate(currentWeekStart.getDate() + 7);
    setCurrentWeekStart(newDate);
    setSelectedDate(null); // Clear selection when changing weeks
  };

  // Generate lessons based on selected date and week
  const lessons = useMemo(() => {
    const selectedDay = weekDays.find((day) => day.date === selectedDate);
    if (!selectedDay?.hasEvent) return [];

    const formatDate = (date: Date) => {
      const months = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
      ];
      const day = date.getDate();
      const suffix =
        day === 1 || day === 21 || day === 31
          ? 'st'
          : day === 2 || day === 22
            ? 'nd'
            : day === 3 || day === 23
              ? 'rd'
              : 'th';
      return `${months[date.getMonth()]} ${day}${suffix}`;
    };

    return [
      {
        title: 'Advanced Jumping',
        date: formatDate(selectedDay.fullDate),
        time: '4pm - 5pm',
        location: 'Ground Arena',
        image: '/placeholder.svg?height=80&width=80&text=Horse+Jumping',
        bgColor: 'bg-[#fdd36b]/20',
      },
      {
        title: 'Basic Trotting',
        date: formatDate(selectedDay.fullDate),
        time: '4pm - 5pm',
        location: 'Ground Arena',
        image: '/placeholder.svg?height=80&width=80&text=Horse+Trotting',
        bgColor: 'bg-[#519c66]/20',
      },
    ];
  }, [selectedDate, weekDays]);

  return (
    <Card className='mx-auto w-full max-w-6xl'>
      {/* Header */}
      <CardHeader className='flex items-center justify-between'>
        <CardTitle className='text-3xl font-semibold text-[#282828]'>
          Lessons Schedule
        </CardTitle>
        <Button
          variant='ghost'
          className='p-0 text-[#8b8d97] hover:text-[#282828]'
        >
          View All <ArrowRight className='ml-2 size-4' />
        </Button>
      </CardHeader>

      {/* Filter Tabs */}
      <CardContent>
        <div className='mb-8 flex gap-4'>
          {filters.map((filter) => (
            <Button
              key={filter.name}
              variant='ghost'
              onClick={() => {
                setSelectedFilter(filter.name);
              }}
              className={`flex items-center gap-3 rounded-full border px-6 py-3 transition-all ${
                selectedFilter === filter.name
                  ? 'border-[#282828] bg-[#282828] text-white'
                  : 'border-[#e7e7e8] bg-[#f4f4f5] text-[#8b8d97] hover:bg-[#e7e7e8]'
              }`}
            >
              {filter.avatar && (
                <img
                  src={filter.avatar || '/placeholder.svg'}
                  alt={filter.name}
                  className='size-6 rounded-full'
                />
              )}
              {filter.name}
            </Button>
          ))}
        </div>

        {/* Date Navigation */}
        <div className='mb-8 flex items-center justify-center gap-8'>
          <Button
            variant='ghost'
            size='icon'
            className='text-[#8b8d97] hover:text-[#282828]'
            onClick={goToPreviousWeek}
          >
            <ChevronLeft className='h-5 w-5' />
          </Button>
          <h2 className='min-w-[200px] text-center text-xl font-medium text-[#282828]'>
            {dateRangeText}
          </h2>
          <Button
            variant='ghost'
            size='icon'
            className='text-[#8b8d97] hover:text-[#282828]'
            onClick={goToNextWeek}
          >
            <ChevronRight className='h-5 w-5' />
          </Button>
        </div>

        {/* Weekly Calendar */}
        <div className='mb-8 grid grid-cols-7 gap-4'>
          {weekDays.map((day) => (
            <div
              key={`${day.fullDate.getTime()}`}
              className={`flex cursor-pointer flex-col items-center justify-center gap-1 rounded-2xl p-4 text-center transition-all ${
                selectedDate === day.date
                  ? 'bg-[#282828] text-white'
                  : 'bg-[#f4f4f5] text-[#8b8d97] hover:bg-[#e7e7e8]'
              }`}
              onClick={() => {
                setSelectedDate(day.date);
              }}
            >
              <div className='text-sm font-medium'>{day.day}</div>
              {day.hasEvent ? (
                <div className='size-2 rounded-full bg-[#fdd36b]' />
              ) : (
                <div className='size-2 rounded-full bg-transparent' />
              )}
              <div className='text-2xl font-semibold'>{day.date}</div>
            </div>
          ))}
        </div>

        {/* Lesson Cards */}
        {lessons.length > 0 ? (
          <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
            {lessons.map((lesson, index) => (
              <Card
                key={index}
                className={`border-0 p-6 ${lesson.bgColor} rounded-2xl`}
              >
                <div className='flex items-center justify-between'>
                  <div className='flex-1'>
                    <h3 className='mb-2 text-xl font-semibold text-[#282828]'>
                      {lesson.title}
                    </h3>
                    <div className='space-y-1 text-[#8b8d97]'>
                      <div>{lesson.date}</div>
                      <div>{lesson.time}</div>
                      <div>{lesson.location}</div>
                    </div>
                  </div>
                  <div className='ml-4'>
                    <img
                      src={lesson.image || '/placeholder.svg'}
                      alt={lesson.title}
                      className='size-20 rounded-full'
                    />
                  </div>
                </div>
              </Card>
            ))}
          </div>
        ) : (
          <div className='py-12 text-center text-[#8b8d97]'>
            <p className='text-lg'>No lessons scheduled for this date</p>
            <p className='mt-2 text-sm'>
              Select a date with an indicator to view lessons
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
