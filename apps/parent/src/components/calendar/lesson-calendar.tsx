import {
  addDays,
  addMonths,
  addWeeks,
  endOfWeek,
  format,
  getDay,
  parse,
  startOfWeek,
} from 'date-fns';
import { enUS } from 'date-fns/locale';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useState } from 'react';
import { Calendar, dateFnsLocalizer, type View } from 'react-big-calendar';
import 'react-big-calendar/lib/css/react-big-calendar.css';

import { Button } from '@/components/ui/button';

import { cn } from '@/lib/utils';

import { SlotDialog } from './slot-dialog';

interface CalendarEvent {
  title: string;
  groundName: string;
  start: Date;
  end: Date;
}

const locales = {
  'en-US': enUS,
};

const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek,
  getDay,
  locales,
});

// const myEvents = [
//   {
//     title: 'Math Class',
//     start: new Date(2025, 6, 15, 10, 0),
//     end: new Date(2025, 6, 15, 11, 0),
//   },
// ];

// Custom toolbar component with simplified typing
interface CustomToolbarProps {
  label: string;
  view: string;
  views: Array<string>;
  onView: (view: View) => void;
  onNavigate: (action: 'PREV' | 'NEXT' | 'TODAY') => void;
  date: Date;
}

const CustomToolbar = ({
  onNavigate,
  label,
  onView,
  view,
  date,
}: CustomToolbarProps) => {
  const goToBack = () => {
    onNavigate('PREV');
  };

  const goToNext = () => {
    onNavigate('NEXT');
  };

  const goToToday = () => {
    onNavigate('TODAY');
  };

  // Format the date based on the current view
  const getViewSpecificDateLabel = () => {
    switch (view) {
      case 'month':
        return format(date, 'MMMM yyyy');
      case 'week': {
        const start = startOfWeek(date, { locale: enUS });
        const end = endOfWeek(date, { locale: enUS });
        return `${format(start, 'MMM d')} - ${format(end, 'MMM d, yyyy')}`;
      }
      case 'day':
        return format(date, 'MMMM d, yyyy');
      case 'agenda': {
        const start = startOfWeek(date, { locale: enUS });
        const end = addDays(start, 6);
        return `${format(start, 'MMM d')} - ${format(end, 'MMM d, yyyy')}`;
      }
      default:
        return label;
    }
  };

  return (
    <div className='mb-4 flex items-center justify-between'>
      <div className='flex items-center gap-2'>
        <Button onClick={goToToday} variant='outline' size='sm'>
          Today
        </Button>
      </div>
      <div className='flex items-center gap-2'>
        <Button
          onClick={goToBack}
          size='sm'
          className={cn(
            'cursor-pointer rounded-r-none bg-[#F4F4F5] text-[#282828] hover:bg-[#282828] hover:text-[#F4F4F5]',
          )}
        >
          <ChevronLeft className='size-6' />
        </Button>
        <div className='text-lg font-medium text-[#282828]'>
          {getViewSpecificDateLabel()}
        </div>
        <Button
          onClick={goToNext}
          size='sm'
          className={cn(
            'cursor-pointer rounded-l-none bg-[#F4F4F5] text-[#282828] hover:bg-[#282828] hover:text-[#F4F4F5]',
          )}
        >
          <ChevronRight className='size-6' />
        </Button>
      </div>
      <div className='flex gap-2'>
        <Button
          onClick={() => {
            onView('month');
          }}
          variant={view === 'month' ? 'default' : 'outline'}
          size='sm'
          className=''
        >
          Month
        </Button>
        <Button
          onClick={() => {
            onView('week');
          }}
          variant={view === 'week' ? 'default' : 'outline'}
          size='sm'
        >
          Week
        </Button>
        <Button
          onClick={() => {
            onView('day');
          }}
          variant={view === 'day' ? 'default' : 'outline'}
          size='sm'
        >
          Day
        </Button>
        <Button
          onClick={() => {
            onView('agenda');
          }}
          variant={view === 'agenda' ? 'default' : 'outline'}
          size='sm'
        >
          Agenda
        </Button>
      </div>
    </div>
  );
};

const CustomTimeGutterHeader = () => {
  return (
    <div className='rbc-time-header-gutter flex flex-col items-start justify-center p-2'>
      <div className='text-xs font-medium text-gray-500'>EST</div>
      <div className='text-xs text-gray-400'>GMT-5</div>
    </div>
  );
};

const CustomDayHeader = ({ date }: { date: Date }) => {
  const dayName = format(date, 'EEE').toUpperCase(); // SUN, MON, TUE etc.
  const dayNumber = format(date, 'd'); // 21, 22, 23 etc.
  const isToday =
    format(date, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd');

  return (
    <div
      className={cn(
        'flex min-h-[80px] w-full flex-col justify-center border-r border-b border-gray-200 p-2 text-center',
        isToday ? 'bg-blue-50' : 'bg-white',
      )}
    >
      <div
        className={cn(
          'mb-1 text-sm font-semibold',
          isToday ? 'text-blue-600' : 'text-gray-700',
        )}
      >
        {dayName}
      </div>
      <div
        className={cn(
          'text-2xl font-bold',
          isToday ? 'text-blue-800' : 'text-gray-900',
        )}
      >
        {dayNumber}
      </div>
    </div>
  );
};

export const LessonCalendar = () => {
  const [view, setView] = useState<View>('week');
  const [date, setDate] = useState<Date>(new Date());
  const [dialogOpen, setDialogOpen] = useState(false);
  const [events, setEvents] = useState<Array<CalendarEvent>>([]);
  const [slotInfo, setSlotInfo] = useState<{ start: Date; end: Date } | null>(
    null,
  );

  const handleSelectSlot = (slotInfo: { start: Date; end: Date }) => {
    setSlotInfo(slotInfo);
    setDialogOpen(true);
  };

  // Navigate based on the current view
  const handleNavigate = (action: 'PREV' | 'NEXT' | 'TODAY') => {
    let newDate = new Date(date);

    if (action === 'TODAY') {
      newDate = new Date();
    } else {
      switch (view) {
        case 'month':
          // Navigate by month
          newDate =
            action === 'PREV' ? addMonths(date, -1) : addMonths(date, 1);
          break;
        case 'week':
          // Navigate by week
          newDate = action === 'PREV' ? addWeeks(date, -1) : addWeeks(date, 1);
          break;
        case 'day':
          // Navigate by day
          newDate = action === 'PREV' ? addDays(date, -1) : addDays(date, 1);
          break;
        case 'agenda':
          // Navigate by week for agenda view
          newDate = action === 'PREV' ? addWeeks(date, -1) : addWeeks(date, 1);
          break;
      }
    }

    setDate(newDate);
  };

  // Custom toolbar props adapter
  const toolbarProps = {
    view,
    views: ['month', 'week', 'day', 'agenda'],
    onView: (newView: View) => {
      setView(newView);
    },
    onNavigate: handleNavigate,
    label: format(date, 'MMMM yyyy'),
    date,
  };
  const handleAddEvent = (newEvent: CalendarEvent) => {
    setEvents((prev) => [...prev, newEvent]);
  };

  return (
    <div>
      <style>
        {`
          /* Fix top row visibility */
          .calendar-fixed-header .rbc-time-header {
            padding-top: 0;
            margin-top: 0;
            border-top: 1px solid #e2e8f0;
          }

          .calendar-fixed-header .rbc-time-header-content {
            border-left: 1px solid #e2e8f0;
          }

          .calendar-fixed-header .rbc-header {
            padding: 0;
            height: auto;
            border-bottom: none;
          }

          .calendar-fixed-header .rbc-row-content {
            z-index: 1;
          }

          /* Improve time gutter appearance */
          .calendar-fixed-header .rbc-time-gutter {
            font-size: 12px;
          }

          /* Ensure day headers are properly visible */
          .calendar-fixed-header .rbc-time-header-cell {
            min-height: 80px;
          }

          /* Fix alignment issues in the time column */
          .calendar-fixed-header .rbc-time-header-gutter {
            min-width: 70px;
          }

          /* Fix header row styling */
          .calendar-fixed-header .rbc-time-header-content .rbc-row {
            min-height: 80px;
            display: flex;
            align-items: stretch;
          }

          .calendar-fixed-header .rbc-time-header-content .rbc-allday-cell .rbc-row-content {
            display: none;
          }
        `}
      </style>
      <SlotDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        slotInfo={slotInfo}
        onAddEvent={handleAddEvent}
      />
      <div className='p-6'>
        <CustomToolbar {...toolbarProps} />
        <Calendar
          events={events}
          localizer={localizer}
          views={['month', 'week', 'day', 'agenda']}
          view={view}
          onView={setView}
          date={date}
          onNavigate={setDate}
          selectable
          onSelectSlot={handleSelectSlot}
          startAccessor='start'
          endAccessor='end'
          style={{ height: 600 }}
          className='custom-calendar calendar-fixed-header'
          min={new Date(2025, 0, 1, 8, 0)}
          max={new Date(2025, 0, 1, 18, 0)}
          toolbar={false}
          eventPropGetter={() => ({
            style: {
              backgroundColor: '#FDD36B',
              borderRadius: '0.3rem',
              color: '#000',
              border: 'none',
              padding: '4px',
              minHeight: '60px',
              display: 'flex',
              justifyContent: 'center',
              justifyItems: 'center',
              alignItems: 'center',
            },
          })}
          components={{
            event: ({ event }) => (
              <div>
                <div className='font-semibold'>{event.title}</div>
                <div className='text-xs'>{event.groundName}</div>
                {view === 'month' ? (
                  <div className='text-xs'>
                    {format(event.start, 'h:mm a')} -
                    {format(event.end, 'h:mm a')}
                  </div>
                ) : null}
              </div>
            ),
            timeGutterHeader:
              view === 'week' ? CustomTimeGutterHeader : undefined,
            header: view === 'week' ? CustomDayHeader : undefined,
          }}
        />
      </div>
    </div>
  );
};
