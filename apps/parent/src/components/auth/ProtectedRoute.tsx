import { useNavigate } from '@tanstack/react-router';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';

import { useAuthStoreHook } from '@/hooks/useAuthStore';

// Define the roles that are allowed to access protected routes (for parent app, mainly 'parent')
type ProtectedUserRole = 'parent';

// Type guard to check if a role is a protected role
const isProtectedRole = (role: string): role is ProtectedUserRole => {
  return ['parent'].includes(role as ProtectedUserRole);
};

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: Array<ProtectedUserRole>;
}

export function ProtectedRoute({
  children,
  requiredRoles = [],
}: ProtectedRouteProps) {
  const { user, isAuthenticated, isLoading, getRedirectUrl } =
    useAuthStoreHook();
  const navigate = useNavigate();

  // Get current studio_id from the URL if available
  const urlParams = new URLSearchParams(window.location.search);
  const studioId = urlParams.get('studio_id');

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      // Use stored studio_id first, then fallback to URL, then fallback to 'required'
      const redirectUrl = getRedirectUrl();

      // If we have studio_id in URL but not stored, we should use the URL one
      if (studioId && !redirectUrl.includes(studioId)) {
        void navigate({ to: '/login', search: { studio_id: studioId } });
      } else {
        void navigate({ to: redirectUrl });
      }
    }
  }, [isAuthenticated, isLoading, navigate, studioId, getRedirectUrl]);

  useEffect(() => {
    // Check role-based access if user is authenticated
    if (isAuthenticated && user) {
      // First, check if the user's role is one of the protected roles
      if (!isProtectedRole(user.role)) {
        // User has a role that's not allowed to access protected routes
        void navigate({ to: '/unauthorized' });
        return;
      }

      // Check if password needs to be reset (only for authenticated users on protected routes)
      if (!user.isPasswordSet) {
        // Get current studio_id from URL or store
        const urlParams = new URLSearchParams(window.location.search);
        const currentStudioId = urlParams.get('studio_id') ?? user.studio_id;

        // Redirect to reset password page with studio_id
        void navigate({
          to: '/reset-password',
          search: { studio_id: currentStudioId },
        });
        return;
      }

      // Then check if specific roles are required
      if (requiredRoles.length > 0) {
        const userRole = user.role;
        if (isProtectedRole(userRole) && !requiredRoles.includes(userRole)) {
          // User doesn't have any of the required roles
          void navigate({ to: '/unauthorized' });
        }
      }
    }
  }, [user, isAuthenticated, requiredRoles, navigate]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className='flex min-h-screen items-center justify-center bg-gray-50'>
        <div className='text-center'>
          <Loader2 className='mx-auto mb-4 h-8 w-8 animate-spin' />
          <p className='text-gray-600'>Loading...</p>
        </div>
      </div>
    );
  }

  // Don't render children if user is not authenticated or doesn't exist
  if (!isAuthenticated || !user) {
    return null;
  }

  // Check if user has a protected role
  if (!isProtectedRole(user.role)) {
    return null;
  }

  // Check if user has any of the required roles if they are specified
  if (requiredRoles.length > 0 && !requiredRoles.includes(user.role)) {
    return null;
  }

  return <>{children}</>;
}

// Convenience components for specific role requirements
export function ParentOnlyRoute({ children }: { children: React.ReactNode }) {
  return <ProtectedRoute requiredRoles={['parent']}>{children}</ProtectedRoute>;
}
