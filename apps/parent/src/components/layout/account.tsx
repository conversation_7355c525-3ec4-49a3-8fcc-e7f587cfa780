'use client';

import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { Camera, ChevronRight, KeyRound, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';

// Profile form schema
const profileFormSchema = z.object({
  firstName: z
    .string()
    .min(2, {
      message: 'First name must be at least 2 characters.',
    })
    .max(50, {
      message: 'First name must not exceed 50 characters.',
    }),
  lastName: z
    .string()
    .min(2, {
      message: 'Last name must be at least 2 characters.',
    })
    .max(50, {
      message: 'Last name must not exceed 50 characters.',
    }),
  email: z.string().email({
    message: 'Please enter a valid email address.',
  }),
  phone: z
    .string()
    .min(10, {
      message: 'Phone number must be at least 10 digits.',
    })
    .max(15, {
      message: 'Phone number must not exceed 15 digits.',
    }),
  address: z
    .string()
    .min(5, {
      message: 'Address must be at least 5 characters.',
    })
    .max(100, {
      message: 'Address must not exceed 100 characters.',
    }),
  city: z
    .string()
    .min(2, {
      message: 'City must be at least 2 characters.',
    })
    .max(50, {
      message: 'City must not exceed 50 characters.',
    }),
  country: z.string().min(1, {
    message: 'Please select a country.',
  }),
  state: z.string().min(1, {
    message: 'Please select a state.',
  }),
  countryCode: z.string().min(1, {
    message: 'Country code is required.',
  }),
});

// Password reset form schema
const passwordResetSchema = z
  .object({
    currentPassword: z.string().min(1, {
      message: 'Current password is required.',
    }),
    newPassword: z
      .string()
      .min(8, {
        message: 'New password must be at least 8 characters.',
      })
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
        message:
          'Password must contain at least one uppercase letter, one lowercase letter, and one number.',
      }),
    confirmPassword: z.string(),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

type ProfileFormValues = z.infer<typeof profileFormSchema>;
type PasswordResetValues = z.infer<typeof passwordResetSchema>;

export const ProfileForm = () => {
  const [isPasswordSheetOpen, setIsPasswordSheetOpen] = useState(false);

  // Profile form
  const profileForm = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      firstName: 'Usman',
      lastName: 'Ndako',
      email: '<EMAIL>',
      phone: '08065650633',
      address: 'No. 93 Skyfield Apartments',
      city: 'Houston',
      country: 'USA',
      state: 'Texas',
      countryCode: 'us',
    },
  });

  // Password reset form
  const passwordForm = useForm<PasswordResetValues>({
    resolver: zodResolver(passwordResetSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  const onProfileSubmit = (values: ProfileFormValues) => {
    console.log('Profile updated:', values);
    console.log('Profile updated successfully');
  };

  const onPasswordSubmit = (values: PasswordResetValues) => {
    console.log('Password reset:', values);
    console.log('Password reset successfully');
    passwordForm.reset();
    setIsPasswordSheetOpen(false);
  };

  const handleChangePhoto = () => {
    console.log('Photo updated successfully');
  };

  const handleDeleteAccount = () => {
    console.log('Account deleted');
  };

  const handleForgotPassword = () => {
    console.log('Password reset link sent');
  };

  const countries = [
    { value: 'USA', label: 'USA' },
    { value: 'Canada', label: 'Canada' },
    { value: 'UK', label: 'United Kingdom' },
    { value: 'Australia', label: 'Australia' },
  ];

  const states = [
    { value: 'Texas', label: 'Texas' },
    { value: 'California', label: 'California' },
    { value: 'New York', label: 'New York' },
    { value: 'Florida', label: 'Florida' },
  ];

  const countryCodes = [
    { value: 'us', label: '🇺🇸 +1', flag: '🇺🇸' },
    { value: 'uk', label: '🇬🇧 +44', flag: '🇬🇧' },
    { value: 'ca', label: '🇨🇦 +1', flag: '🇨🇦' },
  ];

  return (
    <div className='mx-auto max-w-4xl space-y-8 p-6'>
      {/* Personal Information Section */}
      <Card>
        <CardHeader>
          <CardTitle className='text-xl font-semibold text-gray-800'>
            Personal Information
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-6'>
          {/* Profile Photo */}
          <div className='flex gap-8'>
            <div className='flex flex-col space-y-4'>
              <Avatar className='h-24 w-24'>
                <AvatarImage
                  src='/placeholder.svg?height=96&width=96'
                  alt='Profile'
                />
                <AvatarFallback className='text-lg'>UN</AvatarFallback>
              </Avatar>
              <Button
                variant='outline'
                className='flex w-36 items-center space-x-2 bg-transparent'
                onClick={handleChangePhoto}
              >
                <Camera className='h-4 w-4' />
                <span>Change Photo</span>
              </Button>
            </div>

            {/* Profile Form */}
            <Form {...profileForm}>
              <form
                onSubmit={profileForm.handleSubmit(onProfileSubmit)}
                className='space-y-6'
              >
                <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
                  <FormField
                    control={profileForm.control}
                    name='firstName'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>First Name</FormLabel>
                        <FormControl>
                          <Input {...field} className='bg-gray-50' />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={profileForm.control}
                    name='lastName'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Last Name</FormLabel>
                        <FormControl>
                          <Input {...field} className='bg-gray-50' />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={profileForm.control}
                    name='email'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type='email'
                            className='bg-gray-50'
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={profileForm.control}
                    name='phone'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone Number</FormLabel>
                        <div className='flex'>
                          <FormField
                            control={profileForm.control}
                            name='countryCode'
                            render={({ field: countryCodeField }) => (
                              <Select
                                value={countryCodeField.value}
                                onValueChange={countryCodeField.onChange}
                              >
                                <SelectTrigger className='w-20 bg-gray-50'>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {countryCodes.map((code) => (
                                    <SelectItem
                                      key={code.value}
                                      value={code.value}
                                    >
                                      {code.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            )}
                          />
                          <FormControl>
                            <Input
                              {...field}
                              className='ml-2 flex-1 bg-gray-50'
                            />
                          </FormControl>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* heere we have div */}
                  <div>
                    <FormField
                      control={profileForm.control}
                      name='address'
                      render={({ field }) => (
                        <FormItem className='md:col-span-2'>
                          <FormLabel>Address</FormLabel>
                          <FormControl>
                            <Input {...field} className='bg-gray-50' />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={profileForm.control}
                    name='city'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>City</FormLabel>
                        <FormControl>
                          <Input {...field} className='bg-gray-50' />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={profileForm.control}
                    name='country'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Country</FormLabel>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <FormControl>
                            <SelectTrigger className='w-[300px] bg-gray-50'>
                              <SelectValue placeholder='Select a country' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {countries.map((country) => (
                              <SelectItem
                                key={country.value}
                                value={country.value}
                              >
                                {country.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={profileForm.control}
                    name='state'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>State</FormLabel>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <FormControl>
                            <SelectTrigger className='w-[300px] bg-gray-50'>
                              <SelectValue placeholder='Select a state' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {states.map((state) => (
                              <SelectItem key={state.value} value={state.value}>
                                {state.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className='flex justify-end'>
                  <Button type='submit' className='px-8'>
                    Save Changes
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        </CardContent>
      </Card>

      {/* Account Management Section */}
      <Card>
        <CardHeader>
          <CardTitle className='text-xl font-semibold text-gray-800'>
            Account Management
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          {/* Reset Password */}
          <Sheet
            open={isPasswordSheetOpen}
            onOpenChange={setIsPasswordSheetOpen}
          >
            <SheetTrigger asChild>
              <div className='flex cursor-pointer items-center justify-between rounded-lg border p-4 transition-colors hover:bg-gray-50'>
                <div className='flex items-center space-x-3'>
                  <KeyRound className='h-5 w-5 text-gray-600' />
                  <span className='font-medium'>Reset Password</span>
                </div>
                <ChevronRight className='h-5 w-5 text-gray-400' />
              </div>
            </SheetTrigger>
            <SheetContent>
              <SheetHeader>
                <SheetTitle>Reset Password</SheetTitle>
                <SheetDescription>
                  Enter your current password and choose a new one.
                </SheetDescription>
              </SheetHeader>

              <Form {...passwordForm}>
                <form
                  onSubmit={passwordForm.handleSubmit(onPasswordSubmit)}
                  className='space-y-4 py-4'
                >
                  <FormField
                    control={passwordForm.control}
                    name='currentPassword'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Current Password</FormLabel>
                        <FormControl>
                          <Input {...field} type='password' />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={passwordForm.control}
                    name='newPassword'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>New Password</FormLabel>
                        <FormControl>
                          <Input {...field} type='password' />
                        </FormControl>
                        <FormDescription>
                          Password must be at least 8 characters with uppercase,
                          lowercase, and number.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={passwordForm.control}
                    name='confirmPassword'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Confirm New Password</FormLabel>
                        <FormControl>
                          <Input {...field} type='password' />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Button
                    type='button'
                    variant='link'
                    className='h-auto p-0 text-sm'
                    onClick={handleForgotPassword}
                  >
                    Forgot your current password?
                  </Button>

                  <SheetFooter>
                    <Button type='submit' className='w-full'>
                      Update Password
                    </Button>
                  </SheetFooter>
                </form>
              </Form>
            </SheetContent>
          </Sheet>

          {/* Delete Account */}
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <div className='flex cursor-pointer items-center justify-between rounded-lg border border-red-200 p-4 transition-colors hover:bg-red-50'>
                <div className='flex items-center space-x-3'>
                  <Trash2 className='h-5 w-5 text-red-600' />
                  <span className='font-medium text-red-600'>
                    Delete Account
                  </span>
                </div>
                <ChevronRight className='h-5 w-5 text-red-400' />
              </div>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will permanently delete
                  your account and remove all your data from our servers. All
                  your information, settings, and associated content will be
                  lost forever.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDeleteAccount}
                  className='bg-red-600 hover:bg-red-700'
                >
                  Delete Account
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </CardContent>
      </Card>
    </div>
  );
};
