import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarRail,
} from '@/components/ui/sidebar';

import { FooterActions } from './footer-actions';
import { NavigationItem } from './navigation-item';

// Navigation data structure
const navigationData = {
  main: [
    {
      title: 'Dashboard',
      icon: 'grid',
      url: '/dashboard',
    },
    {
      title: 'Student Management',
      icon: 'users',
      url: '/student-management',
    },
    {
      title: 'Lesson Center',
      icon: 'users',
      url: '/lesson-center',
    },
    {
      title: 'Calendar',
      icon: 'calendar',
      url: '/calendar',
    },
    {
      title: 'Courses(MVP)',
      icon: 'book',
      url: '/courses',
    },
    {
      title: 'Curriculum Levels',
      icon: 'dollar-sign',
      url: '/curriculum',
    },
  ],
};

export function StudioSidebar({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar className='border-r border-gray-200' {...props}>
      <SidebarHeader className='border-b border-gray-100 p-4'>
        Horse Riding
      </SidebarHeader>

      <SidebarContent className='px-2 py-4'>
        <SidebarMenu className='space-y-1 text-yellow-950'>
          {navigationData.main.map((item) => (
            <NavigationItem key={item.title} item={item} />
          ))}
        </SidebarMenu>
      </SidebarContent>

      <SidebarFooter className='border-t border-gray-100 p-2'>
        <FooterActions />
      </SidebarFooter>

      <SidebarRail />
    </Sidebar>
  );
}
