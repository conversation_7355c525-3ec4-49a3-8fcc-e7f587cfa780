import { useNavigate } from '@tanstack/react-router';
import { Headphones, LogOut } from 'lucide-react';

import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';

import { useAuthStoreHook } from '@/hooks/useAuthStore';

export function FooterActions() {
  const { logout, getRedirectUrl } = useAuthStoreHook();
  const navigate = useNavigate();

  const handleContactSupport = () => {
    // TODO: Implement contact support functionality
  };

  const handleLogout = () => {
    logout();
    // Navigate to login page after logout using stored studio_id
    const redirectUrl = getRedirectUrl();
    void navigate({ to: redirectUrl });
  };

  return (
    <SidebarMenu className='space-y-1'>
      <SidebarMenuItem>
        <SidebarMenuButton
          onClick={handleContactSupport}
          className='h-11 cursor-pointer px-3 text-gray-600 hover:bg-gray-100 hover:text-gray-900'
        >
          <div className='flex items-center gap-3'>
            <Headphones className='h-5 w-5' />
            <span className='font-medium'>Contact Support</span>
          </div>
        </SidebarMenuButton>
      </SidebarMenuItem>
      <SidebarMenuItem>
        <SidebarMenuButton
          onClick={handleLogout}
          className='h-11 cursor-pointer px-3 text-red-600 hover:bg-red-50 hover:text-red-700'
        >
          <div className='flex items-center gap-3'>
            <LogOut className='h-5 w-5' />
            <span className='font-medium'>Logout</span>
          </div>
        </SidebarMenuButton>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
