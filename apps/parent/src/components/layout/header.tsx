import { useLocation } from '@tanstack/react-router';

import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';

import { getPageTitle } from '@/utils/getPageTitle';

import { PageHeader } from './page-header';
import { StudioSidebar } from './studio-sidebar';

interface HeaderProps {
  children: React.ReactNode;
}

export function Header({ children }: HeaderProps) {
  const location = useLocation();
  const pageTitle = getPageTitle(location.pathname);
  return (
    <SidebarProvider>
      <StudioSidebar />
      <SidebarInset>
        <PageHeader title={pageTitle} />
        <main className='flex-1 overflow-auto'>{children}</main>
      </SidebarInset>
    </SidebarProvider>
  );
}
