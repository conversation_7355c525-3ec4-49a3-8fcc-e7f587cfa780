import { Book, Calendar, LayoutGrid, TvMinimalPlay, Users } from 'lucide-react';

interface NavigationIconProps {
  name: string;
  className?: string;
}

const iconMap = {
  grid: LayoutGrid,
  calendar: Calendar,
  users: Users,
  folder: LayoutGrid,
  book: TvMinimalPlay,
  'dollar-sign': Book,
};

export function NavigationIcon({
  name,
  className = 'size-5',
}: NavigationIconProps) {
  const Icon = iconMap[name as keyof typeof iconMap];

  return <Icon className={className} />;
}
