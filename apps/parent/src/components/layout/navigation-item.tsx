import { useLocation, useRouter } from '@tanstack/react-router';

import { SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';

import { cn } from '@/lib/utils';

import { NavigationIcon } from './navigation-icon';

interface NavigationItemProps {
  item: {
    title: string;
    icon: string;
    url: string;
  };
}

export function NavigationItem({ item }: NavigationItemProps) {
  const router = useRouter();
  const location = useLocation();
  const pathname = location.pathname;

  const handleNavigation = (url: string) => {
    void router.navigate({ to: url });
  };

  const isCurrentPath = (url: string) => {
    return pathname.includes(url);
  };

  const isActive = isCurrentPath(item.url);

  return (
    <SidebarMenuItem>
      <SidebarMenuButton
        onClick={() => {
          handleNavigation(item.url);
        }}
        isActive={isActive}
        className={cn(
          'hover:bg-black-100 h-11 cursor-pointer px-3 text-gray-600 hover:bg-[#282828] hover:text-[#fdd36b]',
          isActive &&
            'hover:bg-[#282828] hover:text-[#fdd36b] data-[active=true]:bg-[#282828] data-[active=true]:text-[#fdd36b]',
        )}
      >
        <div className='flex items-center gap-3'>
          <NavigationIcon
            name={item.icon}
            className={cn(
              'size-5',
              isActive && item.title === 'Dashboard' && 'text-[#fdd36b]',
            )}
          />
          <span className='font-medium'>{item.title}</span>
        </div>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
}
