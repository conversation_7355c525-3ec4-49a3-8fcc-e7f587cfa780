import { Search, UserPlus } from 'lucide-react';

import { StudentManagementCard } from '@/components/student/student-management-card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

const students = [
  {
    id: 1,
    name: '<PERSON>',
    age: 12,
    level: 'Intermediate',
    avatar: '👧',
    badgeImage: '/learning-levels/green.png',
    skills: ['Advanced Jumping', 'Trotting'],
    cardColor: 'bg-[#FDD36B33]',
  },
  {
    id: 2,
    name: '<PERSON>',
    age: 12,
    level: 'Intermediate',
    avatar: '👦',
    badgeImage: '/learning-levels/blue.png',
    skills: ['Advanced Jumping', 'Trotting'],
    cardColor: 'bg-[#FDD36B33]',
  },
];

export interface Student {
  id: number;
  name: string;
  age: number;
  level: string;
  avatar: string;
  badgeImage: string;
  skills: Array<string>;
  cardColor: string;
}

export const StudentManagement = () => {
  return (
    <div className='min-h-screen bg-gray-50 p-6'>
      <div className='mx-auto'>
        {/* Header with Search and Add Button */}
        <div className='mb-8 flex items-center justify-between'>
          <div className='relative max-w-md flex-1'>
            <Search className='absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400' />
            <Input
              placeholder='Search Student'
              className='h-12 rounded-l-full rounded-r-full border-gray-200 bg-[#F1F3F9] pl-10'
            />
          </div>

          <Button className='h-12 bg-gray-900 px-6 text-white hover:bg-gray-800'>
            <UserPlus className='size-4 text-[#FDD36B]' />
            Add New Student
          </Button>
        </div>

        {/* Students Grid */}
        <div className='grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3'>
          {students.map((student) => (
            <StudentManagementCard key={student.id} student={student} />
          ))}
        </div>
      </div>
    </div>
  );
};
