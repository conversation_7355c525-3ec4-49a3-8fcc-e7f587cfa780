import { initializeAuth } from '@/stores/authStore';
import { createRootRoute, Outlet } from '@tanstack/react-router';
import * as React from 'react';

import { Toaster } from '@/components/ui/sonner';

export const Route = createRootRoute({
  component: RootComponent,
});

function RootComponent() {
  // Initialize auth on app load
  React.useEffect(() => {
    void initializeAuth();
  }, []);

  return (
    <React.Fragment>
      <Outlet />
      <Toaster richColors toastOptions={{}} />
    </React.Fragment>
  );
}
