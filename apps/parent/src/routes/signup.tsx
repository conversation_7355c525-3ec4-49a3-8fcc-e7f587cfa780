import { zodResolver } from '@hookform/resolvers/zod';
import {
  createFileRoute,
  useNavigate,
  useSearch,
} from '@tanstack/react-router';
import { format } from 'date-fns';
import { AlertCircle, CalendarIcon, Eye, EyeOff, Loader2 } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

import { useSignUpAuth } from '@/hooks/useAuth';

import { cn } from '@/lib/utils';

// Extended Error type to match what we expect from the server
interface ServerError extends Error {
  code?: string;
  statusCode?: number;
}

// Validation schema
const signUpSchema = z
  .object({
    email: z
      .string()
      .min(1, 'Email is required')
      .email('Please enter a valid email address'),
    password: z
      .string()
      .min(6, 'Password must be at least 6 characters')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        'Password must contain at least one lowercase letter, one uppercase letter, and one number',
      ),
    confirmPassword: z.string().min(1, 'Please confirm your password'),
    first_name: z
      .string()
      .min(1, 'First name is required')
      .min(2, 'First name must be at least 2 characters'),
    last_name: z
      .string()
      .min(1, 'Last name is required')
      .min(2, 'Last name must be at least 2 characters'),
    phone: z.string().optional(),
    address: z.string().optional(),
    date_of_birth: z.date().optional(),
    emergency_contact_name: z.string().optional(),
    emergency_contact_phone: z.string().optional(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

type SignUpFormData = z.infer<typeof signUpSchema>;

// Search params validation
const searchSchema = z.object({
  studio_id: z.string().min(1, 'Studio ID is required'),
});

export const Route = createFileRoute('/signup')({
  component: SignUpPage,
  validateSearch: searchSchema,
  errorComponent: ({ error: _error, reset: _reset }) => {
    // If validation fails (e.g., missing studio_id), redirect to 404
    if (typeof window !== 'undefined') {
      window.location.href = '/404';
    }
    return null;
  },
  onError: (_error) => {
    // Handle search validation errors by redirecting to 404
    if (typeof window !== 'undefined') {
      window.location.href = '/404';
    }
  },
});

function SignUpPage() {
  const navigate = useNavigate();
  const { studio_id } = useSearch({ from: '/signup' });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [errorCode, setErrorCode] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [submittedEmail, setSubmittedEmail] = useState<string>('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<SignUpFormData>({
    resolver: zodResolver(signUpSchema),
    mode: 'onBlur',
  });

  const selectedDate = watch('date_of_birth');

  const signUpMutation = useSignUpAuth({
    onSuccess: (data) => {
      if (data.data) {
        // Clear any previous errors
        setErrorMessage(null);
        setErrorCode(null);

        // Directly redirect to verification page with studio_id and email
        void navigate({
          to: '/verification',
          search: {
            studio_id,
            email: submittedEmail,
          },
        });
      } else if (data.error) {
        setErrorMessage(data.error.message || 'Sign up failed');
        setErrorCode(data.error.code);
      }
    },
    onError: (error: ServerError) => {
      console.error('Sign up error:', error);
      setErrorMessage(getErrorMessage(error));
      // setErrorCode(error.code ?? null);
    },
  });

  // Helper function to get user-friendly error messages
  const getErrorMessage = (error: ServerError): string => {
    // Map specific error codes to user-friendly messages
    switch (error.code) {
      // case 'EMAIL_ALREADY_EXISTS':
      //   return 'An account with this email already exists. Please use a different email or try signing in.';
      // case 'INVALID_EMAIL':
      //   return 'Please enter a valid email address.';
      // case 'WEAK_PASSWORD':
      //   return 'Password is too weak. Please choose a stronger password.';
      // case 'STUDIO_NOT_FOUND':
      //   return 'Studio not found. Please check the studio ID in the URL.';
      // case 'DB_ERROR':
      //   return 'Unable to connect to the server. Please try again later.';
      // case 'VALIDATION_ERROR':
      //   return 'Invalid input provided. Please check your details.';
      default:
        return (
          error.message || 'An unexpected error occurred. Please try again.'
        );
    }
  };

  const onSubmit = (data: SignUpFormData) => {
    setErrorMessage(null);
    setErrorCode(null);
    setSuccessMessage(null);

    // Store the email for verification redirect
    setSubmittedEmail(data.email);

    // Remove confirmPassword from the data before sending
    const { confirmPassword: _confirmPassword, ...submitData } = data;

    // Create base payload with required fields only
    const payload: {
      email: string;
      password: string;
      first_name: string;
      last_name: string;
      studio_id: string;
      phone?: string;
      address?: string;
      date_of_birth?: string;
      emergency_contact_name?: string;
      emergency_contact_phone?: string;
    } = {
      email: submitData.email,
      password: submitData.password,
      first_name: submitData.first_name,
      last_name: submitData.last_name,
      studio_id,
    };

    // Only include optional fields if they have values
    if (submitData.phone?.trim()) {
      payload.phone = submitData.phone.trim();
    }

    if (submitData.address?.trim()) {
      payload.address = submitData.address.trim();
    }

    if (submitData.date_of_birth) {
      payload.date_of_birth = format(submitData.date_of_birth, 'yyyy-MM-dd');
    }

    if (submitData.emergency_contact_name?.trim()) {
      payload.emergency_contact_name = submitData.emergency_contact_name.trim();
    }

    if (submitData.emergency_contact_phone?.trim()) {
      payload.emergency_contact_phone =
        submitData.emergency_contact_phone.trim();
    }

    signUpMutation.mutate(payload);
  };

  return (
    <div className='flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8'>
      <div className='w-full max-w-2xl space-y-8'>
        <div className='text-center'>
          <h2 className='text-3xl font-bold text-gray-900'>
            Create Your Account
          </h2>
          <p className='mt-2 text-sm text-gray-600'>
            Join our riding academy as a parent
          </p>
        </div>

        <Card className='w-full'>
          <CardHeader>
            <CardTitle>Sign Up</CardTitle>
            <CardDescription>
              Enter your information to create a parent account
            </CardDescription>
          </CardHeader>

          <form onSubmit={handleSubmit(onSubmit)}>
            <CardContent className='space-y-4'>
              {errorMessage && (
                <Alert variant='destructive'>
                  <AlertCircle className='h-4 w-4' />
                  <AlertDescription>
                    <div className='space-y-1'>
                      <div>{errorMessage}</div>
                      {errorCode && (
                        <div className='text-xs opacity-75'>
                          Error code: {errorCode}
                        </div>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {successMessage && (
                <Alert>
                  <AlertCircle className='h-4 w-4' />
                  <AlertDescription>{successMessage}</AlertDescription>
                </Alert>
              )}

              {/* Personal Information */}
              <div className='grid grid-cols-1 gap-4 sm:grid-cols-2'>
                <div className='space-y-2'>
                  <Label htmlFor='first_name'>First Name *</Label>
                  <Input
                    id='first_name'
                    placeholder='Enter your first name'
                    {...register('first_name')}
                    className={errors.first_name ? 'border-red-500' : ''}
                  />
                  {errors.first_name && (
                    <p className='text-sm text-red-500'>
                      {errors.first_name.message}
                    </p>
                  )}
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='last_name'>Last Name *</Label>
                  <Input
                    id='last_name'
                    placeholder='Enter your last name'
                    {...register('last_name')}
                    className={errors.last_name ? 'border-red-500' : ''}
                  />
                  {errors.last_name && (
                    <p className='text-sm text-red-500'>
                      {errors.last_name.message}
                    </p>
                  )}
                </div>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='email'>Email Address *</Label>
                <Input
                  id='email'
                  type='email'
                  placeholder='Enter your email'
                  {...register('email')}
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && (
                  <p className='text-sm text-red-500'>{errors.email.message}</p>
                )}
              </div>

              {/* Password Fields */}
              <div className='grid grid-cols-1 gap-4 sm:grid-cols-2'>
                <div className='space-y-2'>
                  <Label htmlFor='password'>Password *</Label>
                  <div className='relative'>
                    <Input
                      id='password'
                      type={showPassword ? 'text' : 'password'}
                      placeholder='Enter your password'
                      {...register('password')}
                      className={errors.password ? 'border-red-500' : ''}
                    />
                    <Button
                      type='button'
                      variant='ghost'
                      size='sm'
                      className='absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent'
                      onClick={() => {
                        setShowPassword(!showPassword);
                      }}
                    >
                      {showPassword ? (
                        <EyeOff className='h-4 w-4' />
                      ) : (
                        <Eye className='h-4 w-4' />
                      )}
                    </Button>
                  </div>
                  {errors.password && (
                    <p className='text-sm text-red-500'>
                      {errors.password.message}
                    </p>
                  )}
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='confirmPassword'>Confirm Password *</Label>
                  <div className='relative'>
                    <Input
                      id='confirmPassword'
                      type={showConfirmPassword ? 'text' : 'password'}
                      placeholder='Confirm your password'
                      {...register('confirmPassword')}
                      className={errors.confirmPassword ? 'border-red-500' : ''}
                    />
                    <Button
                      type='button'
                      variant='ghost'
                      size='sm'
                      className='absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent'
                      onClick={() => {
                        setShowConfirmPassword(!showConfirmPassword);
                      }}
                    >
                      {showConfirmPassword ? (
                        <EyeOff className='h-4 w-4' />
                      ) : (
                        <Eye className='h-4 w-4' />
                      )}
                    </Button>
                  </div>
                  {errors.confirmPassword && (
                    <p className='text-sm text-red-500'>
                      {errors.confirmPassword.message}
                    </p>
                  )}
                </div>
              </div>

              {/* Optional Fields */}
              <div className='space-y-4'>
                <h3 className='text-lg font-medium text-gray-900'>
                  Additional Information (Optional)
                </h3>

                <div className='grid grid-cols-1 gap-4 sm:grid-cols-2'>
                  <div className='space-y-2'>
                    <Label htmlFor='phone'>Phone Number</Label>
                    <Input
                      id='phone'
                      placeholder='Enter your phone number'
                      {...register('phone')}
                    />
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='date_of_birth'>Date of Birth</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant='outline'
                          className={cn(
                            'w-full justify-start text-left font-normal',
                            !selectedDate && 'text-muted-foreground',
                          )}
                        >
                          <CalendarIcon className='mr-2 h-4 w-4' />
                          {selectedDate ? (
                            format(selectedDate, 'PPP')
                          ) : (
                            <span>Pick a date</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className='w-auto p-0'>
                        <Calendar
                          mode='single'
                          selected={selectedDate}
                          onSelect={(date) => {
                            setValue('date_of_birth', date);
                          }}
                          autoFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='address'>Address</Label>
                  <Input
                    id='address'
                    placeholder='Enter your address'
                    {...register('address')}
                  />
                </div>

                <div className='grid grid-cols-1 gap-4 sm:grid-cols-2'>
                  <div className='space-y-2'>
                    <Label htmlFor='emergency_contact_name'>
                      Emergency Contact Name
                    </Label>
                    <Input
                      id='emergency_contact_name'
                      placeholder='Emergency contact name'
                      {...register('emergency_contact_name')}
                    />
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='emergency_contact_phone'>
                      Emergency Contact Phone
                    </Label>
                    <Input
                      id='emergency_contact_phone'
                      placeholder='Emergency contact phone'
                      {...register('emergency_contact_phone')}
                    />
                  </div>
                </div>
              </div>
            </CardContent>

            <CardFooter className='mt-4 flex flex-col space-y-4'>
              <Button
                type='submit'
                className='w-full'
                disabled={signUpMutation.isPending}
              >
                {signUpMutation.isPending ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    Creating Account...
                  </>
                ) : (
                  'Create Account'
                )}
              </Button>

              <div className='text-center text-sm text-gray-600'>
                Already have an account?{' '}
                <button
                  type='button'
                  onClick={() =>
                    void navigate({ to: '/login', search: { studio_id } })
                  }
                  className='font-medium text-blue-600 hover:text-blue-500'
                >
                  Sign in here
                </button>
              </div>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  );
}
