import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { Shield } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

import { useAuthStoreHook } from '@/hooks/useAuthStore';

export const Route = createFileRoute('/unauthorized')({
  component: UnauthorizedPage,
});

function UnauthorizedPage() {
  const navigate = useNavigate();
  const { user, logout, getRedirectUrl } = useAuthStoreHook();

  const handleLogout = () => {
    logout();
    const redirectUrl = getRedirectUrl();
    void navigate({ to: redirectUrl });
  };

  return (
    <div className='flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8'>
      <div className='w-full max-w-md space-y-8'>
        <div className='text-center'>
          <Shield className='mx-auto h-12 w-12 text-amber-500' />
          <h2 className='mt-6 text-3xl font-bold text-gray-900'>
            Access Denied
          </h2>
          <p className='mt-2 text-sm text-gray-600'>
            You don&apos;t have permission to access this page.
          </p>
        </div>

        <Card className='w-full'>
          <CardHeader>
            <CardTitle>Unauthorized Access</CardTitle>
            <CardDescription>This could happen if:</CardDescription>
          </CardHeader>
          <CardContent className='space-y-2'>
            <ul className='list-disc space-y-1 pl-5 text-sm text-gray-600'>
              <li>You&apos;re not signed in</li>
              <li>Your account doesn&apos;t have the required permissions</li>
              <li>Your session has expired</li>
              <li>You&apos;re trying to access an admin-only area</li>
            </ul>
          </CardContent>
          <CardFooter className='flex flex-col space-y-2'>
            {user && (
              <Button
                onClick={handleLogout}
                variant='default'
                className='w-full'
              >
                Sign Out & Go to Login
              </Button>
            )}
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
