import { zodResolver } from '@hookform/resolvers/zod';
import {
  createFileRoute,
  useNavigate,
  useSearch,
} from '@tanstack/react-router';
import { AlertCircle, Eye, EyeOff, Loader2 } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { useSignInAuth } from '@/hooks/useAuth';
import { useAuthStoreHook } from '@/hooks/useAuthStore';

// Extended Error type to match what we expect from the server
interface ServerError extends Error {
  code?: string;
  statusCode?: number;
}

// Validation schema
const signInSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address'),
  password: z
    .string()
    .min(1, 'Password is required')
    .min(6, 'Password must be at least 6 characters'),
});

type SignInFormData = z.infer<typeof signInSchema>;

// Search params validation
const searchSchema = z.object({
  studio_id: z.string().min(1, 'Studio ID is required'),
});

export const Route = createFileRoute('/login')({
  component: SignInPage,
  validateSearch: searchSchema,
  errorComponent: ({ error: _error, reset: _reset }) => {
    // If validation fails (e.g., missing studio_id), redirect to 404
    if (typeof window !== 'undefined') {
      window.location.href = '/404';
    }
    return null;
  },
  onError: (_error) => {
    // Handle search validation errors by redirecting to 404
    if (typeof window !== 'undefined') {
      window.location.href = '/404';
    }
  },
});

function SignInPage() {
  const navigate = useNavigate();
  const { studio_id } = useSearch({ from: '/login' });
  const [showPassword, setShowPassword] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [errorCode, setErrorCode] = useState<string | null>(null);
  const { login } = useAuthStoreHook();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<SignInFormData>({
    resolver: zodResolver(signInSchema),
    mode: 'onBlur',
  });

  const signInMutation = useSignInAuth({
    onSuccess: (data) => {
      if (data.data) {
        // Update auth store with studio_id from URL
        login(
          data.data.accessToken,
          data.data.refreshToken,
          data.data.user,
          studio_id,
        );

        // Clear any previous errors
        setErrorMessage(null);
        setErrorCode(null);

        // Check if user needs to reset their password
        if (!data.data.user.isPasswordSet) {
          // Navigate to reset password page
          void navigate({ to: '/reset-password', search: { studio_id } });
        } else {
          // Navigate to dashboard
          void navigate({ to: '/dashboard' });
        }
      } else if (data.error) {
        setErrorMessage(data.error.message || 'Sign in failed');
        setErrorCode(data.error.code);
      }
    },
    onError: (error: ServerError) => {
      console.error('Sign in error:', error);
      setErrorMessage(getErrorMessage(error));
      setErrorCode(error.code ?? null);
    },
  });

  // Helper function to get user-friendly error messages
  const getErrorMessage = (error: ServerError): string => {
    // Map specific error codes to user-friendly messages
    switch (error.code) {
      case 'INVALID_CREDENTIALS':
        return 'Invalid email or password. Please check your credentials and try again.';
      case 'USER_NOT_FOUND':
        return 'No account found with this email address.';
      case 'INVALID_PASSWORD':
        return 'Incorrect password. Please try again.';
      case 'ACCOUNT_DISABLED':
        return 'Your account has been disabled. Please contact support.';
      case 'STUDIO_NOT_FOUND':
        return 'Studio not found. Please check the studio ID in the URL.';
      case 'DB_ERROR':
        return 'Unable to connect to the server. Please try again later.';
      case 'VALIDATION_ERROR':
        return 'Invalid input provided. Please check your details.';
      default:
        return (
          error.message || 'An unexpected error occurred. Please try again.'
        );
    }
  };

  const onSubmit = (data: SignInFormData) => {
    setErrorMessage(null);
    setErrorCode(null);
    // Pass studio_id to the mutation
    signInMutation.mutate({
      ...data,
      studio_id,
    });
  };

  return (
    <div className='flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8'>
      <div className='w-full max-w-md space-y-8'>
        <div className='text-center'>
          <h2 className='text-3xl font-bold text-gray-900'>Welcome Back</h2>
          <p className='mt-2 text-sm text-gray-600'>
            Sign in to your parent account
          </p>
        </div>

        <Card className='w-full'>
          <CardHeader>
            <CardTitle>Sign In</CardTitle>
            <CardDescription>
              Enter your credentials to access your account
            </CardDescription>
          </CardHeader>

          <form onSubmit={handleSubmit(onSubmit)}>
            <CardContent className='space-y-4'>
              {errorMessage && (
                <Alert variant='destructive'>
                  <AlertCircle className='h-4 w-4' />
                  <AlertDescription>
                    <div className='space-y-1'>
                      <div>{errorMessage}</div>
                      {errorCode && (
                        <div className='text-xs opacity-75'>
                          Error code: {errorCode}
                        </div>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              <div className='space-y-2'>
                <Label htmlFor='email'>Email Address</Label>
                <Input
                  id='email'
                  type='email'
                  placeholder='Enter your email'
                  {...register('email')}
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && (
                  <p className='text-sm text-red-500'>{errors.email.message}</p>
                )}
              </div>

              <div className='space-y-2'>
                <Label htmlFor='password'>Password</Label>
                <div className='relative'>
                  <Input
                    id='password'
                    type={showPassword ? 'text' : 'password'}
                    placeholder='Enter your password'
                    {...register('password')}
                    className={errors.password ? 'border-red-500' : ''}
                  />
                  <Button
                    type='button'
                    variant='ghost'
                    size='sm'
                    className='absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent'
                    onClick={() => {
                      setShowPassword(!showPassword);
                    }}
                  >
                    {showPassword ? (
                      <EyeOff className='h-4 w-4' />
                    ) : (
                      <Eye className='h-4 w-4' />
                    )}
                  </Button>
                </div>
                {errors.password && (
                  <p className='text-sm text-red-500'>
                    {errors.password.message}
                  </p>
                )}
              </div>
            </CardContent>

            <CardFooter className='mt-4 flex flex-col space-y-4'>
              <Button
                type='submit'
                className='w-full'
                disabled={signInMutation.isPending}
              >
                {signInMutation.isPending ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    Signing In...
                  </>
                ) : (
                  'Sign In'
                )}
              </Button>

              <div className='text-center text-sm text-gray-600'>
                Don&#39;t have an account?{' '}
                <button
                  type='button'
                  onClick={() =>
                    void navigate({ to: '/signup', search: { studio_id } })
                  }
                  className='font-medium text-blue-600 hover:text-blue-500'
                >
                  Sign up here
                </button>
              </div>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  );
}
