import { zodResolver } from '@hookform/resolvers/zod';
import {
  createFileRoute,
  useNavigate,
  useSearch,
} from '@tanstack/react-router';
import { Eye, EyeOff, Loader2 } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

import { useResetOneTimePassword, useSignInAuth } from '@/hooks/useAuth';
import { useAuthStoreHook } from '@/hooks/useAuthStore';

// Extended Error type to match what we expect from the server
interface ServerError extends Error {
  code?: string;
  statusCode?: number;
}

// Validation schema
const resetPasswordSchema = z
  .object({
    current_password: z.string().min(1, 'Current password is required'),
    new_password: z
      .string()
      .min(6, 'Password must be at least 6 characters')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        'Password must contain at least one lowercase letter, one uppercase letter, and one number',
      ),
    confirm_password: z.string().min(1, 'Please confirm your password'),
  })
  .refine((data) => data.new_password === data.confirm_password, {
    message: "Passwords don't match",
    path: ['confirm_password'],
  });

type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;

// Search params validation
const searchSchema = z.object({
  studio_id: z.string().min(1, 'Studio ID is required'),
});

export const Route = createFileRoute('/reset-password')({
  component: ResetPasswordPage,
  validateSearch: searchSchema,
  errorComponent: ({ error: _error, reset: _reset }) => {
    // If validation fails (e.g., missing studio_id), redirect to 404
    if (typeof window !== 'undefined') {
      window.location.href = '/404';
    }
    return null;
  },
  onError: (_error) => {
    // Handle search validation errors by redirecting to 404
    if (typeof window !== 'undefined') {
      window.location.href = '/404';
    }
  },
});

function ResetPasswordPage() {
  const navigate = useNavigate();
  const { studio_id } = useSearch({ from: '/reset-password' });
  const { user, login } = useAuthStoreHook();
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
    mode: 'onBlur',
  });

  // Initialize mutations without onSuccess/onError callbacks
  const resetPasswordMutation = useResetOneTimePassword();
  const signInMutation = useSignInAuth();

  // Helper function to get user-friendly error messages
  const getErrorMessage = (error: ServerError): string => {
    // Map specific error codes to user-friendly messages
    switch (error.code) {
      case 'INVALID_CURRENT_PASSWORD':
        return 'Current password is incorrect. Please check and try again.';
      case 'USER_NOT_FOUND':
        return 'User not found. Please contact support.';
      case 'DB_ERROR':
        return 'Unable to connect to the server. Please try again later.';
      case 'VALIDATION_ERROR':
        return 'Invalid input provided. Please check your details.';
      case 'STUDIO_NOT_FOUND':
        return 'Studio not found. Please check the studio ID in the URL.';
      default:
        return (
          error.message || 'An unexpected error occurred. Please try again.'
        );
    }
  };

  const onSubmit = async (data: ResetPasswordFormData) => {
    if (!user?.email) {
      toast.error('User email not found. Please log in again.');
      return;
    }

    setIsLoading(true);

    try {
      // Step 1: Reset the password
      toast.loading('Resetting your password...', { id: 'reset-password' });

      const { confirm_password: _confirm_password, ...submitData } = data;

      await resetPasswordMutation.mutateAsync({
        email: user.email,
        current_password: submitData.current_password,
        new_password: submitData.new_password,
        studio_id,
      });

      // Step 2: Auto-login with new credentials
      toast.loading('Signing you in...', { id: 'reset-password' });

      const signInResult = await signInMutation.mutateAsync({
        email: user.email,
        password: data.new_password,
        studio_id,
      });

      if (signInResult.data) {
        // Update auth store with new user data (including isPasswordSet: true)
        login(
          signInResult.data.accessToken,
          signInResult.data.refreshToken,
          signInResult.data.user,
          studio_id,
        );

        // Success toast and redirect
        toast.success('Password reset successful! Welcome back!', {
          id: 'reset-password',
          duration: 2000,
        });

        // Navigate to dashboard after a brief delay
        setTimeout(() => {
          void navigate({ to: '/dashboard' });
        }, 1000);
      } else {
        throw new Error('Login response missing data');
      }
    } catch (error) {
      console.error('Password reset or login error:', error);
      const serverError = error as ServerError;

      toast.error(getErrorMessage(serverError), {
        id: 'reset-password',
        duration: 5000,
      });

      // If reset succeeded but login failed, redirect to login page
      if (resetPasswordMutation.isSuccess && !signInMutation.isSuccess) {
        toast.info(
          'Password reset successful! Please sign in with your new password.',
          {
            duration: 4000,
          },
        );

        setTimeout(() => {
          void navigate({ to: '/login', search: { studio_id } });
        }, 2000);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8'>
      <div className='w-full max-w-md space-y-8'>
        <div className='text-center'>
          <h2 className='text-3xl font-bold text-gray-900'>
            Reset Your Password
          </h2>
          <p className='mt-2 text-sm text-gray-600'>
            Please set your new password to continue
          </p>
        </div>

        <Card className='w-full'>
          <CardHeader>
            <CardTitle>Set New Password</CardTitle>
            <CardDescription>
              Enter your current temporary password and choose a new permanent
              password. You will be automatically signed in after the reset.
            </CardDescription>
          </CardHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <CardContent className='space-y-4'>
                <FormField
                  control={form.control}
                  name='current_password'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Current Password</FormLabel>
                      <FormControl>
                        <div className='relative'>
                          <Input
                            {...field}
                            type={showCurrentPassword ? 'text' : 'password'}
                            placeholder='Enter your current password'
                          />
                          <Button
                            type='button'
                            variant='ghost'
                            size='sm'
                            className='absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent'
                            onClick={() => {
                              setShowCurrentPassword(!showCurrentPassword);
                            }}
                          >
                            {showCurrentPassword ? (
                              <EyeOff className='h-4 w-4' />
                            ) : (
                              <Eye className='h-4 w-4' />
                            )}
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='new_password'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>New Password</FormLabel>
                      <FormControl>
                        <div className='relative'>
                          <Input
                            {...field}
                            type={showNewPassword ? 'text' : 'password'}
                            placeholder='Enter your new password'
                          />
                          <Button
                            type='button'
                            variant='ghost'
                            size='sm'
                            className='absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent'
                            onClick={() => {
                              setShowNewPassword(!showNewPassword);
                            }}
                          >
                            {showNewPassword ? (
                              <EyeOff className='h-4 w-4' />
                            ) : (
                              <Eye className='h-4 w-4' />
                            )}
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='confirm_password'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confirm New Password</FormLabel>
                      <FormControl>
                        <div className='relative'>
                          <Input
                            {...field}
                            type={showConfirmPassword ? 'text' : 'password'}
                            placeholder='Confirm your new password'
                          />
                          <Button
                            type='button'
                            variant='ghost'
                            size='sm'
                            className='absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent'
                            onClick={() => {
                              setShowConfirmPassword(!showConfirmPassword);
                            }}
                          >
                            {showConfirmPassword ? (
                              <EyeOff className='h-4 w-4' />
                            ) : (
                              <Eye className='h-4 w-4' />
                            )}
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>

              <CardContent className='pt-6'>
                <Button type='submit' className='w-full' disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                      Processing...
                    </>
                  ) : (
                    'Reset Password'
                  )}
                </Button>
              </CardContent>
            </form>
          </Form>
        </Card>
      </div>
    </div>
  );
}
