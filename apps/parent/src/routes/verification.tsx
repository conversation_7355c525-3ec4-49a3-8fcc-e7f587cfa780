import {
  createFileRoute,
  useNavigate,
  useSearch,
} from '@tanstack/react-router';
import { CheckCircle, Mail } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

export const Route = createFileRoute('/verification')({
  component: VerificationPage,
  validateSearch: (search: Record<string, unknown>) => ({
    studio_id: search.studio_id as string,
    email: search.email ? (search.email as string) : undefined,
  }),
});

function VerificationPage() {
  const navigate = useNavigate();
  const searchParams = useSearch({ from: '/verification' });
  const { studio_id } = searchParams;
  const email = searchParams.email as string | undefined;

  const handleGoToLogin = () => {
    void navigate({
      to: '/login',
      search: { studio_id },
    });
  };

  return (
    <div className='flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8'>
      <div className='w-full max-w-md space-y-8'>
        <div className='text-center'>
          <CheckCircle className='mx-auto h-16 w-16 text-green-500' />
          <h2 className='mt-6 text-3xl font-bold text-gray-900'>
            Check Your Email
          </h2>
          <p className='mt-2 text-sm text-gray-600'>
            We&apos;ve sent a verification link to your email address
          </p>
        </div>

        <Card className='w-full'>
          <CardHeader className='text-center'>
            <div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100'>
              <Mail className='h-6 w-6 text-blue-600' />
            </div>
            <CardTitle>Verification Email Sent</CardTitle>
            <CardDescription>
              {email
                ? 'A verification email has been sent to:'
                : 'A verification email has been sent to your email address'}
            </CardDescription>
          </CardHeader>

          <CardContent className='text-center'>
            {email && (
              <div className='rounded-lg bg-gray-50 p-4'>
                <p className='font-medium text-gray-900'>{email}</p>
              </div>
            )}

            <div
              className={`space-y-3 text-sm text-gray-600 ${email ? 'mt-6' : ''}`}
            >
              <p>
                Please check your email and click the verification link to
                activate your account.
              </p>
              <div className='space-y-1'>
                <p className='font-medium'>What to do next:</p>
                <ul className='list-inside list-disc space-y-1 text-left'>
                  <li>Check your inbox for a verification email</li>
                  <li>Click the verification link in the email</li>
                  <li>Return here to sign in to your account</li>
                </ul>
              </div>
            </div>
          </CardContent>

          <CardFooter className='flex flex-col space-y-3'>
            <Button onClick={handleGoToLogin} className='w-full'>
              Go to Sign In
            </Button>

            <div className='text-center text-xs text-gray-500'>
              <p>
                Didn&apos;t receive the email? Check your spam folder or contact
                support.
              </p>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
