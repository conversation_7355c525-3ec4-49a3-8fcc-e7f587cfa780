import {
  useMutation,
  type UseMutationOptions,
  useQueryClient,
} from '@tanstack/react-query';

import transformDates from '@/utils/transformDates';

import { hcWithType } from '../../../server/src/client';
import type { AuthSignIn, AuthSignUp } from '../../../shared/src/types';

// Extended Error type to include server error details
interface ServerError extends Error {
  code?: string;
  statusCode?: number;
}

const SERVER_URL = import.meta.env.VITE_SERVER_URL || 'http://localhost:3000';
const client = hcWithType(SERVER_URL);

// Query Keys
export const authQueryKeys = {
  all: ['auth'] as const,
  lists: () => [...authQueryKeys.all, 'list'] as const,
  list: (filters: Record<string, unknown>) =>
    [...authQueryKeys.lists(), { filters }] as const,
  details: () => [...authQueryKeys.all, 'detail'] as const,
  detail: (id: string | number) => [...authQueryKeys.details(), id] as const,
} as const;

// SignIn Auth Hook
export const useSignInAuth = (
  options?: UseMutationOptions<
    AuthSignIn.typeResult,
    ServerError,
    AuthSignIn.typePayload & { studio_id?: string }
  >,
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      data: AuthSignIn.typePayload & { studio_id?: string },
    ): Promise<AuthSignIn.typeResult> => {
      const { studio_id, ...payloadData } = data;

      // Get studio_id from URL search params if not provided directly
      const urlParams = new URLSearchParams(window.location.search);
      const studioIdParam = studio_id ?? urlParams.get('studio_id');

      if (!studioIdParam) {
        throw new Error('Studio ID is required');
      }

      const res = await client.api.v1.auth.signin.$post({
        json: payloadData,
        query: { studio_id: studioIdParam },
      });

      const rawData = await res.json();

      if (!res.ok) {
        // If there's an error in the response, check if it has the expected structure
        const errorData = rawData as AuthSignIn.typeResult;
        if (errorData.error) {
          const error: ServerError = new Error(errorData.error.message);
          // Attach additional error details
          error.code = errorData.error.code;
          error.statusCode = errorData.error.statusCode;
          throw error;
        }
        // Fallback error if the structure is unexpected
        throw new Error(`Sign in failed with status ${res.status}`);
      }

      // Transform dates from strings to Date objects
      if (rawData.data) {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        rawData.data = transformDates(rawData.data);
      }
      return rawData as AuthSignIn.typeResult;
    },
    onSuccess: (_data, _variables) => {
      // Invalidate and refetch relevant queries
      void queryClient.invalidateQueries({ queryKey: authQueryKeys.all });

      // Refresh relevant data
      void queryClient.invalidateQueries({ queryKey: authQueryKeys.lists() });
    },
    ...options,
  });
};

// SignUp Auth Hook
export const useSignUpAuth = (
  options?: UseMutationOptions<
    AuthSignUp.typeResult,
    ServerError,
    AuthSignUp.typePayload & { studio_id?: string }
  >,
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      data: AuthSignUp.typePayload & { studio_id?: string },
    ): Promise<AuthSignUp.typeResult> => {
      const { studio_id, ...payloadData } = data;

      // Get studio_id from URL search params if not provided directly
      const urlParams = new URLSearchParams(window.location.search);
      const studioIdParam = studio_id ?? urlParams.get('studio_id');

      if (!studioIdParam) {
        throw new Error('Studio ID is required');
      }

      const res = await client.api.v1.auth.signup.$post({
        json: payloadData,
        query: { studio_id: studioIdParam },
      });

      const rawData = await res.json();

      if (!res.ok) {
        // If there's an error in the response, check if it has the expected structure
        const errorData = rawData as AuthSignUp.typeResult;
        if (errorData.error) {
          const error: ServerError = new Error(errorData.error.message);
          // Attach additional error details
          error.code = errorData.error.code;
          error.statusCode = errorData.error.statusCode;
          throw error;
        }
        // Fallback error if the structure is unexpected
        throw new Error(`Sign up failed with status ${res.status}`);
      }

      // Transform dates from strings to Date objects
      if (rawData.data) {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        rawData.data = transformDates(rawData.data);
      }
      return rawData as AuthSignUp.typeResult;
    },
    onSuccess: (_data, _variables) => {
      // Invalidate and refetch relevant queries
      void queryClient.invalidateQueries({ queryKey: authQueryKeys.all });

      // Refresh relevant data
      void queryClient.invalidateQueries({ queryKey: authQueryKeys.lists() });
    },
    ...options,
  });
};

// Reset One Time Password Hook
export const useResetOneTimePassword = (
  options?: UseMutationOptions<
    unknown,
    ServerError,
    {
      email: string;
      current_password: string;
      new_password: string;
      studio_id?: string;
    }
  >,
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: {
      email: string;
      current_password: string;
      new_password: string;
      studio_id?: string;
    }): Promise<unknown> => {
      const { studio_id, ...payloadData } = data;

      // Get studio_id from URL search params if not provided directly
      const urlParams = new URLSearchParams(window.location.search);
      const studioIdParam = studio_id ?? urlParams.get('studio_id');

      if (!studioIdParam) {
        throw new Error('Studio ID is required');
      }

      // For now, use direct fetch until the hono client is updated
      const response = await fetch(
        `${SERVER_URL}/api/v1/auth/reset-password?studio_id=${studioIdParam}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payloadData),
        },
      );

      const rawData = (await response.json()) as {
        data?: { user?: unknown };
        error?: { message: string; code: string; statusCode: number };
      };

      if (!response.ok) {
        // If there's an error in the response, check if it has the expected structure
        if (rawData.error) {
          const error: ServerError = new Error(rawData.error.message);
          // Attach additional error details
          error.code = rawData.error.code;
          error.statusCode = rawData.error.statusCode;
          throw error;
        }
        // Fallback error if the structure is unexpected
        throw new Error(`Password reset failed with status ${response.status}`);
      }

      // Transform dates from strings to Date objects
      if (rawData.data?.user) {
        rawData.data.user = transformDates(rawData.data.user);
      }
      return rawData;
    },
    onSuccess: (_data, _variables) => {
      // Invalidate and refetch relevant queries
      void queryClient.invalidateQueries({ queryKey: authQueryKeys.all });

      // Refresh relevant data
      void queryClient.invalidateQueries({ queryKey: authQueryKeys.lists() });
    },
    ...options,
  });
};
