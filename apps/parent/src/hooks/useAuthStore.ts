import { useAuthStore } from '@/stores/authStore';

export const useAuthStoreHook = () => {
  const {
    user,
    accessToken,
    refreshToken,
    isAuthenticated,
    isLoading,
    isVerifying,
    studioId,
    login,
    logout,
    updateUser,
    verifyToken,
    setStudioId,
    getRedirectUrl,
  } = useAuthStore();

  return {
    user,
    accessToken,
    refreshToken,
    isAuthenticated,
    isLoading: isLoading || isVerifying,
    studioId,
    login,
    logout,
    updateUser,
    verifyToken,
    setStudioId,
    getRedirectUrl,
  };
};
