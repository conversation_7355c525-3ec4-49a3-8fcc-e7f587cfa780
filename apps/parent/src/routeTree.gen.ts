/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root';
import { Route as VerificationRouteImport } from './routes/verification';
import { Route as UnauthorizedRouteImport } from './routes/unauthorized';
import { Route as SignupRouteImport } from './routes/signup';
import { Route as ResetPasswordRouteImport } from './routes/reset-password';
import { Route as LoginRouteImport } from './routes/login';
import { Route as R404RouteImport } from './routes/404';
import { Route as AuthRouteRouteImport } from './routes/_auth/route';
import { Route as IndexRouteImport } from './routes/index';
import { Route as AuthStudentManagementRouteRouteImport } from './routes/_auth/student-management/route';
import { Route as AuthStudentDetailsRouteRouteImport } from './routes/_auth/student-details/route';
import { Route as AuthLessonCenterRouteRouteImport } from './routes/_auth/lesson-center/route';
import { Route as AuthDashboardRouteRouteImport } from './routes/_auth/dashboard/route';
import { Route as AuthCurriculumRouteRouteImport } from './routes/_auth/curriculum/route';
import { Route as AuthCoursesRouteRouteImport } from './routes/_auth/courses/route';
import { Route as AuthCalendarRouteRouteImport } from './routes/_auth/calendar/route';
import { Route as AuthBookingLessonRouteRouteImport } from './routes/_auth/booking-lesson/route';
import { Route as AuthAccountRouteRouteImport } from './routes/_auth/account/route';
import { Route as AuthStudentManagementIdRouteRouteImport } from './routes/_auth/student-management_/$id/route';

const VerificationRoute = VerificationRouteImport.update({
  id: '/verification',
  path: '/verification',
  getParentRoute: () => rootRouteImport,
} as any);
const UnauthorizedRoute = UnauthorizedRouteImport.update({
  id: '/unauthorized',
  path: '/unauthorized',
  getParentRoute: () => rootRouteImport,
} as any);
const SignupRoute = SignupRouteImport.update({
  id: '/signup',
  path: '/signup',
  getParentRoute: () => rootRouteImport,
} as any);
const ResetPasswordRoute = ResetPasswordRouteImport.update({
  id: '/reset-password',
  path: '/reset-password',
  getParentRoute: () => rootRouteImport,
} as any);
const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any);
const R404Route = R404RouteImport.update({
  id: '/404',
  path: '/404',
  getParentRoute: () => rootRouteImport,
} as any);
const AuthRouteRoute = AuthRouteRouteImport.update({
  id: '/_auth',
  getParentRoute: () => rootRouteImport,
} as any);
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any);
const AuthStudentManagementRouteRoute =
  AuthStudentManagementRouteRouteImport.update({
    id: '/student-management',
    path: '/student-management',
    getParentRoute: () => AuthRouteRoute,
  } as any);
const AuthStudentDetailsRouteRoute = AuthStudentDetailsRouteRouteImport.update({
  id: '/student-details',
  path: '/student-details',
  getParentRoute: () => AuthRouteRoute,
} as any);
const AuthLessonCenterRouteRoute = AuthLessonCenterRouteRouteImport.update({
  id: '/lesson-center',
  path: '/lesson-center',
  getParentRoute: () => AuthRouteRoute,
} as any);
const AuthDashboardRouteRoute = AuthDashboardRouteRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => AuthRouteRoute,
} as any);
const AuthCurriculumRouteRoute = AuthCurriculumRouteRouteImport.update({
  id: '/curriculum',
  path: '/curriculum',
  getParentRoute: () => AuthRouteRoute,
} as any);
const AuthCoursesRouteRoute = AuthCoursesRouteRouteImport.update({
  id: '/courses',
  path: '/courses',
  getParentRoute: () => AuthRouteRoute,
} as any);
const AuthCalendarRouteRoute = AuthCalendarRouteRouteImport.update({
  id: '/calendar',
  path: '/calendar',
  getParentRoute: () => AuthRouteRoute,
} as any);
const AuthBookingLessonRouteRoute = AuthBookingLessonRouteRouteImport.update({
  id: '/booking-lesson',
  path: '/booking-lesson',
  getParentRoute: () => AuthRouteRoute,
} as any);
const AuthAccountRouteRoute = AuthAccountRouteRouteImport.update({
  id: '/account',
  path: '/account',
  getParentRoute: () => AuthRouteRoute,
} as any);
const AuthStudentManagementIdRouteRoute =
  AuthStudentManagementIdRouteRouteImport.update({
    id: '/student-management_/$id',
    path: '/student-management/$id',
    getParentRoute: () => AuthRouteRoute,
  } as any);

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute;
  '/404': typeof R404Route;
  '/login': typeof LoginRoute;
  '/reset-password': typeof ResetPasswordRoute;
  '/signup': typeof SignupRoute;
  '/unauthorized': typeof UnauthorizedRoute;
  '/verification': typeof VerificationRoute;
  '/account': typeof AuthAccountRouteRoute;
  '/booking-lesson': typeof AuthBookingLessonRouteRoute;
  '/calendar': typeof AuthCalendarRouteRoute;
  '/courses': typeof AuthCoursesRouteRoute;
  '/curriculum': typeof AuthCurriculumRouteRoute;
  '/dashboard': typeof AuthDashboardRouteRoute;
  '/lesson-center': typeof AuthLessonCenterRouteRoute;
  '/student-details': typeof AuthStudentDetailsRouteRoute;
  '/student-management': typeof AuthStudentManagementRouteRoute;
  '/student-management/$id': typeof AuthStudentManagementIdRouteRoute;
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute;
  '/404': typeof R404Route;
  '/login': typeof LoginRoute;
  '/reset-password': typeof ResetPasswordRoute;
  '/signup': typeof SignupRoute;
  '/unauthorized': typeof UnauthorizedRoute;
  '/verification': typeof VerificationRoute;
  '/account': typeof AuthAccountRouteRoute;
  '/booking-lesson': typeof AuthBookingLessonRouteRoute;
  '/calendar': typeof AuthCalendarRouteRoute;
  '/courses': typeof AuthCoursesRouteRoute;
  '/curriculum': typeof AuthCurriculumRouteRoute;
  '/dashboard': typeof AuthDashboardRouteRoute;
  '/lesson-center': typeof AuthLessonCenterRouteRoute;
  '/student-details': typeof AuthStudentDetailsRouteRoute;
  '/student-management': typeof AuthStudentManagementRouteRoute;
  '/student-management/$id': typeof AuthStudentManagementIdRouteRoute;
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport;
  '/': typeof IndexRoute;
  '/_auth': typeof AuthRouteRouteWithChildren;
  '/404': typeof R404Route;
  '/login': typeof LoginRoute;
  '/reset-password': typeof ResetPasswordRoute;
  '/signup': typeof SignupRoute;
  '/unauthorized': typeof UnauthorizedRoute;
  '/verification': typeof VerificationRoute;
  '/_auth/account': typeof AuthAccountRouteRoute;
  '/_auth/booking-lesson': typeof AuthBookingLessonRouteRoute;
  '/_auth/calendar': typeof AuthCalendarRouteRoute;
  '/_auth/courses': typeof AuthCoursesRouteRoute;
  '/_auth/curriculum': typeof AuthCurriculumRouteRoute;
  '/_auth/dashboard': typeof AuthDashboardRouteRoute;
  '/_auth/lesson-center': typeof AuthLessonCenterRouteRoute;
  '/_auth/student-details': typeof AuthStudentDetailsRouteRoute;
  '/_auth/student-management': typeof AuthStudentManagementRouteRoute;
  '/_auth/student-management_/$id': typeof AuthStudentManagementIdRouteRoute;
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath;
  fullPaths:
    | '/'
    | '/404'
    | '/login'
    | '/reset-password'
    | '/signup'
    | '/unauthorized'
    | '/verification'
    | '/account'
    | '/booking-lesson'
    | '/calendar'
    | '/courses'
    | '/curriculum'
    | '/dashboard'
    | '/lesson-center'
    | '/student-details'
    | '/student-management'
    | '/student-management/$id';
  fileRoutesByTo: FileRoutesByTo;
  to:
    | '/'
    | '/404'
    | '/login'
    | '/reset-password'
    | '/signup'
    | '/unauthorized'
    | '/verification'
    | '/account'
    | '/booking-lesson'
    | '/calendar'
    | '/courses'
    | '/curriculum'
    | '/dashboard'
    | '/lesson-center'
    | '/student-details'
    | '/student-management'
    | '/student-management/$id';
  id:
    | '__root__'
    | '/'
    | '/_auth'
    | '/404'
    | '/login'
    | '/reset-password'
    | '/signup'
    | '/unauthorized'
    | '/verification'
    | '/_auth/account'
    | '/_auth/booking-lesson'
    | '/_auth/calendar'
    | '/_auth/courses'
    | '/_auth/curriculum'
    | '/_auth/dashboard'
    | '/_auth/lesson-center'
    | '/_auth/student-details'
    | '/_auth/student-management'
    | '/_auth/student-management_/$id';
  fileRoutesById: FileRoutesById;
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute;
  AuthRouteRoute: typeof AuthRouteRouteWithChildren;
  R404Route: typeof R404Route;
  LoginRoute: typeof LoginRoute;
  ResetPasswordRoute: typeof ResetPasswordRoute;
  SignupRoute: typeof SignupRoute;
  UnauthorizedRoute: typeof UnauthorizedRoute;
  VerificationRoute: typeof VerificationRoute;
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/verification': {
      id: '/verification';
      path: '/verification';
      fullPath: '/verification';
      preLoaderRoute: typeof VerificationRouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/unauthorized': {
      id: '/unauthorized';
      path: '/unauthorized';
      fullPath: '/unauthorized';
      preLoaderRoute: typeof UnauthorizedRouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/signup': {
      id: '/signup';
      path: '/signup';
      fullPath: '/signup';
      preLoaderRoute: typeof SignupRouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/reset-password': {
      id: '/reset-password';
      path: '/reset-password';
      fullPath: '/reset-password';
      preLoaderRoute: typeof ResetPasswordRouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/login': {
      id: '/login';
      path: '/login';
      fullPath: '/login';
      preLoaderRoute: typeof LoginRouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/404': {
      id: '/404';
      path: '/404';
      fullPath: '/404';
      preLoaderRoute: typeof R404RouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/_auth': {
      id: '/_auth';
      path: '';
      fullPath: '';
      preLoaderRoute: typeof AuthRouteRouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/': {
      id: '/';
      path: '/';
      fullPath: '/';
      preLoaderRoute: typeof IndexRouteImport;
      parentRoute: typeof rootRouteImport;
    };
    '/_auth/student-management': {
      id: '/_auth/student-management';
      path: '/student-management';
      fullPath: '/student-management';
      preLoaderRoute: typeof AuthStudentManagementRouteRouteImport;
      parentRoute: typeof AuthRouteRoute;
    };
    '/_auth/student-details': {
      id: '/_auth/student-details';
      path: '/student-details';
      fullPath: '/student-details';
      preLoaderRoute: typeof AuthStudentDetailsRouteRouteImport;
      parentRoute: typeof AuthRouteRoute;
    };
    '/_auth/lesson-center': {
      id: '/_auth/lesson-center';
      path: '/lesson-center';
      fullPath: '/lesson-center';
      preLoaderRoute: typeof AuthLessonCenterRouteRouteImport;
      parentRoute: typeof AuthRouteRoute;
    };
    '/_auth/dashboard': {
      id: '/_auth/dashboard';
      path: '/dashboard';
      fullPath: '/dashboard';
      preLoaderRoute: typeof AuthDashboardRouteRouteImport;
      parentRoute: typeof AuthRouteRoute;
    };
    '/_auth/curriculum': {
      id: '/_auth/curriculum';
      path: '/curriculum';
      fullPath: '/curriculum';
      preLoaderRoute: typeof AuthCurriculumRouteRouteImport;
      parentRoute: typeof AuthRouteRoute;
    };
    '/_auth/courses': {
      id: '/_auth/courses';
      path: '/courses';
      fullPath: '/courses';
      preLoaderRoute: typeof AuthCoursesRouteRouteImport;
      parentRoute: typeof AuthRouteRoute;
    };
    '/_auth/calendar': {
      id: '/_auth/calendar';
      path: '/calendar';
      fullPath: '/calendar';
      preLoaderRoute: typeof AuthCalendarRouteRouteImport;
      parentRoute: typeof AuthRouteRoute;
    };
    '/_auth/booking-lesson': {
      id: '/_auth/booking-lesson';
      path: '/booking-lesson';
      fullPath: '/booking-lesson';
      preLoaderRoute: typeof AuthBookingLessonRouteRouteImport;
      parentRoute: typeof AuthRouteRoute;
    };
    '/_auth/account': {
      id: '/_auth/account';
      path: '/account';
      fullPath: '/account';
      preLoaderRoute: typeof AuthAccountRouteRouteImport;
      parentRoute: typeof AuthRouteRoute;
    };
    '/_auth/student-management_/$id': {
      id: '/_auth/student-management_/$id';
      path: '/student-management/$id';
      fullPath: '/student-management/$id';
      preLoaderRoute: typeof AuthStudentManagementIdRouteRouteImport;
      parentRoute: typeof AuthRouteRoute;
    };
  }
}

interface AuthRouteRouteChildren {
  AuthAccountRouteRoute: typeof AuthAccountRouteRoute;
  AuthBookingLessonRouteRoute: typeof AuthBookingLessonRouteRoute;
  AuthCalendarRouteRoute: typeof AuthCalendarRouteRoute;
  AuthCoursesRouteRoute: typeof AuthCoursesRouteRoute;
  AuthCurriculumRouteRoute: typeof AuthCurriculumRouteRoute;
  AuthDashboardRouteRoute: typeof AuthDashboardRouteRoute;
  AuthLessonCenterRouteRoute: typeof AuthLessonCenterRouteRoute;
  AuthStudentDetailsRouteRoute: typeof AuthStudentDetailsRouteRoute;
  AuthStudentManagementRouteRoute: typeof AuthStudentManagementRouteRoute;
  AuthStudentManagementIdRouteRoute: typeof AuthStudentManagementIdRouteRoute;
}

const AuthRouteRouteChildren: AuthRouteRouteChildren = {
  AuthAccountRouteRoute: AuthAccountRouteRoute,
  AuthBookingLessonRouteRoute: AuthBookingLessonRouteRoute,
  AuthCalendarRouteRoute: AuthCalendarRouteRoute,
  AuthCoursesRouteRoute: AuthCoursesRouteRoute,
  AuthCurriculumRouteRoute: AuthCurriculumRouteRoute,
  AuthDashboardRouteRoute: AuthDashboardRouteRoute,
  AuthLessonCenterRouteRoute: AuthLessonCenterRouteRoute,
  AuthStudentDetailsRouteRoute: AuthStudentDetailsRouteRoute,
  AuthStudentManagementRouteRoute: AuthStudentManagementRouteRoute,
  AuthStudentManagementIdRouteRoute: AuthStudentManagementIdRouteRoute,
};

const AuthRouteRouteWithChildren = AuthRouteRoute._addFileChildren(
  AuthRouteRouteChildren,
);

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AuthRouteRoute: AuthRouteRouteWithChildren,
  R404Route: R404Route,
  LoginRoute: LoginRoute,
  ResetPasswordRoute: ResetPasswordRoute,
  SignupRoute: SignupRoute,
  UnauthorizedRoute: UnauthorizedRoute,
  VerificationRoute: VerificationRoute,
};
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>();
