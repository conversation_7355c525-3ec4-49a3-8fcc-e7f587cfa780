import { navigationData } from './data/navigationData';

interface NavigationItem {
  title: string;
  icon?: string;
  url: string;
  isActive?: boolean;
  items?: Array<NavigationItem>;
}

export function getPageTitle(pathname: string): string {
  const menuItems = navigationData.main;

  function findTitle(items: Array<NavigationItem>): string | undefined {
    for (const item of items) {
      if (item.url === pathname) return item.title;
      if (item.items) {
        const childTitle = findTitle(item.items);
        if (childTitle) return childTitle;
      }
    }
    return undefined;
  }

  return findTitle(menuItems) ?? 'Untitled Page';
}
