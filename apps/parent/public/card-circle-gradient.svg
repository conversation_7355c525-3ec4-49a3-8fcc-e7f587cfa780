<svg width="133" height="76" viewBox="0 0 133 76" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_41_323" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="-6" width="136" height="82">
<circle cx="122.5" cy="7.5" r="13.5" fill="white" fill-opacity="0.4"/>
<ellipse cx="95.5" cy="35" rx="13.5" ry="14" fill="white" fill-opacity="0.4"/>
<circle cx="68" cy="35" r="14" fill="white" fill-opacity="0.4"/>
<ellipse cx="122.5" cy="35" rx="13.5" ry="14" fill="white" fill-opacity="0.4"/>
<circle cx="122.5" cy="62.5" r="13.5" fill="white" fill-opacity="0.4"/>
<circle cx="95.5" cy="7.5" r="13.5" fill="white" fill-opacity="0.4"/>
<ellipse cx="68" cy="7.5" rx="14" ry="13.5" fill="white" fill-opacity="0.4"/>
<circle cx="40.5" cy="7.5" r="13.5" fill="white" fill-opacity="0.4"/>
<circle cx="13.5" cy="7.5" r="13.5" fill="white" fill-opacity="0.4"/>
</mask>
<g mask="url(#mask0_41_323)">
<rect x="-13" y="-16" width="161" height="101" fill="url(#paint0_linear_41_323)" fill-opacity="0.5"/>
</g>
<defs>
<linearGradient id="paint0_linear_41_323" x1="148" y1="-4.52245" x2="3.5" y2="18" gradientUnits="userSpaceOnUse">
<stop offset="0.879808" stop-color="#FDD36B"/>
<stop offset="1" stop-color="#FDD36B" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
