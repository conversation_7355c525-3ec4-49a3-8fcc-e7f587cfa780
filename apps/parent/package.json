{"name": "parent", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.5", "@tanstack/react-query": "^5.80.7", "@tanstack/react-router": "^1.121.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.518.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "react-resizable-panels": "^3.0.3", "recharts": "^2.15.3", "server": "workspace:*", "shared": "workspace:*", "sonner": "^2.0.5", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.5", "vaul": "^1.1.2", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.22.0", "@tailwindcss/typography": "^0.5.16", "@tanstack/eslint-config": "^0.2.0", "@tanstack/eslint-plugin-query": "^5.78.0", "@tanstack/react-query-devtools": "^5.80.6", "@tanstack/react-router-devtools": "^1.120.20", "@tanstack/router-plugin": "^1.121.29", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/eslint-config-prettier": "^6.11.3", "@types/node": "^22.15.2", "@types/react-dom": "^19.0.4", "@types/react": "^19.0.10", "@typescript-eslint/parser": "^8.34.0", "@vitejs/plugin-react": "^4.3.4", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-import-x": "^4.15.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-react": "^7.37.5", "eslint-plugin-unused-imports": "^4.1.4", "eslint": "^9.22.0", "globals": "^16.0.0", "jsdom": "^26.1.0", "prettier-plugin-tailwindcss": "^0.6.12", "prettier": "^3.5.3", "tw-animate-css": "^1.2.9", "typescript-eslint": "^8.26.1", "typescript": "~5.7.2", "vite-aliases": "^0.11.8", "vite": "^6.3.1", "vitest": "^3.2.3", "web-vitals": "^5.0.2"}}