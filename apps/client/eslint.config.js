import js from '@eslint/js';
import tsParser from '@typescript-eslint/parser';
import eslintConfigPrettier from 'eslint-config-prettier';
import { createTypeScriptImportResolver } from 'eslint-import-resolver-typescript';
import eslintPluginImportX from 'eslint-plugin-import-x';
import react from 'eslint-plugin-react';
import reactHooks from 'eslint-plugin-react-hooks';
import reactRefresh from 'eslint-plugin-react-refresh';
import unusedImports from 'eslint-plugin-unused-imports';
import globals from 'globals';
import TS_ESLint from 'typescript-eslint';

export default TS_ESLint.config(
  { ignores: ['dist', 'eslint.config.js'] },

  {
    extends: [
      js.configs.recommended,
      react.configs.flat.recommended,
      react.configs.flat['jsx-runtime'],
      eslintPluginImportX.flatConfigs.recommended,
      eslintPluginImportX.flatConfigs.typescript,
      ...TS_ESLint.configs.strictTypeChecked,
      ...TS_ESLint.configs.stylisticTypeChecked,
      eslintConfigPrettier,
    ],

    files: ['**/*.{js,mjs,cjs,jsx,mjsx,ts,tsx,mtsx}'],

    languageOptions: {
      // ecmaVersion: 2020,
      globals: { ...globals.browser, ...globals.node },
      parser: tsParser,

      parserOptions: {
        ecmaVersion: 'latest',
        project: [
          './tsconfig.app.json',
          './tsconfig.json',
          './tsconfig.node.json',
        ],
        projectService: true,
        sourceType: 'module',
        tsconfigRootDir: import.meta.dirname,

        ecmaFeatures: {
          jsx: true,
        },
      },
    },

    plugins: {
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
      'unused-imports': unusedImports,
      react,
    },

    settings: {
      react: {
        version: 'detect',
      },
      'import-x/resolver-next': [
        createTypeScriptImportResolver({
          alwaysTryTypes: true,
          // always try to resolve types under `<root>@types` directory even it
          // doesn't contain any source code, like `@types/unist`

          // Choose from one of the "project" configs below or omit to use <root>/tsconfig.json by default

          // use <root>/path/to/folder/tsconfig.json
          // project: '<root>/tsconfig.json',

          // use an array of glob patterns
          project: ['./tsconfig.json'],
        }),
      ],
    },

    ignores: ['.prettierrc.cjs', 'dist'],

    rules: {
      ...reactHooks.configs.recommended.rules,

      'no-console': ['warn', { allow: ['warn', 'error'] }],

      // React
      'react/prop-types': 'off',
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],

      // turn on errors for missing imports
      'import-x/default': 'warn',
      'import-x/no-cycle': 'warn',
      'import-x/no-self-import': 'warn',
      'import-x/no-unassigned-import': [
        'warn',
        {
          allow: ['**/*.css', '**/*.scss', '**/*.sass', '**/*.less'],
        },
      ],
      'import-x/no-useless-path-segments': 'warn',

      // TypeScript
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/array-type': [
        'error',
        {
          default: 'generic',
        },
      ],
      '@typescript-eslint/consistent-type-imports': [
        'warn',
        {
          fixStyle: 'inline-type-imports',
        },
      ],
      '@typescript-eslint/restrict-template-expressions': [
        'error',
        {
          allowNumber: true,
          // allowBoolean: true,
        },
      ],
      '@typescript-eslint/no-misused-promises': [
        'error',
        {
          checksVoidReturn: {
            arguments: false,
            attributes: false,
          },
        },
      ],
      '@typescript-eslint/no-invalid-void-type': [
        'warn',
        { allowInGenericTypeArguments: true, allowAsThisParameter: false },
      ],

      // unused import
      'no-unused-vars': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
      'unused-imports/no-unused-imports': 'error',
      'unused-imports/no-unused-vars': [
        'warn',
        {
          vars: 'all',
          varsIgnorePattern: '^_',
          args: 'after-used',
          argsIgnorePattern: '^_',
        },
      ],
    },
  },
);
