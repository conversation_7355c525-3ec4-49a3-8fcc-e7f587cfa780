{"compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "isolatedModules": true, "moduleDetection": "force", "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@pages/*": ["./src/pages/*"], "@components/*": ["./src/components/*"], "@hooks/*": ["./src/hooks/*"], "@routes/*": ["./src/routes/*"], "@services/*": ["./src/services/*"], "@shared/*": ["./src/shared/*"], "@utils/*": ["./src/utils/*"], "@lib/*": ["./src/lib/*"], "@server/*": ["../server/src/*"], "@client/*": ["./src/*"], "@parent/*": ["../parent/src/*"]}}, "include": ["src"]}