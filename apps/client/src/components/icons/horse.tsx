import type { LucideProps } from 'lucide-react';

export const Horse = (props: LucideProps) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='24'
    height='24'
    viewBox='0 0 24 24'
    fill='none'
    stroke='currentColor'
    strokeWidth='2'
    strokeLinecap='round'
    strokeLinejoin='round'
    {...props}
  >
    <path d='M18 12.5a.5.5 0 0 0-.5-.5H17a2 2 0 0 1-2-2V8.5a.5.5 0 0 0-.5-.5H12a.5.5 0 0 0-.5.5V10a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5V9a.5.5 0 0 0-.5-.5H8a.5.5 0 0 0-.5.5v1.586a1 1 0 0 1-.293.707L6.5 12l.79.793A1 1 0 0 1 7.5 13.5v3a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5V13a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v4a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-4a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v3a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-3a1 1 0 0 1 .293-.707L19.5 11l-.79-.793A1 1 0 0 1 18.5 9.5V8a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5V8a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5V8a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5V8a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.5a2 2 0 0 1-2 2H3.5a.5.5 0 0 0-.5.5V12' />
  </svg>
);
