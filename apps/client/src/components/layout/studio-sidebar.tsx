import {
  Sidebar,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>ooter,
  SidebarHeader,
  SidebarMenu,
  SidebarRail,
} from '@/components/ui/sidebar';

import { FooterActions } from './footer-actions';
import { NavigationItem } from './navigation-item';

// Navigation data structure
const navigationData = {
  main: [
    {
      title: 'Dashboard',
      icon: 'grid',
      url: '/dashboard',
      isActive: true,
    },
    {
      title: 'Calendar',
      icon: 'calendar',
      url: '/lessons/calendar',
      // items: [
      //   {
      //     title: 'Calendar View',
      //     url: '/lessons/calendar',
      //   },
      //   {
      //     title: 'Lesson Scheduling',
      //     url: '/lessons/create',
      //   },
      //   {
      //     title: 'Lesson History',
      //     url: '/lessons/history',
      //   },
      // ],
    },
    {
      title: 'Event Types',
      icon: 'calendar',
      url: '/lessons/create',
      items: [
        {
          title: 'Intro Lesson',
          url: '/lessons/create',
          items: [
            {
              title: 'Sub menu 1',
              url: '/lessons/create',
            },
            {
              title: 'Sub menu 2',
              url: '/lessons/create',
            },
          ],
        },
        {
          title: 'Lesson Series',
          url: '/lessons/create',
          items: [
            {
              title: 'Adult Beginners',
              url: '/lessons/create',
            },
            {
              title: 'Junior Intermediate',
              url: '/lessons/create',
            },
          ],
        },
        {
          title: 'Camp',
          url: '/lessons/create',
          items: [
            {
              title: 'Sub menu 1',
              url: '/lessons/create',
            },
            {
              title: 'Sub menu 2',
              url: '/lessons/create',
            },
          ],
        },
      ],
    },
    {
      title: 'Horse Assignment',
      icon: 'square-check-big',
      url: '/resources/horses-assignment',
    },
    {
      title: 'User Management',
      icon: 'users',
      url: '/users',
      items: [
        {
          title: 'Students',
          url: '/users/students',
        },
        {
          title: 'Account Holder',
          url: '/users/parents',
        },
        {
          title: 'Instructors',
          url: '/users/instructors',
        },
      ],
    },
    {
      title: 'Resources',
      icon: 'folder',
      url: '/resources',
      items: [
        {
          title: 'Horses',
          url: '/resources/horses',
        },
        // {
        //   title: 'Horse Assignment',
        //   url: '/resources/horses-assignment',
        // },
        // {
        //   title: 'Arenas',
        //   url: '/resources/arenas',
        // },
        // {
        //   title: 'Equipments',
        //   url: '/resources/equipments',
        // },
      ],
    },
    {
      title: 'Curriculum',
      icon: 'book',
      url: '/curriculum',
      items: [
        {
          title: 'Curriculum Builder',
          url: '/curriculum/builder',
        },
      ],
    },
    {
      title: 'Pricing',
      icon: 'dollar-sign',
      url: '/pricing',
    },
  ],
};

export function StudioSidebar({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar className='border-r border-gray-200' {...props}>
      <SidebarHeader className='border-b border-gray-100 p-4'>
        Horse Riding
      </SidebarHeader>

      <SidebarContent className='px-2 py-4'>
        <SidebarMenu className='space-y-1'>
          {navigationData.main.map((item) => (
            <NavigationItem key={item.title} item={item} />
          ))}
        </SidebarMenu>
      </SidebarContent>

      <SidebarFooter className='border-t border-gray-100 p-2'>
        <FooterActions />
      </SidebarFooter>

      <SidebarRail />
    </Sidebar>
  );
}
