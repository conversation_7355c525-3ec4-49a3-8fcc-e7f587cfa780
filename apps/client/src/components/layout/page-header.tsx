import { Calendar, LogOut, Mail, MapPin, Phone, User } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

import { useAuth } from '@/hooks/useAuthStore';

interface PageHeaderProps {
  title: string;
}

export function PageHeader({ title }: PageHeaderProps) {
  const { user, logout } = useAuth();

  // Generate user initials
  const getUserInitials = () => {
    if (!user) return 'U';
    const firstInitial = user.first_name.charAt(0).toUpperCase();
    const lastInitial = user.last_name.charAt(0).toUpperCase();
    return `${firstInitial}${lastInitial}` || 'U';
  };

  // Format role for display
  const formatRole = (role: string) => {
    return role
      .split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Format date
  const formatDate = (date: string | Date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString();
  };

  return (
    <header className='sticky top-0 z-30 flex h-16 items-center justify-between border-b border-gray-200 bg-white px-6'>
      {/* Page Title */}
      <div className='flex items-center'>
        <h1 className='text-2xl font-semibold text-gray-900'>{title}</h1>
      </div>

      {/* Right Side Actions */}
      <div className='flex items-center gap-3'>
        {/* Add New Entry Button */}
        {/* {showAddButton && (
          <Button
            onClick={onAddClick}
            className='rounded-lg bg-[#fdd36b] px-4 py-2 font-medium text-gray-900 hover:bg-[#fdd36b]/90'
          >
            <Plus className='mr-2 h-4 w-4' />
            {addButtonText}
          </Button>
        )} */}

        {/* Action Icons */}
        <div className='flex items-center gap-2'>
          {/* User Avatar with Popover */}
          <Popover>
            <PopoverTrigger asChild>
              <button className='rounded-full focus:outline-none'>
                <Avatar className='size-9 cursor-pointer'>
                  <AvatarImage
                    src={user?.profile_image}
                    alt={`${user?.first_name} ${user?.last_name}`}
                  />
                  <AvatarFallback className='bg-blue-100 font-medium text-blue-700'>
                    {getUserInitials()}
                  </AvatarFallback>
                </Avatar>
              </button>
            </PopoverTrigger>
            <PopoverContent className='w-80 p-4' align='end'>
              <div className='space-y-4'>
                {/* Header */}
                <div className='flex items-center gap-3'>
                  <Avatar className='size-12'>
                    <AvatarImage
                      src={user?.profile_image}
                      alt={`${user?.first_name} ${user?.last_name}`}
                    />
                    <AvatarFallback className='bg-blue-100 text-lg font-medium text-blue-700'>
                      {getUserInitials()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className='font-semibold text-gray-900'>
                      {user?.first_name} {user?.last_name}
                    </h3>
                    <p className='text-sm text-gray-500 capitalize'>
                      {user?.role ? formatRole(user.role) : 'User'}
                    </p>
                  </div>
                </div>

                {/* User Details */}
                <div className='space-y-3'>
                  <div className='flex items-center gap-2 text-sm'>
                    <Mail className='h-4 w-4 text-gray-400' />
                    <span className='text-gray-600'>
                      {user?.email ?? 'No email'}
                    </span>
                  </div>

                  {user?.phone && (
                    <div className='flex items-center gap-2 text-sm'>
                      <Phone className='h-4 w-4 text-gray-400' />
                      <span className='text-gray-600'>{user.phone}</span>
                    </div>
                  )}

                  {user?.address && (
                    <div className='flex items-center gap-2 text-sm'>
                      <MapPin className='h-4 w-4 text-gray-400' />
                      <span className='text-gray-600'>{user.address}</span>
                    </div>
                  )}

                  {user?.date_of_birth && (
                    <div className='flex items-center gap-2 text-sm'>
                      <Calendar className='h-4 w-4 text-gray-400' />
                      <span className='text-gray-600'>
                        Born: {formatDate(user.date_of_birth)}
                      </span>
                    </div>
                  )}

                  <div className='flex items-center gap-2 text-sm'>
                    <User className='h-4 w-4 text-gray-400' />
                    <span className='text-gray-600'>
                      Joined: {formatDate(user?.created_at ?? '')}
                    </span>
                  </div>
                </div>

                {/* Actions */}
                <div className='border-t pt-2'>
                  <Button
                    onClick={logout}
                    variant='ghost'
                    className='w-full justify-start text-red-600 hover:bg-red-50 hover:text-red-700'
                  >
                    <LogOut className='mr-2 h-4 w-4' />
                    Sign Out
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </header>
  );
}
