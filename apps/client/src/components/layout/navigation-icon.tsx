import {
  BookOpen,
  Calendar,
  DollarSign,
  FolderOpen,
  LayoutGrid,
  SquareCheckBig,
  Users,
} from 'lucide-react';

interface NavigationIconProps {
  name: string;
  className?: string;
}

const iconMap = {
  grid: LayoutGrid,
  calendar: Calendar,
  users: Users,
  folder: FolderOpen,
  book: BookOpen,
  'dollar-sign': DollarSign,
  'square-check-big': SquareCheckBig,
};

export function NavigationIcon({
  name,
  className = 'size-5',
}: NavigationIconProps) {
  const Icon = iconMap[name as keyof typeof iconMap];

  return <Icon className={className} />;
}
