import { Outlet, useLocation } from '@tanstack/react-router';

import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';

import { getPageTitle } from '@/utils/getPageTitle';

import { PageHeader } from './page-header';
import { StudioSidebar } from './studio-sidebar';

export const Layout = () => {
  const location = useLocation();
  const pageTitle = getPageTitle(location.pathname);
  return (
    <SidebarProvider>
      <StudioSidebar />
      <SidebarInset>
        <PageHeader title={pageTitle} />
        <main className='flex-1 overflow-auto'>
          <Outlet />
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
};
