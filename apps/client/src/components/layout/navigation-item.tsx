import { useLocation, useRouter } from '@tanstack/react-router';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { useState } from 'react';

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import {
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@/components/ui/sidebar';

import { cn } from '@/lib/utils';

import { NavigationIcon } from './navigation-icon';

interface NavigationSubItem {
  title: string;
  url: string;
  isActive?: boolean;
  items?: Array<NavigationSubItem>;
}

interface NavigationItemProps {
  item: {
    title: string;
    icon: string;
    url: string;
    isActive?: boolean;
    items?: Array<NavigationSubItem>;
  };
}

export function NavigationItem({ item }: NavigationItemProps) {
  const router = useRouter();
  const location = useLocation();
  const pathname = location.pathname;
  const [isOpen, setIsOpen] = useState(false);
  const [openSubItems, setOpenSubItems] = useState<Record<string, boolean>>({});

  const handleNavigation = (url: string) => {
    void router.navigate({ to: url });
  };

  const isCurrentPath = (url: string) => {
    return pathname === url;
    // || pathname.startsWith(url);
  };

  const toggleSubItem = (title: string) => {
    setOpenSubItems((prev) => ({
      ...prev,
      [title]: !prev[title],
    }));
  };

  if (!item.items) {
    const isActive = item.isActive ?? isCurrentPath(item.url);

    return (
      <SidebarMenuItem>
        <SidebarMenuButton
          onClick={() => {
            handleNavigation(item.url);
          }}
          isActive={isActive}
          className={cn(
            'h-11 cursor-pointer px-3 text-gray-600 hover:bg-gray-100 hover:text-gray-900',
            isActive && item.title === 'Dashboard'
              ? 'rounded-r-full bg-[#282828] text-[#fdd36b] hover:bg-[#282828] hover:text-[#fdd36b]'
              : '',
          )}
        >
          <div className='flex items-center gap-3'>
            <NavigationIcon
              name={item.icon}
              className={cn(
                'size-5',
                isActive && item.title === 'Dashboard' && 'text-[#fdd36b]',
              )}
            />
            <span className='font-medium'>{item.title}</span>
          </div>
        </SidebarMenuButton>
      </SidebarMenuItem>
    );
  }

  const renderSubItem = (subItem: NavigationSubItem, level = 0) => {
    const isSubActive = isCurrentPath(subItem.url);
    const hasChildren = subItem.items && subItem.items.length > 0;
    const isSubOpen = openSubItems[subItem.title] || false;

    const handleSubItemClick = () => {
      if (hasChildren) {
        toggleSubItem(subItem.title);
      } else {
        // Route to specific pages based on submenu title
        if (subItem.title.toLowerCase().includes('student')) {
          handleNavigation('/users/students');
        } else if (subItem.title.toLowerCase().includes('parent')) {
          handleNavigation('/users/parents');
        } else if (subItem.title.toLowerCase().includes('horse assignment')) {
          handleNavigation('/resources/horses-assignment');
        } else if (subItem.title.toLowerCase().includes('horse')) {
          handleNavigation('/resources/horses');
        } else if (subItem.title.toLowerCase().includes('instructor')) {
          handleNavigation('/users/instructors');
        } else {
          handleNavigation(subItem.url);
        }
      }
    };

    return (
      <SidebarMenuSubItem key={subItem.title}>
        <div className='w-full'>
          <SidebarMenuSubButton
            onClick={handleSubItemClick}
            isActive={isSubActive}
            className={cn(
              'h-9 w-full cursor-pointer px-3 text-gray-500 hover:bg-gray-100 hover:text-gray-900',
              isSubActive &&
                'border-l-2 border-yellow-400 bg-gray-100 font-medium text-gray-900',
              hasChildren && 'flex items-center justify-between',
            )}
          >
            <span>{subItem.title}</span>
            {hasChildren &&
              (isSubOpen ? (
                <ChevronUp className='size-3' />
              ) : (
                <ChevronDown className='size-3' />
              ))}
          </SidebarMenuSubButton>

          {hasChildren && isSubOpen && subItem.items && (
            <SidebarMenuSub className={`mt-1 ml-3 space-y-1`}>
              {subItem.items
                .filter(
                  (nestedItem) =>
                    !nestedItem.title.toLowerCase().includes('arena') &&
                    !nestedItem.title.toLowerCase().includes('equipment'),
                )
                .map((nestedItem) => renderSubItem(nestedItem, level + 1))}
            </SidebarMenuSub>
          )}
        </div>
      </SidebarMenuSubItem>
    );
  };

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen}>
      <SidebarMenuItem>
        <CollapsibleTrigger asChild>
          <SidebarMenuButton className='h-11 w-full justify-between px-3 text-gray-600 hover:bg-gray-100 hover:text-gray-900'>
            <div className='flex items-center gap-3'>
              <NavigationIcon name={item.icon} />
              <span className='font-medium'>{item.title}</span>
            </div>
            {isOpen ? (
              <ChevronUp className='size-4' />
            ) : (
              <ChevronDown className='size-4' />
            )}
          </SidebarMenuButton>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <SidebarMenuSub className='mt-1 ml-6 space-y-1'>
            {item.items
              .filter(
                (subItem) =>
                  !subItem.title.toLowerCase().includes('arena') &&
                  !subItem.title.toLowerCase().includes('equipment'),
              )
              .map((subItem) => renderSubItem(subItem))}
          </SidebarMenuSub>
        </CollapsibleContent>
      </SidebarMenuItem>
    </Collapsible>
  );
}
