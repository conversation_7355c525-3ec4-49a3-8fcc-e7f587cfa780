import {
  Edit2,
  FileText,
  Plus,
  Save,
  Trash2,
  Upload,
  XIcon,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { ScheduleItem } from './schedule-item';

interface DocumentItem {
  id: number;
  name: string;
  uploadDate: string;
  type: string;
}

interface HealthData {
  lastCheckup: Date | null;
  nextCheckupDue: Date | null;
  vaccinations: string;
  healthStatus: string;
  documents: Array<DocumentItem>;
}

interface HealthRecordsCardProps {
  isEditing: boolean;
  healthData: HealthData;
  newDocument: File | null;
  onChange: (updatedData: HealthData) => void;
  onUpload: (file: File | null) => void;
  onAddDocument: (file: File) => void;
  onCancelUpload: () => void;
  onDeleteDocument: (id: number) => void;
  onToggleEdit: () => void;
}

export const HealthRecordsCard = ({
  isEditing,
  healthData,
  newDocument,
  onChange,
  onUpload,
  onAddDocument,
  onCancelUpload,
  onDeleteDocument,
  onToggleEdit,
}: HealthRecordsCardProps) => {
  return (
    <Card className='shadow-sm'>
      <CardHeader className='flex flex-row items-center justify-between'>
        <CardTitle className='text-lg'>Health Records</CardTitle>
        <Button
          variant='outline'
          size='sm'
          onClick={onToggleEdit}
          className='flex items-center gap-2'
        >
          {isEditing ? (
            <Save className='h-4 w-4' />
          ) : (
            <Edit2 className='h-4 w-4' />
          )}
          {isEditing ? 'Save' : 'Edit'}
        </Button>
      </CardHeader>
      <CardContent className='space-y-6'>
        <div className='space-y-3'>
          <ScheduleItem
            label='Last checkup'
            value={healthData.lastCheckup}
            isEditing={isEditing}
            onChange={(date) => {
              onChange({ ...healthData, lastCheckup: date });
            }}
          />

          <ScheduleItem
            label='Next checkup due'
            value={healthData.nextCheckupDue}
            isEditing={isEditing}
            onChange={(date) => {
              onChange({ ...healthData, nextCheckupDue: date });
            }}
          />

          <div className='flex items-center justify-between border-b border-gray-100 py-2'>
            <span className='text-gray-600'>Vaccinations</span>
            {isEditing ? (
              <input
                type='text'
                value={healthData.vaccinations}
                onChange={(e) => {
                  onChange({ ...healthData, vaccinations: e.target.value });
                }}
                className='w-32 rounded border border-gray-300 px-3 py-1'
              />
            ) : (
              <span className='font-semibold text-green-600'>
                {healthData.vaccinations}
              </span>
            )}
          </div>

          <div className='flex items-center justify-between py-2'>
            <span className='text-gray-600'>Health status</span>
            {isEditing ? (
              <select
                value={healthData.healthStatus}
                onChange={(e) => {
                  onChange({ ...healthData, healthStatus: e.target.value });
                }}
                className='rounded border border-gray-300 px-3 py-1'
              >
                {['Excellent', 'Good', 'Fair', 'Poor', 'Under Treatment'].map(
                  (status) => (
                    <option key={status} value={status}>
                      {status}
                    </option>
                  ),
                )}
              </select>
            ) : (
              <span className='font-semibold text-green-600'>
                {healthData.healthStatus}
              </span>
            )}
          </div>
        </div>

        <div className='border-t pt-4'>
          <div className='mb-4 flex items-center justify-between'>
            <h4 className='font-medium text-gray-900'>Health Documents</h4>
            <div className='flex gap-2'>
              <input
                type='file'
                id='document-upload'
                className='hidden'
                accept='.pdf,.doc,.docx,.jpg,.jpeg,.png'
                onChange={(e) => {
                  onUpload(e.target.files?.[0] ?? null);
                }}
              />
              <Button
                variant='outline'
                size='sm'
                onClick={() =>
                  document.getElementById('document-upload')?.click()
                }
                className='flex items-center gap-2'
              >
                <Upload className='h-4 w-4' />
                Upload
              </Button>
            </div>
          </div>

          <div className='space-y-2'>
            {healthData.documents.map((doc) => (
              <div
                key={doc.id}
                className='flex items-center justify-between rounded-lg bg-gray-50 p-3'
              >
                <div className='flex items-center gap-3'>
                  <FileText className='h-5 w-5 text-gray-500' />
                  <div>
                    <p className='text-sm font-medium text-gray-900'>
                      {doc.name}
                    </p>
                    <p className='text-xs text-gray-500'>
                      Uploaded {doc.uploadDate}
                    </p>
                  </div>
                </div>
                <div className='flex items-center gap-2'>
                  <Badge variant='outline' className='text-xs'>
                    {doc.type}
                  </Badge>
                  {isEditing && (
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={() => {
                        onDeleteDocument(doc.id);
                      }}
                    >
                      <Trash2 className='h-4 w-4 text-red-500' />
                    </Button>
                  )}
                </div>
              </div>
            ))}

            {newDocument && (
              <div className='flex items-center justify-between rounded-lg border border-blue-200 bg-blue-50 p-3'>
                <div className='flex items-center gap-3'>
                  <FileText className='h-5 w-5 text-blue-500' />
                  <div>
                    <p className='text-sm font-medium text-blue-900'>
                      {newDocument.name}
                    </p>
                    <p className='text-xs text-blue-600'>Ready to upload</p>
                  </div>
                </div>
                <div className='flex items-center gap-2'>
                  <Button
                    size='sm'
                    onClick={() => {
                      onAddDocument(newDocument);
                    }}
                  >
                    <Plus className='h-4 w-4' />
                  </Button>
                  <Button variant='ghost' size='sm' onClick={onCancelUpload}>
                    <XIcon className='h-4 w-4' />
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
