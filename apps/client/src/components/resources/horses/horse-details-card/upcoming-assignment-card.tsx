import { Badge } from '@/components/ui/badge';

interface UpcomingAssignment {
  name: string;
  className: string;
  instructor: string;
  time: string;
  badgeLabel: string;
  badgeStyle: string;
}

export const UpcomingAssignmentCard = ({
  name,
  className,
  instructor,
  time,
  badgeLabel,
  badgeStyle,
}: UpcomingAssignment) => {
  return (
    <div className='rounded-lg border border-gray-200 p-3'>
      <div className='mb-2 flex items-center justify-between'>
        <span className='font-medium text-gray-900'>{name}</span>
        <Badge variant='outline' className={badgeStyle}>
          {badgeLabel}
        </Badge>
      </div>
      <div className='space-y-1 text-sm text-gray-600'>
        <p>Class: {className}</p>
        <p>Instructor: {instructor}</p>
        <p>Time: {time}</p>
      </div>
    </div>
  );
};
