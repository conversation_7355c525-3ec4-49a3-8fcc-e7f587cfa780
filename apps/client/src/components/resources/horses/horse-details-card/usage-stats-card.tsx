// components/UsageStatsCard.tsx
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

interface UsageStatsCardProps {
  lessonsThisWeek: number;
  lessonsThisMonth: number;
  totalLessons: number;
  totalHours: number;
}

export const UsageStatsCard = ({
  lessonsThisWeek,
  lessonsThisMonth,
  totalLessons,
  totalHours,
}: UsageStatsCardProps) => {
  return (
    <Card className='shadow-sm'>
      <CardHeader>
        <CardTitle className='text-lg'>Usage Metrics</CardTitle>
      </CardHeader>
      <CardContent>
        <div className='space-y-3'>
          <div className='flex items-center justify-between border-b border-gray-100 py-2'>
            <span className='text-gray-600'>Lessons this week</span>
            <span className='font-semibold text-gray-900'>
              {lessonsThisWeek}
            </span>
          </div>
          <div className='flex items-center justify-between border-b border-gray-100 py-2'>
            <span className='text-gray-600'>Lessons this month</span>
            <span className='font-semibold text-gray-900'>
              {lessonsThisMonth}
            </span>
          </div>
          <div className='flex items-center justify-between border-b border-gray-100 py-2'>
            <span className='text-gray-600'>Total lessons</span>
            <span className='font-semibold text-gray-900'>{totalLessons}</span>
          </div>
          <div className='flex items-center justify-between py-2'>
            <span className='text-gray-600'>Total hours worked</span>
            <span className='font-semibold text-gray-900'>{totalHours}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
