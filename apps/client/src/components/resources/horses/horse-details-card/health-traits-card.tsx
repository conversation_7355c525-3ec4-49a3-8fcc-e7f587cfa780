import { Badge } from '@/components/ui/badge';

interface HorseTraitsCardProps {
  specialties: Array<string>;
  trainingLevel?: string;
  suitableFor?: Array<string>;
}

export const HorseTraitsCard = ({
  specialties,
  trainingLevel = 'Advanced',
  suitableFor = ['Beginners', 'Intermediate'],
}: HorseTraitsCardProps) => {
  return (
    <div className='space-y-4 px-6 pb-4'>
      {/* Specialties */}
      <div>
        <h3 className='mb-2 text-sm font-medium text-gray-700'>Specialties</h3>
        <div className='flex flex-wrap gap-2'>
          {specialties.map((specialty, index) => (
            <Badge
              key={index}
              variant='outline'
              className='rounded-full border-blue-200 bg-blue-50 text-blue-700'
            >
              {specialty}
            </Badge>
          ))}
        </div>
      </div>

      {/* Training Level & Suitable For */}
      <div className='grid grid-cols-2 gap-4'>
        <div>
          <h3 className='mb-2 text-sm font-medium text-gray-700'>
            Training Level
          </h3>
          <Badge
            variant='outline'
            className='rounded-full border-green-200 bg-green-50 text-green-700'
          >
            {trainingLevel}
          </Badge>
        </div>
        <div>
          <h3 className='mb-2 text-sm font-medium text-gray-700'>
            Suitable For
          </h3>
          <div className='flex flex-wrap gap-1'>
            {suitableFor.map((level, index) => (
              <Badge
                key={index}
                variant='outline'
                className='rounded-full border-purple-200 bg-purple-50 text-xs text-purple-700'
              >
                {level}
              </Badge>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
