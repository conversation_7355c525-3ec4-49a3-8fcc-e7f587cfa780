import { Badge } from '@/components/ui/badge';

interface CurrentAssignmentProps {
  student: string;
  className: string;
  instructor: string;
  time: string;
}

export const CurrentAssignmentCard = ({
  student,
  className,
  instructor,
  time,
}: CurrentAssignmentProps) => {
  return (
    <div className='rounded-lg border border-blue-200 bg-blue-50 p-4'>
      <div className='mb-3 flex items-center justify-between'>
        <h4 className='font-semibold text-blue-900'>Currently Assigned</h4>
        <Badge className='bg-blue-500 text-white'>Active</Badge>
      </div>
      <div className='space-y-2'>
        <div className='flex items-center justify-between'>
          <span className='font-medium text-blue-700'>Student:</span>
          <span className='text-blue-900'>{student}</span>
        </div>
        <div className='flex items-center justify-between'>
          <span className='font-medium text-blue-700'>Class:</span>
          <span className='text-blue-900'>{className}</span>
        </div>
        <div className='flex items-center justify-between'>
          <span className='font-medium text-blue-700'>Instructor:</span>
          <span className='text-blue-900'>{instructor}</span>
        </div>
        <div className='flex items-center justify-between'>
          <span className='font-medium text-blue-700'>Time:</span>
          <span className='text-blue-900'>{time}</span>
        </div>
      </div>
    </div>
  );
};
