interface HistoryItem {
  name: string;
  className: string;
  date: string;
}

export const RecentAssignmentHistory = ({
  history,
}: {
  history: Array<HistoryItem>;
}) => {
  return (
    <div>
      <h4 className='mb-3 font-medium text-gray-900'>
        Recent Assignment History
      </h4>
      <div className='space-y-2'>
        {history.map((item, idx) => (
          <div
            key={idx}
            className={`flex items-center justify-between py-2 ${idx !== history.length - 1 ? 'border-b border-gray-100' : ''}`}
          >
            <div>
              <span className='font-medium text-gray-900'>{item.name}</span>
              <span className='ml-2 text-sm text-gray-500'>
                {item.className}
              </span>
            </div>
            <span className='text-sm text-gray-500'>{item.date}</span>
          </div>
        ))}
      </div>
    </div>
  );
};
