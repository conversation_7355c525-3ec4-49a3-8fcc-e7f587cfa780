import { ScheduleItem } from './schedule-item';

interface RestScheduleData {
  nextRestDay: Date | null;
  lastRestDay: Date | null;
  restDaysPerWeek: number;
  preferredRestDay: string;
}

interface RestScheduleCardProps {
  isEditing: boolean;
  data: RestScheduleData;
  onChange: (data: RestScheduleData) => void;
}

export const RestScheduleCard = ({
  isEditing,
  data,
  onChange,
}: RestScheduleCardProps) => {
  return (
    <div className='space-y-4'>
      {/* Next Rest Day */}
      <ScheduleItem
        label='Next rest day'
        isEditing={isEditing}
        value={data.nextRestDay}
        onChange={(date) => {
          onChange({ ...data, nextRestDay: date });
        }}
      />

      {/* Last Rest Day */}
      <ScheduleItem
        label='Last rest day'
        isEditing={isEditing}
        value={data.lastRestDay}
        onChange={(date) => {
          onChange({ ...data, lastRestDay: date });
        }}
      />

      {/* Rest Days Per Week */}
      <div className='flex items-center justify-between border-b border-gray-100 py-2'>
        <span className='text-gray-600'>Rest days per week</span>
        {isEditing ? (
          <input
            type='number'
            min='0'
            max='7'
            value={data.restDaysPerWeek}
            onChange={(e) => {
              onChange({
                ...data,
                restDaysPerWeek: parseInt(e.target.value),
              });
            }}
            className='w-16 rounded border border-gray-300 px-2 py-1 text-center'
          />
        ) : (
          <span className='font-semibold text-gray-900'>
            {data.restDaysPerWeek}
          </span>
        )}
      </div>

      {/* Preferred Rest Day */}
      <div className='flex items-center justify-between py-2'>
        <span className='text-gray-600'>Preferred rest day</span>
        {isEditing ? (
          <select
            value={data.preferredRestDay}
            onChange={(e) => {
              onChange({
                ...data,
                preferredRestDay: e.target.value,
              });
            }}
            className='rounded border border-gray-300 px-3 py-1'
          >
            {[
              'Sunday',
              'Monday',
              'Tuesday',
              'Wednesday',
              'Thursday',
              'Friday',
              'Saturday',
            ].map((day) => (
              <option key={day} value={day}>
                {day}
              </option>
            ))}
          </select>
        ) : (
          <span className='font-semibold text-gray-900'>
            {data.preferredRestDay}
          </span>
        )}
      </div>
    </div>
  );
};
