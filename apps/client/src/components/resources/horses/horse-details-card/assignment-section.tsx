import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';

import { CurrentAssignmentCard } from './current-assignment-card';
import { RecentAssignmentHistory } from './recent-assignment-history';
import { UpcomingAssignmentCard } from './upcoming-assignment-card';

const current = {
  student: '<PERSON>',
  className: 'Advanced Jumping - Morning Session',
  instructor: '<PERSON>',
  time: 'Today, 10:00 AM - 11:00 AM',
};

const upcoming = [
  {
    name: '<PERSON>',
    className: 'Intermediate Dressage',
    instructor: '<PERSON>',
    time: '2:00 PM - 3:00 PM',
    badgeLabel: 'Tomorrow',
    badgeStyle: 'border-green-200 bg-green-50 text-green-700',
  },
  {
    name: '<PERSON>',
    className: 'Beginner Trail Riding',
    instructor: '<PERSON>',
    time: '9:00 AM - 10:00 AM',
    badgeLabel: 'Jan 26',
    badgeStyle: 'border-yellow-200 bg-yellow-50 text-yellow-700',
  },
  {
    name: '<PERSON>',
    className: 'Advanced Jumping',
    instructor: '<PERSON>',
    time: '11:00 AM - 12:00 PM',
    badgeLabel: 'Jan 27',
    badgeStyle: 'border-purple-200 bg-purple-50 text-purple-700',
  },
];

const history = [
  {
    name: '<PERSON>',
    className: 'Advanced Jumping',
    date: 'Jan 23, 2024',
  },
  {
    name: '<PERSON> Chen',
    className: 'Intermediate Dressage',
    date: 'Jan 22, 2024',
  },
  {
    name: 'Sophie Williams',
    className: 'Beginner Trail Riding',
    date: 'Jan 21, 2024',
  },
  {
    name: 'Alex Parker',
    className: 'Advanced Jumping',
    date: 'Jan 20, 2024',
  },
];
export const AssignmentSectionCard = () => {
  return (
    <Card className='shadow-sm'>
      <CardHeader>
        <CardTitle className='text-lg'>Current Assignments</CardTitle>
      </CardHeader>
      <CardContent>
        <div className='space-y-4'>
          <CurrentAssignmentCard {...current} />
          <div>
            <h4 className='mb-3 font-medium text-gray-900'>
              Upcoming Assignments
            </h4>
            <div className='space-y-3'>
              {upcoming.map((assignment, index) => (
                <UpcomingAssignmentCard key={index} {...assignment} />
              ))}
            </div>
          </div>
          <RecentAssignmentHistory history={history} />
        </div>
      </CardContent>
    </Card>
  );
};
