import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

import { cn } from '@/lib/utils';

export const ScheduleItem = ({
  label,
  value,
  isEditing,
  onChange,
}: {
  label: string;
  value: Date | null;
  isEditing: boolean;
  onChange: (date: Date | null) => void;
}) => {
  return (
    <div className='flex items-center justify-between border-b border-gray-100 py-2'>
      <span className='text-gray-600'>{label}</span>
      {isEditing ? (
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant='outline'
              className={cn(
                'w-[200px] justify-start text-left font-normal',
                !value && 'text-muted-foreground',
              )}
            >
              <CalendarIcon className='mr-2 h-4 w-4' />
              {value ? format(value, 'PPP') : 'Pick a date'}
            </Button>
          </PopoverTrigger>
          <PopoverContent className='w-auto p-0'>
            <Calendar
              mode='single'
              selected={value ?? undefined}
              onSelect={(date) => {
                onChange(date ?? null);
              }}
              autoFocus
            />
          </PopoverContent>
        </Popover>
      ) : (
        <span className='font-semibold text-gray-900'>
          {value ? format(value, 'EEEE, MMM d') : 'Pick a date'}
        </span>
      )}
    </div>
  );
};
