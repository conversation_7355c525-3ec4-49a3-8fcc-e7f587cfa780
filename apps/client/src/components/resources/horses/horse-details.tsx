import { Edit2, Save } from 'lucide-react';
import { useState } from 'react';

import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader } from '@/components/ui/dialog';
import { <PERSON><PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';

import { AssignmentSectionCard } from './horse-details-card/assignment-section';
import { HealthRecordsCard } from './horse-details-card/health-record-card';
import { HorseTraitsCard } from './horse-details-card/health-traits-card';
import { RestScheduleCard } from './horse-details-card/rest-schedule-card';
import { UsageStatsCard } from './horse-details-card/usage-stats-card';

interface Horse {
  id: string;
  name: string;
  age: number;
  breed: string;
  status: 'available' | 'resting' | 'injured';
  training_level: string;
  specialties?: Array<string> | null;
  suitable_for?: Array<string> | null;
  notes_id?: string | null;
  studio_id: string;
  created_at: Date;
  updated_at: Date | null;
  // Legacy fields for compatibility
  image?: string;
  trainingLevel?: string;
  suitableFor?: Array<string>;
  usageStats?: {
    lessonsThisWeek: number;
    lessonsThisMonth: number;
    totalLessons: number;
    totalHours: number;
  };
  notes?: string;
}

interface HorseDetailsProps {
  horse: Horse | null;
  isOpen: boolean;
  onClose: () => void;
}

export function HorseDetails({ horse, isOpen, onClose }: HorseDetailsProps) {
  const [isEditingSchedule, setIsEditingSchedule] = useState(false);
  const [isEditingHealth, setIsEditingHealth] = useState(false);
  const [scheduleData, setScheduleData] = useState<{
    nextRestDay: Date | null;
    lastRestDay: Date | null;
    restDaysPerWeek: number;
    preferredRestDay: string;
  }>({
    nextRestDay: new Date('2024-01-28'),
    lastRestDay: new Date('2024-01-21'),
    restDaysPerWeek: 1,
    preferredRestDay: 'Sunday',
  });
  const [healthData, setHealthData] = useState<{
    lastCheckup: Date | null;
    nextCheckupDue: Date | null;
    vaccinations: string;
    healthStatus: string;
    documents: Array<{
      id: number;
      name: string;
      uploadDate: string;
      type: string;
    }>;
  }>({
    lastCheckup: new Date('2024-01-15'),
    nextCheckupDue: new Date('2024-04-15'),
    vaccinations: 'Up to date',
    healthStatus: 'Excellent',
    documents: [
      {
        id: 1,
        name: 'Vaccination Record 2024.pdf',
        uploadDate: '2024-01-15',
        type: 'vaccination',
      },
      {
        id: 2,
        name: 'Health Checkup Report.pdf',
        uploadDate: '2024-01-15',
        type: 'checkup',
      },
    ],
  });
  const [newDocument, setNewDocument] = useState<File | null>(null);

  if (!horse) return null;

  const usageStats = horse.usageStats ?? {
    lessonsThisWeek: 8,
    lessonsThisMonth: 32,
    totalLessons: 245,
    totalHours: 368,
  };

  const notes =
    horse.notes ??
    'Thunder is a calm and reliable horse with excellent temperament for beginners. Shows great patience with new riders and responds well to gentle guidance. Prefers morning sessions and works best in cooler weather.';

  function getStatusColor(status: string) {
    switch (status) {
      case 'Available':
        return 'bg-green-500 text-white';
      case 'Resting':
        return 'bg-orange-500 text-white';
      case 'Injured':
        return 'bg-red-500 text-white';
      case 'Unavailable':
        return 'bg-gray-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  }

  function getInitials(name: string) {
    return name
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase())
      .join('')
      .slice(0, 2);
  }
  const tabItems = [
    { value: 'usage', label: 'Usage Statistics' },
    { value: 'assignments', label: 'Assignments' },
    { value: 'schedule', label: 'Rest Schedule' },
    { value: 'health', label: 'Health Records' },
  ];
  const handleAddDocument = (file: File) => {
    const newDoc = {
      id: Date.now(),
      name: file.name,
      uploadDate: new Date().toISOString().split('T')[0],
      type: 'document',
    };
    setHealthData((prev) => ({
      ...prev,
      documents: [...prev.documents, newDoc],
    }));
    setNewDocument(null);
  };

  const handleCancelUpload = () => {
    setNewDocument(null);
  };

  const handleUpload = (file: File | null) => {
    setNewDocument(file);
  };
  const handleDeleteDocument = (id: number) => {
    setHealthData((prev) => ({
      ...prev,
      documents: prev.documents.filter((doc) => doc.id !== id),
    }));
  };

  const toggleEdit = () => {
    setIsEditingHealth((prev) => !prev);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-h-[90vh] w-full overflow-y-auto p-0 sm:max-w-3xl'>
        <div className='relative'>
          {/* Header */}
          <DialogHeader className='p-6 pb-4'>
            <div className='flex items-center gap-4'>
              <Avatar className='h-16 w-16'>
                <AvatarFallback className='bg-gray-200 text-lg font-semibold text-gray-700'>
                  {getInitials(horse.name)}
                </AvatarFallback>
              </Avatar>
              {/* <div className='h-16 w-16 overflow-hidden rounded-full bg-gray-200'>
                <img
                  src={horse.image ?? '/placeholder.svg?height=64&width=64'}
                  alt={horse.name}
                  className='h-full w-full object-cover'
                />
              </div> */}
              <div className='flex-1'>
                <h2 className='text-2xl font-bold text-gray-900'>
                  {horse.name} · Age {horse.age} · {horse.breed}
                </h2>
                <div className='mt-2'>
                  <Badge
                    className={`${getStatusColor(horse.status)} rounded-full px-3 py-1`}
                  >
                    {horse.status}
                  </Badge>
                </div>
              </div>
            </div>
          </DialogHeader>

          <HorseTraitsCard
            specialties={horse.specialties ?? []}
            trainingLevel={horse.trainingLevel}
            suitableFor={horse.suitableFor}
          />

          {/* Tabbed Information Section */}
          <div className='px-6 pb-6'>
            <Tabs defaultValue='usage' className='w-full'>
              <TabsList className='grid w-full grid-cols-4 rounded-lg bg-gray-100'>
                {tabItems.map((tab) => (
                  <TabsTrigger
                    key={tab.value}
                    value={tab.value}
                    className='data-[state=active]:bg-accent data-[state=active]:rounded-none data-[state=active]:border-b-4 data-[state=active]:border-b-amber-500 data-[state=active]:text-amber-500'
                  >
                    {tab.label}
                  </TabsTrigger>
                ))}
              </TabsList>

              <TabsContent value='usage' className='mt-4'>
                <UsageStatsCard
                  lessonsThisWeek={usageStats.lessonsThisWeek}
                  lessonsThisMonth={usageStats.lessonsThisMonth}
                  totalLessons={usageStats.totalLessons}
                  totalHours={usageStats.totalHours}
                />
              </TabsContent>

              <TabsContent value='assignments' className='mt-4'>
                <AssignmentSectionCard />
              </TabsContent>

              <TabsContent value='schedule' className='mt-4'>
                <Card className='shadow-sm'>
                  <CardHeader className='flex flex-row items-center justify-between'>
                    <CardTitle className='text-lg'>Rest Schedule</CardTitle>
                    <Button
                      variant='outline'
                      size='sm'
                      onClick={() => {
                        setIsEditingSchedule(!isEditingSchedule);
                      }}
                      className='flex items-center gap-2'
                    >
                      {isEditingSchedule ? (
                        <Save className='h-4 w-4' />
                      ) : (
                        <Edit2 className='h-4 w-4' />
                      )}
                      {isEditingSchedule ? 'Save' : 'Edit'}
                    </Button>
                  </CardHeader>
                  <CardContent>
                    <RestScheduleCard
                      isEditing={isEditingSchedule}
                      data={scheduleData}
                      onChange={(newData) => {
                        setScheduleData(newData);
                      }}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value='health' className='mt-4'>
                <HealthRecordsCard
                  isEditing={isEditingHealth}
                  healthData={healthData}
                  newDocument={newDocument}
                  onChange={setHealthData}
                  onUpload={handleUpload}
                  onAddDocument={handleAddDocument}
                  onCancelUpload={handleCancelUpload}
                  onDeleteDocument={handleDeleteDocument}
                  onToggleEdit={toggleEdit}
                />
              </TabsContent>
            </Tabs>
          </div>

          {/* Notes Section */}
          <div className='px-6 pb-6'>
            <Card className='shadow-sm'>
              <CardHeader>
                <CardTitle className='text-lg'>Notes</CardTitle>
              </CardHeader>
              <CardContent>
                <p className='leading-relaxed text-gray-700'>{notes}</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
