import { Clock, MapPin } from 'lucide-react';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface Horse {
  id: string;
  name: string;
  status: 'active' | 'injured' | 'inactive';
  tags: Array<string>;
  assignedCount: number;
}
interface Rider {
  id: string;
  name: string;
  assignedHorse?: string;
}
interface TimeSlot {
  id: string;
  time: string;
  lesson: string;
  instructor: string;
  location: string;
  riders: Array<Rider>;
  maxRiders: number;
}

interface AssignmentTableProps {
  timeSlots: Array<TimeSlot>;
  horses: Array<Horse>;
  isHorseAvailable: (horseId: string, slotId: string) => boolean;
  handleHorseAssignment: (
    slotId: string,
    riderId: string,
    horseId: string | null,
  ) => void;
  getAssignedCount: (slot: TimeSlot) => number;
  getHorseLabel: (horse: Horse) => string;
}

export const AssignmentTable = ({
  timeSlots,
  horses,
  isHorseAvailable,
  handleHorseAssignment,
  getAssignedCount,
  getHorseLabel,
}: AssignmentTableProps) => (
  <div className='flex-1 overflow-auto'>
    <table className='w-full border-collapse bg-white'>
      <thead className='sticky top-0 z-10 bg-gray-50'>
        <tr>
          <th className='w-48 border px-4 py-3 text-left text-sm font-medium text-gray-900'>
            Time & Lesson
          </th>
          <th className='w-48 border px-4 py-3 text-left text-sm font-medium text-gray-900'>
            Instructor & Location
          </th>
          <th className='border px-4 py-3 text-left text-sm font-medium text-gray-900'>
            Riders & Horse Assignments
          </th>
          <th className='w-32 border px-4 py-3 text-left text-sm font-medium text-gray-900'>
            Status
          </th>
        </tr>
      </thead>
      <tbody>
        {timeSlots.map((slot) => (
          <tr key={slot.id} className='hover:bg-gray-50'>
            <td className='border px-4 py-3 align-top'>
              <div className='flex items-center gap-2'>
                <Clock className='h-4 w-4 text-gray-400' />
                <div>
                  <div className='font-medium text-gray-900'>{slot.lesson}</div>
                  <div className='text-sm text-gray-600'>{slot.time}</div>
                </div>
              </div>
            </td>
            <td className='border px-4 py-3 align-top'>
              <div className='space-y-1'>
                <div className='font-medium text-gray-900'>
                  {slot.instructor}
                </div>
                <div className='flex items-center gap-1 text-sm text-gray-600'>
                  <MapPin className='h-3 w-3' />
                  {slot.location}
                </div>
              </div>
            </td>
            <td className='border px-4 py-3 align-top'>
              <div className='space-y-2'>
                {slot.riders.map((rider) => (
                  <div key={rider.id} className='flex items-center gap-3'>
                    <div className='w-32 truncate text-sm font-medium text-gray-900'>
                      {rider.name}
                    </div>
                    <Select
                      value={rider.assignedHorse ?? ''}
                      onValueChange={(value) => {
                        handleHorseAssignment(slot.id, rider.id, value || null);
                      }}
                    >
                      <SelectTrigger className='h-8 w-48 text-xs'>
                        <SelectValue placeholder='Select horse...' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='none'>No horse assigned</SelectItem>
                        {horses
                          .filter(
                            (horse) =>
                              isHorseAvailable(horse.id, slot.id) ||
                              horse.id === rider.assignedHorse,
                          )
                          .map((horse) => (
                            <SelectItem
                              key={horse.id}
                              value={horse.id}
                              className={
                                horse.id === rider.assignedHorse
                                  ? 'bg-orange-50'
                                  : ''
                              }
                            >
                              <span
                                className={
                                  horse.status !== 'active' ? 'opacity-50' : ''
                                }
                              >
                                {getHorseLabel(horse)}
                              </span>
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>
                ))}
              </div>
            </td>
            <td className='border px-4 py-3 align-top'>
              <div className='text-sm'>
                <div className='font-medium text-gray-900'>
                  {getAssignedCount(slot)}/{slot.maxRiders} assigned
                </div>
                <div className='mt-1 h-2 w-full rounded-full bg-gray-200'>
                  <div
                    className='h-2 rounded-full bg-blue-600 transition-all'
                    style={{
                      width: `${(getAssignedCount(slot) / slot.maxRiders) * 100}%`,
                    }}
                  />
                </div>
              </div>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  </div>
);
