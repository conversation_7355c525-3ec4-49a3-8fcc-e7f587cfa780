import { Search } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';

export interface Horse {
  id: string;
  name: string;
  status: 'active' | 'injured' | 'inactive';
  tags: Array<string>;
  assignedCount: number;
}

export interface HorsePoolSidebarProps {
  filteredHorses: Array<Horse>;
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  getStatusColor: (status: Horse['status']) => string;
}
export const HorsePoolSidebar = ({
  filteredHorses,
  searchTerm,
  setSearchTerm,
  getStatusColor,
}: HorsePoolSidebarProps) => (
  <div className='flex w-80 flex-col border-l border-gray-200 bg-white'>
    <div className='border-b border-gray-200 p-4'>
      <h3 className='mb-3 font-medium text-gray-900'>
        Horse Pool ({filteredHorses.length})
      </h3>
      <div className='relative'>
        <Search className='absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400' />
        <Input
          placeholder='Search horses...'
          value={searchTerm}
          onChange={(e) => {
            setSearchTerm(e.target.value);
          }}
          className='h-8 pl-9 text-sm'
        />
      </div>
    </div>
    <div className='flex-1 overflow-auto p-4'>
      <div className='space-y-2'>
        {filteredHorses.map((horse) => (
          <div
            key={horse.id}
            className='flex items-center justify-between rounded border border-gray-100 p-2 hover:bg-gray-50'
          >
            <div className='min-w-0 flex-1'>
              <div
                className={`text-sm font-medium ${horse.status !== 'active' ? 'opacity-50' : ''}`}
              >
                {horse.name}
              </div>
              <div className='mt-1 flex items-center gap-2'>
                <Badge
                  variant='secondary'
                  className={`text-xs ${getStatusColor(horse.status)}`}
                >
                  {horse.status}
                </Badge>
                {horse.tags.map((tag) => (
                  <Badge key={tag} variant='outline' className='text-xs'>
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
            <div className='ml-2 text-xs text-gray-500'>
              {horse.assignedCount} assigned
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
);
