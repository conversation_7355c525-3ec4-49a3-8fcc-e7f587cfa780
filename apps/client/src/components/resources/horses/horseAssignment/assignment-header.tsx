import { Calendar, ChevronLeft, ChevronRight } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

type StatusFilter = 'all' | 'active' | 'injured' | 'inactive';
interface AssignmentHeaderProps {
  selectedDate: Date;

  statusFilter: StatusFilter;
  formatDate: (date: Date) => string;
  navigateDate: (direction: 'prev' | 'next') => void;
  goToToday: () => void;
  setStatusFilter: (value: StatusFilter) => void;
}
export const AssignmentHeader = ({
  selectedDate,
  formatDate,
  navigateDate,
  goToToday,
  statusFilter,
  setStatusFilter,
}: AssignmentHeaderProps) => (
  <div className='border-b bg-white px-6 py-4'>
    <div className='flex items-center justify-between'>
      <div>
        <h1 className='text-2xl font-semibold text-gray-900'>
          Horse Assignment Board
        </h1>
        <p className='mt-1 text-sm text-gray-600'>
          Assign horses to riders for scheduled lessons
        </p>
      </div>
      <div className='flex items-center gap-3'>
        <div className='flex items-center gap-2 rounded-lg bg-gray-50 p-1'>
          <Button
            variant='ghost'
            size='sm'
            onClick={() => {
              navigateDate('prev');
            }}
            className='h-8 w-8 p-0'
          >
            <ChevronLeft className='h-4 w-4' />
          </Button>
          <div className='flex items-center gap-2 px-3 py-1'>
            <Calendar className='h-4 w-4 text-gray-500' />
            <span className='min-w-[200px] text-center text-sm font-medium text-gray-900'>
              {formatDate(selectedDate)}
            </span>
          </div>
          <Button
            variant='ghost'
            size='sm'
            onClick={() => {
              navigateDate('next');
            }}
            className='h-8 w-8 p-0'
          >
            <ChevronRight className='h-4 w-4' />
          </Button>
        </div>
        <Button
          variant='outline'
          size='sm'
          onClick={goToToday}
          className='text-xs'
        >
          Today
        </Button>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className='w-32'>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>All Horses</SelectItem>
            <SelectItem value='active'>Active</SelectItem>
            <SelectItem value='injured'>Injured</SelectItem>
            <SelectItem value='inactive'>Inactive</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  </div>
);
