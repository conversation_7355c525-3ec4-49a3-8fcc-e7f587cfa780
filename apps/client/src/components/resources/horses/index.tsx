import { useNavigate } from '@tanstack/react-router';
import { MoreHorizontal, Plus, Search } from 'lucide-react';
import { useEffect, useState } from 'react';

import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';

import { useHorses } from '@/hooks/useHorses';

export interface Horse {
  id: string;
  name: string;
  age: number;
  breed: string;
  status: 'available' | 'resting' | 'injured';
  training_level: string;
  specialties?: Array<string> | null;
  suitable_for?: Array<string> | null;
  notes_id?: string | null;
  studio_id: string;
  created_at: Date;
  updated_at: Date | null;
}

const students = [
  {
    id: 1,
    name: '<PERSON>',
    classes: ['Advanced Jumping - Morning', 'Dressage Basics'],
    skillLevel: 'Advanced',
  },
  {
    id: 2,
    name: 'Michael Chen',
    classes: ['Intermediate Dressage', 'Trail Riding'],
    skillLevel: 'Intermediate',
  },
  {
    id: 3,
    name: 'Sophie Williams',
    classes: ['Beginner Trail Riding', 'Basic Horsemanship'],
    skillLevel: 'Beginner',
  },
  {
    id: 4,
    name: 'Alex Parker',
    classes: ['Advanced Jumping', 'Competition Prep'],
    skillLevel: 'Advanced',
  },
];

interface HorsesProps {
  setSelectedHorse: React.Dispatch<React.SetStateAction<Horse | null>>;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

export const Horses = ({ setSelectedHorse, setIsModalOpen }: HorsesProps) => {
  const navigate = useNavigate();
  const [assignmentDropdowns, setAssignmentDropdowns] = useState<
    Record<string, boolean>
  >({});

  // Fetch horses using the API
  const { data: horsesData, isLoading, error } = useHorses();
  const horses = horsesData?.horses ?? [];

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.relative')) {
        setAssignmentDropdowns({});
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleViewDetails = (horse: Horse) => {
    setSelectedHorse(horse);
    setIsModalOpen(true);
  };

  const toggleAssignmentDropdown = (horseId: string) => (open: boolean) => {
    setAssignmentDropdowns((prev) => ({
      ...prev,
      [horseId]: open,
    }));
  };

  const handleCreateHorse = () => {
    void navigate({ to: '/resources/create-horse' });
  };

  function getStatusColor(status: string) {
    switch (status.toLowerCase()) {
      case 'available':
        return 'bg-green-500 text-white hover:bg-green-600';
      case 'resting':
        return 'bg-orange-500 text-white hover:bg-orange-600';
      case 'injured':
        return 'bg-red-500 text-white hover:bg-red-600';
      default:
        return 'bg-gray-500 text-white hover:bg-gray-600';
    }
  }

  function formatStatus(status: string) {
    return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
  }

  function getInitials(name: string) {
    return name
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase())
      .join('')
      .slice(0, 2);
  }

  if (isLoading) {
    return (
      <div className='min-h-screen space-y-6 bg-gray-50 p-6'>
        <div className='flex min-h-[400px] items-center justify-center'>
          <div className='text-lg text-gray-600'>Loading horses...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='min-h-screen space-y-6 bg-gray-50 p-6'>
        <div className='flex min-h-[400px] items-center justify-center'>
          <div className='text-lg text-red-600'>
            Error loading horses: {error.message}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen space-y-6 bg-gray-50 p-6'>
      {/* Header with Search Bar and Add Button */}
      <div className='flex items-center justify-between'>
        <div className='relative max-w-md'>
          <Search className='absolute top-1/2 left-3 h-5 w-5 -translate-y-1/2 transform text-gray-400' />
          <Input
            placeholder='Search Horses'
            className='h-12 rounded-full border-gray-200 bg-white pl-10 text-gray-600 placeholder:text-gray-400'
          />
        </div>
        <Button
          onClick={handleCreateHorse}
          className='h-12 rounded-xl bg-blue-600 px-6 text-white hover:bg-blue-700'
        >
          <Plus className='mr-2 h-5 w-5' />
          Add Horse
        </Button>
      </div>

      {/* Horses Grid */}
      <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
        {horses.length === 0 ? (
          <div className='col-span-full flex min-h-[300px] items-center justify-center'>
            <div className='text-center'>
              <div className='mb-4 text-lg text-gray-600'>No horses found</div>
              <Button
                onClick={handleCreateHorse}
                className='bg-blue-600 text-white hover:bg-blue-700'
              >
                <Plus className='mr-2 h-4 w-4' />
                Add Your First Horse
              </Button>
            </div>
          </div>
        ) : (
          horses.map((horse) => (
            <Card
              key={horse.id}
              className='overflow-visible rounded-2xl border-0 bg-[#fef7e6] shadow-sm'
            >
              <CardContent className='p-6'>
                {/* Header with horse info and status */}
                <div className='mb-6 flex items-start justify-between'>
                  <div className='flex items-center gap-3'>
                    <Avatar className='h-12 w-12'>
                      <AvatarFallback className='bg-gray-200 font-semibold text-gray-700'>
                        {getInitials(horse.name)}
                      </AvatarFallback>
                    </Avatar>
                    {/* <div className='h-12 w-12 overflow-hidden rounded-full bg-gray-200'>
                      <img
                        src='/placeholder.svg?height=60&width=60'
                        alt={horse.name}
                        className='h-full w-full object-cover'
                      />
                    </div> */}
                    <div>
                      <h3 className='text-lg font-semibold text-gray-900'>
                        {horse.name}
                      </h3>
                      <p className='text-sm text-gray-600'>Age: {horse.age}</p>
                      <p className='text-sm text-gray-600'>{horse.breed}</p>
                    </div>
                  </div>
                  <Badge
                    className={`${getStatusColor(horse.status)} rounded-full px-4 py-1 text-sm font-medium`}
                  >
                    {formatStatus(horse.status)}
                  </Badge>
                </div>

                {/* Specialties */}
                <div className='mb-6'>
                  <h4 className='mb-3 font-medium text-gray-900'>
                    Specialties
                  </h4>
                  <div className='flex flex-wrap gap-2'>
                    {horse.specialties && horse.specialties.length > 0 ? (
                      horse.specialties.map((specialty, index) => (
                        <Badge
                          key={index}
                          variant='outline'
                          className='rounded-full border-gray-300 bg-white px-3 py-1 text-sm text-gray-700 hover:bg-gray-50'
                        >
                          {specialty}
                        </Badge>
                      ))
                    ) : (
                      <span className='text-sm text-gray-500'>
                        No specialties listed
                      </span>
                    )}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className='relative flex gap-3'>
                  <div className='relative flex-1'>
                    <DropdownMenu
                      open={assignmentDropdowns[horse.id]}
                      onOpenChange={(open) => {
                        toggleAssignmentDropdown(horse.id)(open);
                      }}
                    >
                      <DropdownMenuTrigger asChild>
                        <Button className='h-10 w-full rounded-xl bg-gray-800 text-white hover:bg-gray-900'>
                          Assign Student
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        className='max-h-64 w-80 overflow-y-auto rounded-xl border border-gray-200 bg-white p-2 shadow-xl'
                        align='start'
                        side='bottom'
                        sideOffset={4}
                      >
                        <DropdownMenuLabel className='border-b px-3 py-2 text-xs font-medium text-gray-500'>
                          Select Student to Assign
                        </DropdownMenuLabel>
                        {students.map((student) => (
                          <DropdownMenuItem
                            key={student.id}
                            className='cursor-pointer border-b border-gray-100 p-3 last:border-b-0 hover:bg-gray-50 focus:bg-gray-50'
                            onSelect={() => {
                              setAssignmentDropdowns((prev) => ({
                                ...prev,
                                [horse.id]: false,
                              }));
                            }}
                          >
                            <div className='w-full'>
                              <div className='mb-2 flex items-center justify-between'>
                                <span className='font-medium text-gray-900'>
                                  {student.name}
                                </span>
                                <Badge variant='outline' className='text-xs'>
                                  {student.skillLevel}
                                </Badge>
                              </div>
                              <div className='space-y-1'>
                                <div className='text-xs font-medium text-gray-500'>
                                  Enrolled Classes:
                                </div>
                                {student.classes.map((className, index) => (
                                  <div
                                    key={index}
                                    className='mr-1 mb-1 inline-block rounded-md bg-gray-100 px-2 py-1 text-xs text-gray-700'
                                  >
                                    {className}
                                  </div>
                                ))}
                              </div>
                            </div>
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <Button
                    variant='outline'
                    className='h-10 flex-1 rounded-xl border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                    onClick={() => {
                      handleViewDetails(horse);
                    }}
                  >
                    View Details
                  </Button>
                  <Button
                    variant='outline'
                    size='icon'
                    className='h-10 w-10 rounded-xl border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                  >
                    <MoreHorizontal className='h-4 w-4' />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};
