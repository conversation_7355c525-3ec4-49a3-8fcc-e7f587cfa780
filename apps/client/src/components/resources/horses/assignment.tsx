import { useState } from 'react';

import { AssignmentHeader } from './horseAssignment/assignment-header';
import { AssignmentTable } from './horseAssignment/assignment-table';
import { HorsePoolSidebar } from './horseAssignment/horse-pool-sidebar';

interface Horse {
  id: string;
  name: string;
  status: 'active' | 'injured' | 'inactive';
  tags: Array<string>;
  assignedCount: number;
}

interface Rider {
  id: string;
  name: string;
  assignedHorse?: string;
}

interface TimeSlot {
  id: string;
  time: string;
  lesson: string;
  instructor: string;
  location: string;
  riders: Array<Rider>;
  maxRiders: number;
}

const mockHorses: Array<Horse> = [
  {
    id: '1',
    name: 'Romeo',
    status: 'active',
    tags: ['sale'],
    assignedCount: 1,
  },
  {
    id: '2',
    name: '<PERSON>',
    status: 'active',
    tags: ['in training'],
    assignedCount: 0,
  },
  {
    id: '3',
    name: 'Katie',
    status: 'active',
    tags: ['leased'],
    assignedCount: 2,
  },
  { id: '4', name: '<PERSON>', status: 'active', tags: [], assignedCount: 1 },
  {
    id: '5',
    name: '<PERSON>',
    status: 'injured',
    tags: ['sale'],
    assignedCount: 0,
  },
  {
    id: '6',
    name: 'Storm',
    status: 'active',
    tags: ['in training'],
    assignedCount: 0,
  },
  {
    id: '7',
    name: 'Luna',
    status: 'inactive',
    tags: ['leased'],
    assignedCount: 0,
  },
  { id: '8', name: 'Max', status: 'active', tags: [], assignedCount: 1 },
];

const mockTimeSlots: Array<TimeSlot> = [
  {
    id: '1',
    time: '11:00 AM - 12:00 PM',
    lesson: 'B3',
    instructor: 'Sarah Johnson',
    location: 'Arena 1',
    maxRiders: 3,
    riders: [
      { id: '1', name: 'Emma Wilson', assignedHorse: '1' },
      { id: '2', name: 'Jake Miller', assignedHorse: '4' },
      { id: '3', name: 'Sophie Chen' },
    ],
  },
  {
    id: '2',
    time: '12:00 PM - 1:00 PM',
    lesson: 'A2',
    instructor: 'Mike Davis',
    location: 'Arena 2',
    maxRiders: 4,
    riders: [
      { id: '4', name: 'Alex Brown', assignedHorse: '3' },
      { id: '5', name: 'Lily Garcia' },
      { id: '6', name: 'Noah Taylor' },
      { id: '7', name: 'Zoe Anderson' },
    ],
  },
  {
    id: '3',
    time: '1:00 PM - 2:00 PM',
    lesson: 'C1',
    instructor: 'Lisa Thompson',
    location: 'Arena 1',
    maxRiders: 2,
    riders: [
      { id: '8', name: 'Ryan Martinez', assignedHorse: '8' },
      { id: '9', name: 'Maya Patel' },
    ],
  },
  {
    id: '4',
    time: '2:00 PM - 3:00 PM',
    lesson: 'B1',
    instructor: 'Sarah Johnson',
    location: 'Arena 3',
    maxRiders: 3,
    riders: [
      { id: '10', name: 'Ethan Lee' },
      { id: '11', name: 'Grace Kim' },
      { id: '12', name: 'Lucas White' },
    ],
  },
];

export const HorseAssignment = () => {
  const [timeSlots, setTimeSlots] = useState<Array<TimeSlot>>(mockTimeSlots);
  const [horses] = useState<Array<Horse>>(mockHorses);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<
    'all' | 'active' | 'injured' | 'inactive'
  >('active');
  const [selectedDate, setSelectedDate] = useState(new Date());

  const filteredHorses = horses.filter((horse) => {
    const matchesSearch = horse.name
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === 'all' || horse.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getAssignedHorses = (currentSlotId: string) => {
    const currentSlot = timeSlots.find((slot) => slot.id === currentSlotId);
    if (!currentSlot) return [];

    return (
      currentSlot.riders
        .filter((rider) => rider.assignedHorse)
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        .map((rider) => rider.assignedHorse!)
    );
  };

  const isHorseAvailable = (horseId: string, slotId: string) => {
    const horse = horses.find((h) => h.id === horseId);
    if (!horse || horse.status !== 'active') return false;

    const assignedHorses = getAssignedHorses(slotId);
    return !assignedHorses.includes(horseId);
  };

  const handleHorseAssignment = (
    slotId: string,
    riderId: string,
    horseId: string | null,
  ) => {
    setTimeSlots((prev) =>
      prev.map((slot) => {
        if (slot.id === slotId) {
          return {
            ...slot,
            riders: slot.riders.map((rider) => {
              if (rider.id === riderId) {
                return { ...rider, assignedHorse: horseId ?? undefined };
              }
              return rider;
            }),
          };
        }
        return slot;
      }),
    );
  };

  const getAssignedCount = (slot: TimeSlot) => {
    return slot.riders.filter((rider) => rider.assignedHorse).length;
  };

  const getHorseLabel = (horse: Horse) => {
    const tags = horse.tags.length > 0 ? ` (${horse.tags.join(', ')})` : '';
    return `${horse.name}${tags}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'injured':
        return 'bg-red-100 text-red-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    setSelectedDate((prev) => {
      const newDate = new Date(prev);
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
      return newDate;
    });
  };

  const goToToday = () => {
    setSelectedDate(new Date());
  };

  return (
    <div className='flex h-screen bg-gray-50'>
      {/* Main Content */}
      <div className='flex flex-1 flex-col'>
        {/* Header */}
        {/* <div className='border-b bg-white px-6 py-4'>
          <div className='flex items-center justify-between'>
            <div>
              <h1 className='text-2xl font-semibold text-gray-900'>
                Horse Assignment Board
              </h1>
              <p className='mt-1 text-sm text-gray-600'>
                Assign horses to riders for scheduled lessons
              </p>
            </div>
            <div className='flex items-center gap-3'>
              <div className='flex items-center gap-2 rounded-lg bg-gray-50 p-1'>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => {
                    navigateDate('prev');
                  }}
                  className='h-8 w-8 p-0'
                >
                  <ChevronLeft className='h-4 w-4' />
                </Button>
                <div className='flex items-center gap-2 px-3 py-1'>
                  <Calendar className='h-4 w-4 text-gray-500' />
                  <span className='min-w-[200px] text-center text-sm font-medium text-gray-900'>
                    {formatDate(selectedDate)}
                  </span>
                </div>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => {
                    navigateDate('next');
                  }}
                  className='h-8 w-8 p-0'
                >
                  <ChevronRight className='h-4 w-4' />
                </Button>
              </div>
              <Button
                variant='outline'
                size='sm'
                onClick={goToToday}
                className='text-xs'
              >
                Today
              </Button>
              <Select
                value={statusFilter}
                onValueChange={(
                  value: 'active' | 'injured' | 'inactive' | 'all',
                ) => {
                  setStatusFilter(value);
                }}
              >
                <SelectTrigger className='w-32'>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All Horses</SelectItem>
                  <SelectItem value='active'>Active</SelectItem>
                  <SelectItem value='injured'>Injured</SelectItem>
                  <SelectItem value='inactive'>Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div> */}
        <AssignmentHeader
          setStatusFilter={setStatusFilter}
          selectedDate={selectedDate}
          formatDate={formatDate}
          navigateDate={navigateDate}
          statusFilter={statusFilter}
          goToToday={goToToday}
        />
        {/* Spreadsheet Table */}
        {/* <div className='flex-1 overflow-auto'>
          <table className='w-full border-collapse bg-white'>
            <thead className='sticky top-0 z-10 bg-gray-50'>
              <tr>
                <th className='w-48 border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-900'>
                  Time & Lesson
                </th>
                <th className='w-48 border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-900'>
                  Instructor & Location
                </th>
                <th className='border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-900'>
                  Riders & Horse Assignments
                </th>
                <th className='w-32 border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-900'>
                  Status
                </th>
              </tr>
            </thead>
            <tbody>
              {timeSlots.map((slot) => (
                <tr key={slot.id} className='hover:bg-gray-50'>
                  <td className='border border-gray-200 px-4 py-3 align-top'>
                    <div className='flex items-center gap-2'>
                      <Clock className='h-4 w-4 text-gray-400' />
                      <div>
                        <div className='font-medium text-gray-900'>
                          {slot.lesson}
                        </div>
                        <div className='text-sm text-gray-600'>{slot.time}</div>
                      </div>
                    </div>
                  </td>
                  <td className='border border-gray-200 px-4 py-3 align-top'>
                    <div className='space-y-1'>
                      <div className='font-medium text-gray-900'>
                        {slot.instructor}
                      </div>
                      <div className='flex items-center gap-1 text-sm text-gray-600'>
                        <MapPin className='h-3 w-3' />
                        {slot.location}
                      </div>
                    </div>
                  </td>
                  <td className='border border-gray-200 px-4 py-3 align-top'>
                    <div className='space-y-2'>
                      {slot.riders.map((rider) => (
                        <div key={rider.id} className='flex items-center gap-3'>
                          <div className='w-32 truncate text-sm font-medium text-gray-900'>
                            {rider.name}
                          </div>
                          <Select
                            value={rider.assignedHorse ?? ''}
                            onValueChange={(value) => {
                              handleHorseAssignment(
                                slot.id,
                                rider.id,
                                value || null,
                              );
                            }}
                          >
                            <SelectTrigger className='h-8 w-48 text-xs'>
                              <SelectValue placeholder='Select horse...' />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value='none'>
                                No horse assigned
                              </SelectItem>
                              {horses
                                .filter(
                                  (horse) =>
                                    isHorseAvailable(horse.id, slot.id) ||
                                    horse.id === rider.assignedHorse,
                                )
                                .map((horse) => (
                                  <SelectItem
                                    key={horse.id}
                                    value={horse.id}
                                    className={
                                      horse.id === rider.assignedHorse
                                        ? 'bg-orange-50'
                                        : ''
                                    }
                                  >
                                    <span
                                      className={
                                        horse.status !== 'active'
                                          ? 'opacity-50'
                                          : ''
                                      }
                                    >
                                      {getHorseLabel(horse)}
                                    </span>
                                  </SelectItem>
                                ))}
                            </SelectContent>
                          </Select>
                        </div>
                      ))}
                    </div>
                  </td>
                  <td className='border border-gray-200 px-4 py-3 align-top'>
                    <div className='text-sm'>
                      <div className='font-medium text-gray-900'>
                        {getAssignedCount(slot)}/{slot.maxRiders} assigned
                      </div>
                      <div className='mt-1 h-2 w-full rounded-full bg-gray-200'>
                        <div
                          className='h-2 rounded-full bg-blue-600 transition-all'
                          style={{
                            width: `${(getAssignedCount(slot) / slot.maxRiders) * 100}%`,
                          }}
                        />
                      </div>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div> */}
        <AssignmentTable
          timeSlots={timeSlots}
          horses={horses}
          isHorseAvailable={isHorseAvailable}
          handleHorseAssignment={handleHorseAssignment}
          getAssignedCount={getAssignedCount}
          getHorseLabel={getHorseLabel}
        />
      </div>
      {/* Horse Pool Sidebar */}
      {/* <div className='flex w-80 flex-col border-l border-gray-200 bg-white'>
        <div className='border-b border-gray-200 p-4'>
          <h3 className='mb-3 font-medium text-gray-900'>
            Horse Pool ({filteredHorses.length})
          </h3>
          <div className='relative'>
            <Search className='absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400' />
            <Input
              placeholder='Search horses...'
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
              }}
              className='h-8 pl-9 text-sm'
            />
          </div>
        </div>

        <div className='flex-1 overflow-auto p-4'>
          <div className='space-y-2'>
            {filteredHorses.map((horse) => (
              <div
                key={horse.id}
                className='flex items-center justify-between rounded border border-gray-100 p-2 hover:bg-gray-50'
              >
                <div className='min-w-0 flex-1'>
                  <div
                    className={`text-sm font-medium ${horse.status !== 'active' ? 'opacity-50' : ''}`}
                  >
                    {horse.name}
                  </div>
                  <div className='mt-1 flex items-center gap-2'>
                    <Badge
                      variant='secondary'
                      className={`text-xs ${getStatusColor(horse.status)}`}
                    >
                      {horse.status}
                    </Badge>
                    {horse.tags.map((tag) => (
                      <Badge key={tag} variant='outline' className='text-xs'>
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div className='ml-2 text-xs text-gray-500'>
                  {horse.assignedCount} assigned
                </div>
              </div>
            ))}
          </div>
        </div> */}
      <HorsePoolSidebar
        filteredHorses={filteredHorses}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        getStatusColor={getStatusColor}
      />
    </div>
  );
};
