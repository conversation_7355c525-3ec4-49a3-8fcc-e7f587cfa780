import { Search, Users, X } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface HorseOption {
  value: string;
  label: string;
  level: string;
  age: number;
}

interface AssignedHorsesProps {
  watchedAssignedHorses: Array<string>;
  horseOptions: Array<HorseOption>;
  filteredHorses: Array<HorseOption>;
  horseSearch: string;
  showHorseDropdown: boolean;
  setHorseSearch: (val: string) => void;
  setShowHorseDropdown: (val: boolean) => void;
  addHorse: (horseId: string) => void;
  removeHorse: (horseId: string) => void;
  getHorseLevelColor: (level: string) => string;
}

export const AssignedHorses: React.FC<AssignedHorsesProps> = ({
  watchedAssignedHorses,
  horseOptions,
  filteredHorses,
  horseSearch,
  showHorseDropdown,
  setHorseSearch,
  setShowHorseDropdown,
  addHorse,
  removeHorse,
  getHorseLevelColor,
}) => {
  return (
    <Card className='border-gray-200 bg-white shadow-sm'>
      <CardHeader className='pb-4'>
        <CardTitle className='flex items-center gap-2 text-lg font-semibold text-gray-900'>
          <Users className='size-5 text-orange-600' />
          Assigned Horses
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        {/* Selected Horses */}
        <div>
          <Label className='mb-3 block text-sm font-medium text-gray-700'>
            Selected Horses
          </Label>
          <div className='flex min-h-[40px] flex-wrap gap-2 rounded-lg border border-gray-300 bg-gray-50 p-3'>
            {watchedAssignedHorses.length === 0 ? (
              <span className='text-sm text-gray-500'>
                No horses assigned yet
              </span>
            ) : (
              watchedAssignedHorses.map((horseId) => {
                const horse = horseOptions.find((h) => h.value === horseId);
                return (
                  <Badge
                    key={horseId}
                    variant='secondary'
                    className='flex items-center gap-2 rounded-full border border-orange-200 bg-orange-100 px-3 py-1 text-orange-800'
                  >
                    <span className='font-medium'>{horse?.label}</span>
                    <Badge
                      className={`${getHorseLevelColor(horse?.level ?? '')} px-1.5 py-0.5 text-xs`}
                    >
                      {horse?.level}
                    </Badge>
                    <X
                      className='size-3 cursor-pointer transition-colors hover:text-red-600'
                      onClick={() => {
                        removeHorse(horseId);
                      }}
                    />
                  </Badge>
                );
              })
            )}
          </div>
        </div>

        {/* Horse Search */}
        <div className='relative'>
          <Label className='mb-2 block text-sm font-medium text-gray-700'>
            Add Horses
          </Label>
          <div className='relative'>
            <Search className='absolute top-1/2 left-3 size-4 -translate-y-1/2 transform text-gray-400' />
            <Input
              value={horseSearch}
              onChange={(e) => {
                setHorseSearch(e.target.value);
                setShowHorseDropdown(e.target.value.length > 0);
              }}
              onFocus={() => {
                setShowHorseDropdown(horseSearch.length > 0);
              }}
              placeholder='Search horses by name...'
              className='h-12 border-gray-300 bg-white pl-10 focus:border-blue-500 focus:ring-blue-500'
            />
          </div>

          {showHorseDropdown && filteredHorses.length > 0 && (
            <div className='absolute z-10 mt-1 max-h-60 w-full overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-lg'>
              {filteredHorses.map((horse) => (
                <div
                  key={horse.value}
                  onClick={() => {
                    addHorse(horse.value);
                  }}
                  className='flex cursor-pointer items-center justify-between border-b border-gray-100 p-3 last:border-b-0 hover:bg-gray-50'
                >
                  <div>
                    <p className='font-medium text-gray-900'>{horse.label}</p>
                    <p className='text-sm text-gray-500'>
                      Age: {horse.age} years
                    </p>
                  </div>
                  <Badge
                    className={`${getHorseLevelColor(horse.level)} px-2 py-1 text-xs`}
                  >
                    {horse.level}
                  </Badge>
                </div>
              ))}
            </div>
          )}
        </div>

        <p className='text-xs text-gray-500'>
          Assign horses that this instructor is qualified to work with based on
          their experience level.
        </p>
      </CardContent>
    </Card>
  );
};
