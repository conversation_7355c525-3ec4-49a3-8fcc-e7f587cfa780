// components/BioNotes.tsx
import { FileText } from 'lucide-react';
import { type Control, Controller } from 'react-hook-form';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { type InstructorFormData } from '@/components/users/instructors/create';

interface BioNotesProps {
  control: Control<InstructorFormData>;
}

export const BioNotes: React.FC<BioNotesProps> = ({ control }) => {
  return (
    <Card className='border-gray-200 bg-white shadow-sm'>
      <CardHeader className='pb-4'>
        <CardTitle className='flex items-center gap-2 text-lg font-semibold text-gray-900'>
          <FileText className='h-5 w-5 text-gray-600' />
          Bio / Notes
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div>
          <Label
            htmlFor='bio'
            className='mb-2 block text-sm font-medium text-gray-700'
          >
            Instructor Bio & Additional Notes
          </Label>
          <Controller
            name='bio'
            control={control}
            render={({ field }) => (
              <Textarea
                {...field}
                placeholder="Enter instructor's background, certifications, teaching philosophy, or any additional notes..."
                className='min-h-[120px] resize-none border-gray-300 bg-white focus:border-blue-500 focus:ring-blue-500'
              />
            )}
          />
          <p className='mt-2 text-xs text-gray-500'>
            Include certifications, achievements, teaching style, or any other
            relevant information.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
