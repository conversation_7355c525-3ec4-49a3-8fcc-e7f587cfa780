// components/InstructorFilters.tsx
import { Search } from 'lucide-react';

import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface Option {
  value: string;
  label: string;
}

interface InstructorFiltersProps {
  searchTerm: string;
  setSearchTerm: (value: string) => void;

  statusFilter: string;
  setStatusFilter: (value: string) => void;
  statusOptions: Array<Option>;

  specializationFilter: string;
  setSpecializationFilter: (value: string) => void;
  specializationOptions: Array<Option>;
}

export const InstructorFilters: React.FC<InstructorFiltersProps> = ({
  searchTerm,
  setSearchTerm,
  statusFilter,
  setStatusFilter,
  statusOptions,
  specializationFilter,
  setSpecializationFilter,
  specializationOptions,
}) => {
  return (
    <Card className='border-gray-200 bg-white shadow-sm'>
      <CardContent className='p-6'>
        <div className='flex flex-col items-center justify-between gap-4 md:flex-row'>
          {/* Search */}
          <div className='relative max-w-md flex-1'>
            <Search className='absolute top-1/2 left-3 size-4 -translate-y-1/2 transform text-gray-400' />
            <Input
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
              }}
              placeholder='Search instructors...'
              className='h-11 border-gray-200 bg-gray-50 pl-10 focus:border-blue-500 focus:ring-blue-500'
            />
          </div>

          {/* Filters */}
          <div className='flex gap-4'>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className='h-11 w-48 border-gray-200 bg-white focus:border-blue-500 focus:ring-blue-500'>
                <SelectValue placeholder='Select status' />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={specializationFilter}
              onValueChange={setSpecializationFilter}
            >
              <SelectTrigger className='h-11 w-56 border-gray-200 bg-white focus:border-blue-500 focus:ring-blue-500'>
                <SelectValue placeholder='Select specialization' />
              </SelectTrigger>
              <SelectContent>
                {specializationOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
