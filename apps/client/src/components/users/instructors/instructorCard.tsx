// components/InstructorCard.tsx
import { Edit, Eye, Mail, MoreHorizontal, Phone, Trash2 } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface Instructor {
  id: string;
  name: string;
  avatar: string;
  status: string;
  email: string;
  phone: string;
  specialization: string;
  experience: number;
  totalLessons: number;
  assignedHorses: Array<string>;
}

interface InstructorCardProps {
  instructor: Instructor;
  getStatusColor: (status: string) => string;
  getSpecializationColor: (spec: string) => string;
  handleViewInstructor: (id: string) => void;
  handleEditInstructor: (id: string) => void;
  handleDeleteInstructor: (id: string) => void;
}

export const InstructorCard: React.FC<InstructorCardProps> = ({
  instructor,
  getStatusColor,
  getSpecializationColor,
  handleViewInstructor,
  handleEditInstructor,
  handleDeleteInstructor,
}) => {
  return (
    <Card className='border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md'>
      <CardHeader className='pb-4'>
        <div className='flex items-start justify-between'>
          <div className='flex items-center gap-3'>
            <Avatar className='h-12 w-12'>
              <AvatarImage
                src={instructor.avatar || '/placeholder.svg'}
                alt={instructor.name}
              />
              <AvatarFallback className='bg-blue-100 font-medium text-blue-700'>
                {instructor.name
                  .split(' ')
                  .map((n) => n[0])
                  .join('')}
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className='font-semibold text-gray-900'>{instructor.name}</h3>
              <Badge
                className={`${getStatusColor(instructor.status)} mt-1 border px-2 py-1 text-xs`}
              >
                {instructor.status}
              </Badge>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='ghost' size='sm' className='size-8 p-0'>
                <MoreHorizontal className='size-4' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              <DropdownMenuItem
                onClick={() => {
                  handleViewInstructor(instructor.id);
                }}
              >
                <Eye className='mr-2 size-4' />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  handleEditInstructor(instructor.id);
                }}
              >
                <Edit className='mr-2 size-4' />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => {
                  handleDeleteInstructor(instructor.id);
                }}
                className='text-red-600'
              >
                <Trash2 className='mr-2 size-4' />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className='space-y-4'>
        {/* Contact Info */}
        <div className='space-y-2'>
          <div className='flex items-center gap-2 text-sm text-gray-600'>
            <Mail className='size-4' />
            <span className='truncate'>{instructor.email}</span>
          </div>
          <div className='flex items-center gap-2 text-sm text-gray-600'>
            <Phone className='size-4' />
            <span>{instructor.phone}</span>
          </div>
        </div>

        {/* Specialization */}
        <div>
          <Badge
            className={`${getSpecializationColor(instructor.specialization)} border px-2 py-1 text-xs`}
          >
            {instructor.specialization}
          </Badge>
        </div>

        {/* Experience & Stats */}
        <div className='grid grid-cols-2 gap-4 text-sm'>
          <div>
            <span className='text-gray-500'>Experience</span>
            <p className='font-medium text-gray-900'>
              {instructor.experience} years
            </p>
          </div>
          <div>
            <span className='text-gray-500'>Lessons</span>
            <p className='font-medium text-gray-900'>
              {instructor.totalLessons}
            </p>
          </div>
        </div>

        {/* Assigned Horses */}
        <div>
          <span className='mb-2 block text-sm text-gray-500'>
            Assigned Horses:
          </span>
          <div className='flex flex-wrap gap-1'>
            {instructor.assignedHorses.slice(0, 3).map((horse) => (
              <Badge
                key={horse}
                variant='outline'
                className='px-2 py-0.5 text-xs'
              >
                {horse}
              </Badge>
            ))}
            {instructor.assignedHorses.length > 3 && (
              <Badge variant='outline' className='px-2 py-0.5 text-xs'>
                +{instructor.assignedHorses.length - 3} more
              </Badge>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className='flex gap-2 pt-2'>
          <Button
            variant='outline'
            size='sm'
            className='flex-1'
            onClick={() => {
              handleViewInstructor(instructor.id);
            }}
          >
            View Profile
          </Button>
          <Button
            variant='outline'
            size='sm'
            className='flex-1'
            onClick={() => {
              handleEditInstructor(instructor.id);
            }}
          >
            Edit
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
