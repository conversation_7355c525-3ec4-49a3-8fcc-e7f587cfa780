import { useNavigate } from '@tanstack/react-router';
import { Plus, Search } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

import { InstructorCard } from './instructorCard';
import { InstructorFilters } from './instructorFilter';
import { StatsCards } from './statsCards';

export interface Instructor {
  id: string;
  name: string;
  email: string;
  phone: string;
  specialization: string;
  experience: number;
  avatar: string;
  status: 'Active' | 'Inactive' | 'On Leave';
  availability: Array<string>;
  assignedHorses: Array<string>;
  totalLessons: number;
}

const instructorsData: Array<Instructor> = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '(*************',
    specialization: 'Dressage',
    experience: 8,
    avatar: '/placeholder.svg?height=40&width=40',
    status: 'Active',
    availability: ['monday', 'tuesday', 'wednesday', 'friday'],
    assignedHorses: ['Thunder', 'Spirit', 'Grace'],
    totalLessons: 156,
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '(*************',
    specialization: 'Show Jumping',
    experience: 12,
    avatar: '/placeholder.svg?height=40&width=40',
    status: 'Active',
    availability: ['tuesday', 'thursday', 'friday', 'saturday'],
    assignedHorses: ['Hunter', 'Lightning', 'Brave'],
    totalLessons: 203,
  },
  {
    id: '3',
    name: 'Anna Wilson',
    email: '<EMAIL>',
    phone: '(*************',
    specialization: 'Beginner Riding',
    experience: 5,
    avatar: '/placeholder.svg?height=40&width=40',
    status: 'Active',
    availability: ['monday', 'wednesday', 'thursday', 'saturday'],
    assignedHorses: ['Gentle', 'Star', 'Sunshine'],
    totalLessons: 89,
  },
  {
    id: '4',
    name: 'David Brown',
    email: '<EMAIL>',
    phone: '(*************',
    specialization: 'Cross Country',
    experience: 15,
    avatar: '/placeholder.svg?height=40&width=40',
    status: 'On Leave',
    availability: ['friday', 'saturday', 'sunday'],
    assignedHorses: ['Storm', 'Midnight'],
    totalLessons: 267,
  },
  {
    id: '5',
    name: 'Jessica Taylor',
    email: '<EMAIL>',
    phone: '(*************',
    specialization: 'Western Riding',
    experience: 7,
    avatar: '/placeholder.svg?height=40&width=40',
    status: 'Active',
    availability: ['monday', 'tuesday', 'thursday', 'sunday'],
    assignedHorses: ['Whisper', 'Brave'],
    totalLessons: 134,
  },
  {
    id: '6',
    name: 'Usman Ali',
    email: '<EMAIL>',
    phone: '(*************',
    specialization: 'Therapeutic Riding',
    experience: 6,
    avatar: '/placeholder.svg?height=40&width=40',
    status: 'Active',
    availability: ['wednesday', 'friday', 'saturday'],
    assignedHorses: ['Gentle', 'Sunshine'],
    totalLessons: 98,
  },
];

const statusOptions = [
  { value: 'all', label: 'All Status' },
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'on-leave', label: 'On Leave' },
];

const specializationOptions = [
  { value: 'all', label: 'All Specializations' },
  { value: 'dressage', label: 'Dressage' },
  { value: 'show-jumping', label: 'Show Jumping' },
  { value: 'beginner-riding', label: 'Beginner Riding' },
  { value: 'cross-country', label: 'Cross Country' },
  { value: 'western-riding', label: 'Western Riding' },
  { value: 'therapeutic-riding', label: 'Therapeutic Riding' },
];

export function InstructorList() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [specializationFilter, setSpecializationFilter] = useState('all');

  const handleCreateInstructor = () => {
    void navigate({ to: '/users/create-instructor' });
  };

  const handleViewInstructor = (_instructorId: string) => {
    // navigate({ to: `/users/instructors/${instructorId}` });
  };

  const handleEditInstructor = (_instructorId: string) => {
    // navigate({ to: `/users/instructors/${instructorId}/edit` });
  };

  const handleDeleteInstructor = (_instructorId: string) => {
    // console.log('Delete instructor:', instructorId);
    // Handle delete logic here
  };

  // Filter instructors based on search and filters
  const filteredInstructors = instructorsData.filter((instructor) => {
    const matchesSearch =
      instructor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      instructor.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === 'all' ||
      instructor.status.toLowerCase().replace(' ', '-') === statusFilter;
    const matchesSpecialization =
      specializationFilter === 'all' ||
      instructor.specialization.toLowerCase().replace(' ', '-') ===
        specializationFilter;

    return matchesSearch && matchesStatus && matchesSpecialization;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Inactive':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'On Leave':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getSpecializationColor = (specialization: string) => {
    const colors = [
      'bg-blue-100 text-blue-800 border-blue-200',
      'bg-purple-100 text-purple-800 border-purple-200',
      'bg-pink-100 text-pink-800 border-pink-200',
      'bg-indigo-100 text-indigo-800 border-indigo-200',
      'bg-cyan-100 text-cyan-800 border-cyan-200',
      'bg-teal-100 text-teal-800 border-teal-200',
    ];
    const index = specialization.length % colors.length;
    return colors[index];
  };

  return (
    <div className='min-h-screen bg-gray-50 p-6'>
      <div className='mx-auto max-w-7xl space-y-6'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div>
            <h1 className='text-3xl font-bold text-gray-900'>Instructors</h1>
            <p className='mt-1 text-gray-600'>
              Manage your riding school instructors
            </p>
          </div>
          <Button
            onClick={handleCreateInstructor}
            className='bg-[#fdd36b] px-6 py-3 font-medium text-gray-900 hover:bg-[#fdd36b]/90'
          >
            <Plus className='mr-2 size-4' />
            Add New Instructor
          </Button>
        </div>

        {/* Search and Filters */}
        <InstructorFilters
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          statusOptions={statusOptions}
          specializationFilter={specializationFilter}
          setSpecializationFilter={setSpecializationFilter}
          specializationOptions={specializationOptions}
        />
        {/* Stats Cards */}
        <StatsCards instructorsData={instructorsData} />

        {/* Instructors Grid */}
        <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
          {filteredInstructors.map((instructor) => (
            <InstructorCard
              key={instructor.id}
              instructor={instructor}
              getStatusColor={getStatusColor}
              getSpecializationColor={getSpecializationColor}
              handleViewInstructor={handleViewInstructor}
              handleEditInstructor={handleEditInstructor}
              handleDeleteInstructor={handleDeleteInstructor}
            />
          ))}
        </div>

        {/* Empty State */}
        {filteredInstructors.length === 0 && (
          <Card className='border-gray-200 bg-white shadow-sm'>
            <CardContent className='p-12 text-center'>
              <div className='mx-auto mb-4 flex size-16 items-center justify-center rounded-full bg-gray-100'>
                <Search className='size-8 text-gray-400' />
              </div>
              <h3 className='mb-2 text-lg font-medium text-gray-900'>
                No instructors found
              </h3>
              <p className='mb-4 text-gray-500'>
                Try adjusting your search or filter criteria.
              </p>
              <Button
                onClick={handleCreateInstructor}
                className='bg-[#fdd36b] text-gray-900 hover:bg-[#fdd36b]/90'
              >
                <Plus className='mr-2 size-4' />
                Add First Instructor
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
