import { Mail, Phone, User } from 'lucide-react';
import { type Control, Controller, type FieldErrors } from 'react-hook-form';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { type InstructorFormData } from '@/components/users/instructors/create';

import { cn } from '@/lib/utils';

interface PersonalInformationProps {
  control: Control<InstructorFormData>;
  errors: FieldErrors<InstructorFormData>;
}

export const PersonalInformation: React.FC<PersonalInformationProps> = ({
  control,
  errors,
}) => {
  return (
    <Card className='border-gray-200 bg-white shadow-sm'>
      <CardHeader className='pb-4'>
        <CardTitle className='flex items-center gap-2 text-lg font-semibold text-gray-900'>
          <User className='h-5 w-5 text-blue-600' />
          Personal Information
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-6'>
        {/* Full Name */}
        <div>
          <Label
            htmlFor='fullName'
            className='mb-2 block text-sm font-medium text-gray-700'
          >
            Full Name *
          </Label>
          <Controller
            name='fullName'
            control={control}
            rules={{ required: 'Full name is required' }}
            render={({ field }) => (
              <div className='relative'>
                <User className='absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400' />
                <Input
                  {...field}
                  placeholder="Enter instructor's full name"
                  className={cn(
                    'h-12 border-gray-300 bg-white pl-10 focus:border-blue-500 focus:ring-blue-500',
                    errors.fullName &&
                      'border-red-300 focus:border-red-500 focus:ring-red-500',
                  )}
                />
              </div>
            )}
          />
          {errors.fullName?.message && (
            <p className='mt-1 text-sm text-red-600'>
              {errors.fullName.message}
            </p>
          )}
        </div>

        {/* Email */}
        <div>
          <Label
            htmlFor='email'
            className='mb-2 block text-sm font-medium text-gray-700'
          >
            Email Address *
          </Label>
          <Controller
            name='email'
            control={control}
            rules={{
              required: 'Email is required',
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: 'Invalid email address',
              },
            }}
            render={({ field }) => (
              <div className='relative'>
                <Mail className='absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400' />
                <Input
                  {...field}
                  type='email'
                  placeholder='<EMAIL>'
                  className={cn(
                    'h-12 border-gray-300 bg-white pl-10 focus:border-blue-500 focus:ring-blue-500',
                    errors.email &&
                      'border-red-300 focus:border-red-500 focus:ring-red-500',
                  )}
                />
              </div>
            )}
          />
          {errors.email?.message && (
            <p className='mt-1 text-sm text-red-600'>{errors.email.message}</p>
          )}
        </div>

        {/* Phone Number */}
        <div>
          <Label
            htmlFor='phoneNumber'
            className='mb-2 block text-sm font-medium text-gray-700'
          >
            Phone Number *
          </Label>
          <Controller
            name='phoneNumber'
            control={control}
            rules={{ required: 'Phone number is required' }}
            render={({ field }) => (
              <div className='relative'>
                <Phone className='absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400' />
                <Input
                  {...field}
                  type='tel'
                  placeholder='(*************'
                  className={cn(
                    'h-12 border-gray-300 bg-white pl-10 focus:border-blue-500 focus:ring-blue-500',
                    errors.phoneNumber &&
                      'border-red-300 focus:border-red-500 focus:ring-red-500',
                  )}
                />
              </div>
            )}
          />
          {errors.phoneNumber?.message && <p>{errors.phoneNumber.message}</p>}
        </div>
      </CardContent>
    </Card>
  );
};
