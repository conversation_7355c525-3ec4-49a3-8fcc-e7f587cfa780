import { Calendar } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';

import { cn } from '@/lib/utils';

interface WeekdayOption {
  value: string;
  label: string;
  fullLabel: string;
}

interface AvailabilityProps {
  weekdays: Array<WeekdayOption>;
  watchedAvailability: Array<string>;
  toggleAvailability: (dayValue: string) => void;
}

export const Availability: React.FC<AvailabilityProps> = ({
  weekdays,
  watchedAvailability,
  toggleAvailability,
}) => {
  return (
    <Card className='border-gray-200 bg-white shadow-sm'>
      <CardHeader className='pb-4'>
        <CardTitle className='flex items-center gap-2 text-lg font-semibold text-gray-900'>
          <Calendar className='size-5 text-purple-600' />
          Availability
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div>
          <Label className='mb-3 block text-sm font-medium text-gray-700'>
            Available Days *
          </Label>
          <div className='grid grid-cols-7 gap-2'>
            {weekdays.map((day) => (
              <Button
                key={day.value}
                type='button'
                variant={
                  watchedAvailability.includes(day.value)
                    ? 'default'
                    : 'outline'
                }
                onClick={() => {
                  toggleAvailability(day.value);
                }}
                className={cn(
                  'flex h-12 flex-col items-center justify-center text-xs font-medium transition-all',
                  watchedAvailability.includes(day.value)
                    ? 'border-purple-600 bg-purple-600 text-white hover:bg-purple-700'
                    : 'border-gray-300 bg-white text-gray-600 hover:bg-gray-50',
                )}
              >
                <span className='font-bold'>{day.label}</span>
                <span className='text-xs opacity-75'>
                  {day.fullLabel.slice(0, 3)}
                </span>
              </Button>
            ))}
          </div>
          <p className='mt-2 text-xs text-gray-500'>
            Select the days when this instructor is available to teach
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
