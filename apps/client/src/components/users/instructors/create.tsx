import { useNavigate } from '@tanstack/react-router';
import { Plus } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';

import { AssignedHorses } from './assignedHorses';
import { Availability } from './availability';
import { BioNotes } from './bioNotes';
import { PersonalInformation } from './personalInformation';
import { ProfessionalInformation } from './professionalInformation';

export interface InstructorFormData {
  fullName: string;
  email: string;
  phoneNumber: string;
  specialization: Array<string>;
  certifications: string;
  teachingPhilosophy: string;
  experience: number;
  availability: Array<string>;
  assignedHorses: Array<string>;
  bio: string;
}

const specializationOptions = [
  { value: 'beginner-riding', label: 'Beginner Riding' },
  { value: 'intermediate-riding', label: 'Intermediate Riding' },
  { value: 'advanced-riding', label: 'Advanced Riding' },
  { value: 'dressage', label: 'Dressage' },
  { value: 'show-jumping', label: 'Show Jumping' },
  { value: 'cross-country', label: 'Cross Country' },
  { value: 'western-riding', label: 'Western Riding' },
  { value: 'trail-riding', label: 'Trail Riding' },
  { value: 'therapeutic-riding', label: 'Therapeutic Riding' },
  { value: 'competition-training', label: 'Competition Training' },
];

const weekdays = [
  { value: 'monday', label: 'Mon', fullLabel: 'Monday' },
  { value: 'tuesday', label: 'Tue', fullLabel: 'Tuesday' },
  { value: 'wednesday', label: 'Wed', fullLabel: 'Wednesday' },
  { value: 'thursday', label: 'Thu', fullLabel: 'Thursday' },
  { value: 'friday', label: 'Fri', fullLabel: 'Friday' },
  { value: 'saturday', label: 'Sat', fullLabel: 'Saturday' },
  { value: 'sunday', label: 'Sun', fullLabel: 'Sunday' },
];

const horseOptions = [
  { value: 'thunder', label: 'Thunder', level: 'Advanced', age: 8 },
  { value: 'spirit', label: 'Spirit', level: 'Intermediate', age: 6 },
  { value: 'grace', label: 'Grace', level: 'Beginner', age: 12 },
  { value: 'hunter', label: 'Hunter', level: 'Advanced', age: 9 },
  { value: 'star', label: 'Star', level: 'Beginner', age: 10 },
  { value: 'lightning', label: 'Lightning', level: 'Advanced', age: 7 },
  { value: 'gentle', label: 'Gentle', level: 'Beginner', age: 15 },
  { value: 'brave', label: 'Brave', level: 'Intermediate', age: 8 },
  { value: 'midnight', label: 'Midnight', level: 'Advanced', age: 6 },
  { value: 'sunshine', label: 'Sunshine', level: 'Beginner', age: 11 },
  { value: 'storm', label: 'Storm', level: 'Advanced', age: 9 },
  { value: 'whisper', label: 'Whisper', level: 'Intermediate', age: 7 },
];

export const CreateInstructor = () => {
  const navigate = useNavigate();
  const [horseSearch, setHorseSearch] = useState('');
  const [showHorseDropdown, setShowHorseDropdown] = useState(false);

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<InstructorFormData>({
    defaultValues: {
      fullName: '',
      email: '',
      phoneNumber: '',
      specialization: [],
      certifications: '',
      teachingPhilosophy: '',
      experience: 0,
      availability: [],
      assignedHorses: [],
      bio: '',
    },
  });

  const watchedAvailability = watch('availability');
  const watchedAssignedHorses = watch('assignedHorses');

  const onSubmit = (_data: InstructorFormData) => {
    // Handle form submission here
    void navigate({ to: '/users/instructors' });
  };

  const toggleAvailability = (day: string) => {
    const currentAvailability = watchedAvailability;
    const updatedAvailability = currentAvailability.includes(day)
      ? currentAvailability.filter((d) => d !== day)
      : [...currentAvailability, day];
    setValue('availability', updatedAvailability);
  };

  const addHorse = (horseId: string) => {
    const currentHorses = watchedAssignedHorses;
    if (!currentHorses.includes(horseId)) {
      setValue('assignedHorses', [...currentHorses, horseId]);
    }
    setHorseSearch('');
    setShowHorseDropdown(false);
  };

  const removeHorse = (horseId: string) => {
    const currentHorses = watchedAssignedHorses;
    setValue(
      'assignedHorses',
      currentHorses.filter((id) => id !== horseId),
    );
  };

  const filteredHorses = horseOptions.filter(
    (horse) =>
      horse.label.toLowerCase().includes(horseSearch.toLowerCase()) &&
      !watchedAssignedHorses.includes(horse.value),
  );

  const getHorseLevelColor = (level: string) => {
    switch (level) {
      case 'Beginner':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Intermediate':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Advanced':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className='min-h-screen bg-gray-50 px-4 py-8'>
      <div className='mx-auto max-w-full px-2'>
        {/* Header */}
        <div className='mb-8 text-center'>
          <h1 className='mb-2 text-3xl font-bold text-gray-900'>
            Create New Instructor
          </h1>
          <p className='text-gray-600'>
            Add a new instructor to your riding school team
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className='space-y-8'>
          {/* Personal Information */}

          <PersonalInformation control={control} errors={errors} />
          {/* Professional Information */}
          <ProfessionalInformation
            control={control}
            errors={errors}
            specializationOptions={specializationOptions}
          />

          {/* Availability */}
          <Availability
            weekdays={weekdays}
            watchedAvailability={watchedAvailability}
            toggleAvailability={toggleAvailability}
          />

          {/* Assigned Horses */}

          <AssignedHorses
            watchedAssignedHorses={watchedAssignedHorses}
            horseOptions={horseOptions}
            filteredHorses={filteredHorses}
            horseSearch={horseSearch}
            showHorseDropdown={showHorseDropdown}
            setHorseSearch={setHorseSearch}
            setShowHorseDropdown={setShowHorseDropdown}
            addHorse={addHorse}
            removeHorse={removeHorse}
            getHorseLevelColor={getHorseLevelColor}
          />
          {/* Bio / Notes */}
          <BioNotes control={control} />

          {/* Form Actions */}
          <div className='flex gap-4 pt-6'>
            <Button
              type='button'
              variant='outline'
              className='h-12 flex-1 border-gray-300 text-gray-700 hover:bg-gray-50'
              onClick={() => {
                void navigate({ to: '/users/instructors' });
              }}
            >
              Cancel
            </Button>
            <Button
              type='submit'
              className='h-12 flex-1 bg-[#fdd36b] font-medium text-gray-900 hover:bg-[#fdd36b]/90'
            >
              <Plus className='mr-2 h-4 w-4' />
              Create Instructor
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
