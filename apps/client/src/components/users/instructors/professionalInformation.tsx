import { Award, X } from 'lucide-react';
import { type Control, Controller, type FieldErrors } from 'react-hook-form';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { type InstructorFormData } from '@/components/users/instructors/create';

import { cn } from '@/lib/utils';

interface SpecializationOption {
  value: string;
  label: string;
}

interface ProfessionalInformationProps {
  control: Control<InstructorFormData>;
  errors: FieldErrors<InstructorFormData>;
  specializationOptions: Array<SpecializationOption>;
}

export const ProfessionalInformation: React.FC<
  ProfessionalInformationProps
> = ({ control, errors, specializationOptions }) => {
  return (
    <Card className='border-gray-200 bg-white shadow-sm'>
      <CardHeader className='pb-4'>
        <CardTitle className='flex items-center gap-2 text-lg font-semibold text-gray-900'>
          <Award className='size-5 text-green-600' />
          Professional Information
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-6'>
        {/* Specializations */}
        <div>
          <Label
            htmlFor='specialization'
            className='mb-2 block text-sm font-medium text-gray-700'
          >
            Specializations *
          </Label>
          <Controller
            name='specialization'
            control={control}
            rules={{
              required: 'At least one specialization is required',
              validate: (value) =>
                value.length > 0 || 'Please select at least one specialization',
            }}
            render={({ field }) => (
              <div className='space-y-3'>
                <div className='flex min-h-[40px] flex-wrap gap-2 rounded-lg border border-gray-300 bg-gray-50 p-3'>
                  {field.value.length === 0 ? (
                    <span className='text-sm text-gray-500'>
                      No specializations selected
                    </span>
                  ) : (
                    field.value.map((spec: string) => {
                      const specOption = specializationOptions.find(
                        (opt) => opt.value === spec,
                      );
                      return (
                        <Badge
                          key={spec}
                          variant='secondary'
                          className='flex items-center gap-2 rounded-full border border-blue-200 bg-blue-100 px-3 py-1 text-blue-800'
                        >
                          <span className='font-medium'>
                            {specOption?.label}
                          </span>
                          <X
                            className='size-3 cursor-pointer transition-colors hover:text-red-600'
                            onClick={() => {
                              const updated = field.value.filter(
                                (s: string) => s !== spec,
                              );
                              field.onChange(updated);
                            }}
                          />
                        </Badge>
                      );
                    })
                  )}
                </div>

                <Select
                  value=''
                  onValueChange={(value) => {
                    if (value && !field.value.includes(value)) {
                      field.onChange([...field.value, value]);
                    }
                  }}
                >
                  <SelectTrigger
                    className={cn(
                      'h-12 border-gray-300 bg-white focus:border-blue-500 focus:ring-blue-500',
                      errors.specialization &&
                        'border-red-300 focus:border-red-500 focus:ring-red-500',
                    )}
                  >
                    <SelectValue placeholder='Add specialization...' />
                  </SelectTrigger>
                  <SelectContent>
                    {specializationOptions
                      .filter((option) => !field.value.includes(option.value))
                      .map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          />
          {errors.specialization && (
            <p className='mt-1 text-sm text-red-600'>
              {errors.specialization.message}
            </p>
          )}
        </div>

        {/* Certifications */}
        <div>
          <Label
            htmlFor='certifications'
            className='mb-2 block text-sm font-medium text-gray-700'
          >
            Certifications & Qualifications
          </Label>
          <Controller
            name='certifications'
            control={control}
            render={({ field }) => (
              <Textarea
                {...field}
                placeholder='List any relevant certifications, qualifications, or training programs completed...'
                className='min-h-[80px] resize-none border-gray-300 bg-white focus:border-blue-500 focus:ring-blue-500'
              />
            )}
          />
          <p className='mt-2 text-xs text-gray-500'>
            Include riding instructor certifications, first aid training, etc.
          </p>
        </div>

        {/* Teaching Philosophy */}
        <div>
          <Label
            htmlFor='teachingPhilosophy'
            className='mb-2 block text-sm font-medium text-gray-700'
          >
            Teaching Philosophy
          </Label>
          <Controller
            name='teachingPhilosophy'
            control={control}
            render={({ field }) => (
              <Textarea
                {...field}
                placeholder='Describe your approach to teaching and working with students...'
                className='min-h-[80px] resize-none border-gray-300 bg-white focus:border-blue-500 focus:ring-blue-500'
              />
            )}
          />
          <p className='mt-2 text-xs text-gray-500'>
            Share your teaching style, methods, and philosophy
          </p>
        </div>

        {/* Years of Experience */}
        <div>
          <Label
            htmlFor='experience'
            className='mb-2 block text-sm font-medium text-gray-700'
          >
            Years of Experience *
          </Label>
          <Controller
            name='experience'
            control={control}
            rules={{
              required: 'Experience is required',
              min: { value: 0, message: 'Experience cannot be negative' },
              max: {
                value: 50,
                message: 'Please enter a valid number of years',
              },
            }}
            render={({ field }) => (
              <div className='relative'>
                <Award className='absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400' />
                <Input
                  {...field}
                  type='number'
                  min='0'
                  max='50'
                  placeholder='5'
                  onChange={(e) => {
                    const val = Number.parseInt(e.target.value) || 0;
                    field.onChange(val);
                  }}
                  className={cn(
                    'h-12 border-gray-300 bg-white pl-10 focus:border-blue-500 focus:ring-blue-500',
                    errors.experience &&
                      'border-red-300 focus:border-red-500 focus:ring-red-500',
                  )}
                />
              </div>
            )}
          />
          {errors.experience && (
            <p className='mt-1 text-sm text-red-600'>
              {errors.experience.message}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
