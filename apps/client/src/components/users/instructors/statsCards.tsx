// components/DashboardStats.tsx
import { Card, CardContent } from '@/components/ui/card';

import { type Instructor } from '.';

interface StatsCardsProps {
  instructorsData: Array<Instructor>;
}

export const StatsCards: React.FC<StatsCardsProps> = ({ instructorsData }) => {
  const totalInstructors = instructorsData.length;

  const activeInstructors = instructorsData.filter(
    (i) => i.status === 'Active',
  ).length;

  const totalLessons = instructorsData.reduce(
    (sum, i) => sum + i.totalLessons,
    0,
  );

  const stats = [
    { value: totalInstructors, label: 'Total Instructors' },
    {
      value: activeInstructors,
      label: 'Active Instructors',
      colorClass: 'text-green-600',
    },
    {
      value: totalLessons,
      label: 'Total Lessons Taught',
      colorClass: 'text-blue-600',
    },
  ];

  return (
    <div className='grid grid-cols-1 gap-6 md:grid-cols-3'>
      {stats.map((stat, index) => (
        <Card key={index} className='border-gray-200 bg-white shadow-sm'>
          <CardContent className='p-6'>
            <div
              className={`text-2xl font-bold ${stat.colorClass ?? 'text-gray-900'}`}
            >
              {stat.value}
            </div>
            <p className='text-sm text-gray-600'>{stat.label}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
