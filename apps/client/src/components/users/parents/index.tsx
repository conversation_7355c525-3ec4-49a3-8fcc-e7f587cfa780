import { useNavigate } from '@tanstack/react-router';
import { Plus, Search } from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import { useParents } from '@/hooks/useParents';

import { ParentDetails } from './details';
import { TableParents } from './tableParents';

interface Parent {
  id: string;
  name: string;
  email: string;
  phone: string;
  children: Array<string>;
  emergencyContact: string;
  emergencyPhone: string;
  address: string;
  joinDate: string;
  status: 'active' | 'inactive';
  avatar?: string;
}

// Helper function to transform API parent data to match the expected interface
const transformApiParent = (apiParent: {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string | null;
  emergency_contact_name: string | null;
  emergency_contact_phone: string | null;
  address: string | null;
  created_at: string;
}): Parent => {
  return {
    id: apiParent.id,
    name: `${apiParent.first_name} ${apiParent.last_name}`,
    email: apiParent.email,
    phone: apiParent.phone ?? '',
    children: [], // This would need to come from a separate API call
    emergencyContact: apiParent.emergency_contact_name ?? '',
    emergencyPhone: apiParent.emergency_contact_phone ?? '',
    address: apiParent.address ?? '',
    joinDate: new Date(apiParent.created_at).toISOString().split('T')[0],
    status: 'active', // Default to active, could be determined by other logic
  };
};

export const ParentList = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedParent, setSelectedParent] = useState<Parent | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, _setPageSize] = useState(10);

  // Use the useParents hook to fetch data
  const {
    data: parentsData,
    isLoading,
    error,
  } = useParents({
    page: currentPage,
    limit: pageSize,
  });

  // Transform API data to match expected interface
  const allParents = parentsData?.parents.map(transformApiParent) ?? [];

  const filteredParents = allParents.filter(
    (parent) =>
      parent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      parent.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      parent.children.some((child) =>
        child.toLowerCase().includes(searchTerm.toLowerCase()),
      ),
  );

  const handleViewDetails = (parent: Parent) => {
    setSelectedParent(parent);
    setIsDetailsModalOpen(true);
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className='min-h-screen space-y-6 bg-gray-50 p-6'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div>
            <h1 className='text-2xl font-bold text-gray-900'>
              Account Holder Management
            </h1>
            <p className='text-gray-600'>
              Manage account holder information and family details
            </p>
          </div>
          <Button
            className='bg-[#282828] text-white hover:bg-[#282828]/90'
            onClick={async () => {
              await navigate({
                to: '/users/create-parents',
              });
            }}
          >
            <Plus className='mr-2 size-4' />
            Add Account Holder
          </Button>
        </div>

        {/* Search and Filters */}
        <div className='flex items-center gap-4'>
          <div className='relative max-w-sm flex-1'>
            <Search className='absolute top-1/2 left-3 size-4 -translate-y-1/2 transform text-gray-400' />
            <Input
              placeholder='Search parents or children...'
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
              }}
              className='pl-10'
              disabled
            />
          </div>
          <div className='flex items-center gap-2'>
            <Badge variant='secondary' className='bg-gray-100 text-gray-800'>
              Loading...
            </Badge>
          </div>
        </div>

        {/* Loading Table */}
        <div className='rounded-lg border bg-white p-8'>
          <div className='flex items-center justify-center'>
            <div className='text-lg'>Loading parents...</div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className='min-h-screen space-y-6 bg-gray-50 p-6'>
        <div className='flex items-center justify-center'>
          <div className='text-lg text-red-600'>
            Error loading parents: {error.message}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen space-y-6 bg-gray-50 p-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>
            Account Holder Management
          </h1>
          <p className='text-gray-600'>
            Manage account holder information and family details
          </p>
        </div>
        <Button
          className='bg-[#282828] text-white hover:bg-[#282828]/90'
          onClick={async () => {
            await navigate({
              to: '/users/create-parents',
            });
          }}
        >
          <Plus className='mr-2 size-4' />
          Add Account Holder
        </Button>
      </div>

      {/* Search and Filters */}
      <div className='flex items-center gap-4'>
        <div className='relative max-w-sm flex-1'>
          <Search className='absolute top-1/2 left-3 size-4 -translate-y-1/2 transform text-gray-400' />
          <Input
            placeholder='Search parents or children...'
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
            }}
            className='pl-10'
          />
        </div>
        <div className='flex items-center gap-2'>
          <Badge variant='secondary' className='bg-green-100 text-green-800'>
            {filteredParents.filter((p) => p.status === 'active').length} Active
          </Badge>
          <Badge variant='secondary' className='bg-gray-100 text-gray-800'>
            {filteredParents.filter((p) => p.status === 'inactive').length}{' '}
            Inactive
          </Badge>
          <Badge variant='secondary' className='bg-blue-100 text-blue-800'>
            Total: {parentsData?.pagination.total ?? 0}
          </Badge>
        </div>
      </div>

      {/* Parents Table */}
      <div className='rounded-lg border bg-white'>
        <TableParents
          parents={filteredParents}
          getInitials={getInitials}
          handleViewDetails={handleViewDetails}
        />
      </div>

      {/* Parent Details Modal */}
      {selectedParent && (
        <ParentDetails
          parent={selectedParent}
          isOpen={isDetailsModalOpen}
          onClose={() => {
            setIsDetailsModalOpen(false);
          }}
        />
      )}

      {/* Pagination */}
      {parentsData && parentsData.pagination.totalPages > 1 && (
        <div className='flex items-center justify-between rounded-lg border bg-white p-4'>
          <div className='flex items-center gap-2'>
            <span className='text-sm text-gray-600'>
              Showing {(currentPage - 1) * pageSize + 1} to{' '}
              {Math.min(currentPage * pageSize, parentsData.pagination.total)}{' '}
              of {parentsData.pagination.total} results
            </span>
          </div>
          <div className='flex items-center gap-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => {
                setCurrentPage(currentPage - 1);
              }}
              disabled={!parentsData.pagination.hasPrevPage}
            >
              Previous
            </Button>
            <span className='text-sm text-gray-600'>
              Page {currentPage} of {parentsData.pagination.totalPages}
            </span>
            <Button
              variant='outline'
              size='sm'
              onClick={() => {
                setCurrentPage(currentPage + 1);
              }}
              disabled={!parentsData.pagination.hasNextPage}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
