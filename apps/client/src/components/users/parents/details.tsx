import {
  AlertTriangle,
  Calendar,
  Mail,
  MapPin,
  Phone,
  Users,
} from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';

interface Parent {
  id: string;
  name: string;
  email: string;
  phone: string;
  children: Array<string>;
  emergencyContact: string;
  emergencyPhone: string;
  address: string;
  joinDate: string;
  status: 'active' | 'inactive';
  avatar?: string;
}

interface ParentDetailsProps {
  parent: Parent;
  isOpen: boolean;
  onClose: () => void;
}

export const ParentDetails = ({
  parent,
  isOpen,
  onClose,
}: ParentDetailsProps) => {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-h-[90vh] w-full overflow-x-hidden overflow-y-auto sm:max-w-3xl'>
        <DialogHeader>
          <div className='flex items-center justify-between'>
            <DialogTitle className='text-xl font-semibold'>
              Parent Details
            </DialogTitle>
          </div>
        </DialogHeader>

        <div className='space-y-6'>
          {/* Parent Profile */}
          <div className='flex items-start gap-4'>
            <Avatar className='size-16'>
              <AvatarImage src={parent.avatar ?? '/placeholder.svg'} />
              <AvatarFallback className='bg-[#fdd36b] text-lg font-semibold text-[#282828]'>
                {getInitials(parent.name)}
              </AvatarFallback>
            </Avatar>
            <div className='flex-1'>
              <div className='mb-2 flex items-center gap-3'>
                <h3 className='text-lg font-semibold text-gray-900'>
                  {parent.name}
                </h3>
                <Badge
                  variant={parent.status === 'active' ? 'default' : 'secondary'}
                  className={
                    parent.status === 'active'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }
                >
                  {parent.status}
                </Badge>
              </div>
              <div className='space-y-2'>
                <div className='flex items-center gap-2 text-sm text-gray-600'>
                  <Mail className='size-4' />
                  {parent.email}
                </div>
                <div className='flex items-center gap-2 text-sm text-gray-600'>
                  <Phone className='size-4' />
                  {parent.phone}
                </div>
                <div className='flex items-center gap-2 text-sm text-gray-600'>
                  <MapPin className='size-4' />
                  {parent.address}
                </div>
                <div className='flex items-center gap-2 text-sm text-gray-600'>
                  <Calendar className='size-4' />
                  Joined {new Date(parent.joinDate).toLocaleDateString()}
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Children Information */}
          <div>
            <h4 className='mb-3 flex items-center gap-2 text-lg font-semibold text-gray-900'>
              <Users className='size-5' />
              Children ({parent.children.length})
            </h4>
            <div className='grid gap-3'>
              {parent.children.map((child, index) => (
                <div
                  key={index}
                  className='flex items-center justify-between rounded-lg bg-gray-50 p-3'
                >
                  <div className='flex items-center gap-3'>
                    <Avatar className='size-8'>
                      <AvatarFallback className='bg-blue-100 text-xs text-blue-800'>
                        {getInitials(child)}
                      </AvatarFallback>
                    </Avatar>
                    <span className='font-medium text-gray-900'>{child}</span>
                  </div>
                  <div className='flex gap-2'>
                    <Button variant='outline' size='sm'>
                      View Profile
                    </Button>
                    <Button variant='outline' size='sm'>
                      Lessons
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Emergency Contact */}
          <div>
            <h4 className='mb-3 flex items-center gap-2 text-lg font-semibold text-gray-900'>
              <AlertTriangle className='size-5' />
              Emergency Contact
            </h4>
            <div className='rounded-lg border border-red-200 bg-red-50 p-4'>
              <div className='space-y-2'>
                <div className='font-medium text-gray-900'>
                  {parent.emergencyContact}
                </div>
                <div className='flex items-center gap-2 text-sm text-gray-600'>
                  <Phone className='size-4' />
                  {parent.emergencyPhone}
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Recent Activity */}
          <div>
            <h4 className='mb-3 text-lg font-semibold text-gray-900'>
              Recent Activity
            </h4>
            <div className='space-y-3'>
              <div className='flex items-center justify-between rounded-lg bg-gray-50 p-3'>
                <div>
                  <div className='font-medium text-gray-900'>
                    Payment Received
                  </div>
                  <div className='text-sm text-gray-600'>
                    Monthly lesson fees - $240
                  </div>
                </div>
                <div className='text-sm text-gray-500'>2 days ago</div>
              </div>
              <div className='flex items-center justify-between rounded-lg bg-gray-50 p-3'>
                <div>
                  <div className='font-medium text-gray-900'>
                    Lesson Scheduled
                  </div>
                  <div className='text-sm text-gray-600'>
                    Emma Johnson - Advanced Jumping
                  </div>
                </div>
                <div className='text-sm text-gray-500'>1 week ago</div>
              </div>
              <div className='flex items-center justify-between rounded-lg bg-gray-50 p-3'>
                <div>
                  <div className='font-medium text-gray-900'>
                    Profile Updated
                  </div>
                  <div className='text-sm text-gray-600'>
                    Emergency contact information
                  </div>
                </div>
                <div className='text-sm text-gray-500'>2 weeks ago</div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className='flex gap-3 pt-4'>
            <Button className='bg-[#282828] text-white hover:bg-[#282828]/90'>
              Edit Parent
            </Button>
            <Button variant='outline'>Send Message</Button>
            <Button variant='outline'>View Billing</Button>
            <Button
              variant='outline'
              className='border-red-200 text-red-600 hover:bg-red-50'
            >
              Deactivate
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
