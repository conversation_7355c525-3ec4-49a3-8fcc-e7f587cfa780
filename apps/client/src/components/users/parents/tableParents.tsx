import { Mail, MoreHorizontal, Phone, User } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

// TableParents.types.ts

interface Parent {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  children: Array<string>;
  emergencyContact: string;
  emergencyPhone: string;
  joinDate: string;
  status: 'active' | 'inactive';
  address: string;
}

interface TableParentsProps {
  parents: Array<Parent>;
  getInitials: (name: string) => string;
  handleViewDetails: (parent: Parent) => void;
}

export const TableParents: React.FC<TableParentsProps> = ({
  parents,
  getInitials,
  handleViewDetails,
}) => {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Account Holder</TableHead>
          <TableHead>Contact</TableHead>
          <TableHead>Children</TableHead>
          <TableHead>Emergency Contact</TableHead>
          <TableHead>Join Date</TableHead>
          <TableHead>Status</TableHead>
          <TableHead className='w-[50px]'></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {parents.map((parent) => (
          <TableRow key={parent.id} className='hover:bg-gray-50'>
            <TableCell>
              <div className='flex items-center gap-3'>
                <Avatar className='h-10 w-10'>
                  <AvatarImage
                    src={parent.avatar ?? '/placeholder.svg'}
                    alt={parent.name}
                  />
                  <AvatarFallback className='bg-[#fdd36b] font-semibold text-[#282828]'>
                    {getInitials(parent.name)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className='font-medium text-gray-900'>{parent.name}</div>
                  <div className='text-sm text-gray-500'>{parent.email}</div>
                </div>
              </div>
            </TableCell>

            <TableCell>
              <div className='space-y-1'>
                <div className='flex items-center gap-2 text-sm'>
                  <Phone className='size-3 text-gray-400' />
                  {parent.phone}
                </div>
                <div className='flex items-center gap-2 text-sm'>
                  <Mail className='size-3 text-gray-400' />
                  {parent.email}
                </div>
              </div>
            </TableCell>

            <TableCell>
              <div className='space-y-1'>
                {parent.children.map((child, index) => (
                  <div key={index} className='flex items-center gap-2 text-sm'>
                    <User className='size-3 text-gray-400' />
                    {child}
                  </div>
                ))}
              </div>
            </TableCell>

            <TableCell>
              <div className='space-y-1'>
                <div className='text-sm font-medium'>
                  {parent.emergencyContact}
                </div>
                <div className='text-sm text-gray-500'>
                  {parent.emergencyPhone}
                </div>
              </div>
            </TableCell>

            <TableCell>
              <div className='text-sm'>
                {new Date(parent.joinDate).toLocaleDateString()}
              </div>
            </TableCell>

            <TableCell>
              <Badge
                variant={parent.status === 'active' ? 'default' : 'secondary'}
                className={
                  parent.status === 'active'
                    ? 'bg-green-100 text-green-800 hover:bg-green-100'
                    : 'bg-gray-100 text-gray-800 hover:bg-gray-100'
                }
              >
                {parent.status}
              </Badge>
            </TableCell>

            <TableCell>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant='ghost' size='sm'>
                    <MoreHorizontal className='size-4' />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align='end'>
                  <DropdownMenuItem
                    onClick={() => {
                      handleViewDetails(parent);
                    }}
                  >
                    View Details
                  </DropdownMenuItem>
                  <DropdownMenuItem>Edit Parent</DropdownMenuItem>
                  <DropdownMenuItem>View Children</DropdownMenuItem>
                  <DropdownMenuItem>Send Message</DropdownMenuItem>
                  <DropdownMenuItem className='text-red-600'>
                    Deactivate
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};
