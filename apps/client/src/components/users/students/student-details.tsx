import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { StudentDocuments } from '@/components/users/students/student-documents';

import { HorseAssignment } from './horseAssigment';
import { LessonTab } from './lessonTab';
import { PaymentsTab } from './PaymentsTab';
import { ProfileTab } from './profileTab';

// import type React from 'react';
// import { useState } from 'react';

interface Student {
  id: string;
  name: string;
  contact: string;
  location: string;
  email: string;
  age: number;
  level: string;
  joinDate: string;
  avatar: string;
  lessons: Array<string>;
  instructors: Array<{ id: string; name: string; avatar: string }>;
  assignedHorse: string;
  emergencyContact: string;
  paymentStatus: string;
  upcomingLessons: number;
  completedLessons: number;
}

const upcomingLessons = [
  {
    date: 'Dec 20, 2024',
    time: '2:00 PM',
    type: 'Dressage',
    instructor: '<PERSON>',
  },
  {
    date: 'Dec 22, 2024',
    time: '10:00 AM',
    type: 'Show Jumping',
    instructor: 'Michael',
  },
  {
    date: 'Dec 25, 2024',
    time: '3:00 PM',
    type: 'Dressage',
    instructor: 'Sarah',
  },
];

const lessonHistory = [
  {
    date: 'Dec 15, 2024',
    type: 'Dressage',
    status: 'Completed',
    rating: 'Excellent',
  },
  {
    date: 'Dec 13, 2024',
    type: 'Show Jumping',
    status: 'Completed',
    rating: 'Good',
  },
  {
    date: 'Dec 10, 2024',
    type: 'Dressage',
    status: 'Completed',
    rating: 'Excellent',
  },
];

const summary = {
  totalPaid: '$1,240',
  thisMonth: '$320',
  outstanding: '$0',
};

const history = [
  {
    date: 'Dec 1, 2024',
    amount: '$320',
    method: 'Credit Card',
    status: 'Paid',
  },
  {
    date: 'Nov 1, 2024',
    amount: '$320',
    method: 'Bank Transfer',
    status: 'Paid',
  },
  {
    date: 'Oct 1, 2024',
    amount: '$320',
    method: 'Credit Card',
    status: 'Paid',
  },
  {
    date: 'Sep 1, 2024',
    amount: '$280',
    method: 'Cash',
    status: 'Paid',
  },
];

const current = {
  avatarSrc: '/placeholder.svg',
  name: 'Thunder',
  breedInfo: 'Arabian • Age 12 • Advanced Level',
  assignedSince: 'March 15, 2023',
  status: 'Active',
};

const horseHistory = [
  {
    horse: 'Thunder',
    period: 'Mar 2023 - Present',
    lessons: 24,
    status: 'Active',
  },
  {
    horse: 'Spirit',
    period: 'Jan 2023 - Mar 2023',
    lessons: 8,
    status: 'Completed',
  },
  {
    horse: 'Gentle',
    period: 'Nov 2022 - Jan 2023',
    lessons: 6,
    status: 'Completed',
  },
];
interface StudentDetailsModalProps {
  student: Student;
  isOpen: boolean;
  onClose: () => void;
}

export const StudentDetails = ({
  student,
  isOpen,
  onClose,
}: StudentDetailsModalProps) => {
  // const [isEditing, setIsEditing] = useState(false);
  // const [uploadedFiles, setUploadedFiles] = useState<Array<File>>([]);

  if (!isOpen) return null;

  // const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
  //   const files = Array.from(event.target.files ?? []);
  //   setUploadedFiles((prev) => [...prev, ...files]);
  // };

  // const removeFile = (index: number) => {
  //   setUploadedFiles((prev) => prev.filter((_, i) => i !== index));
  // };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-h-[90vh] w-full overflow-y-auto sm:max-w-3xl'>
        {/* Header */}
        <DialogHeader className='flex flex-row items-center justify-between border-b pb-2'>
          <div className='flex items-center gap-4'>
            <Avatar className='size-16 border-2 border-gray-200'>
              <AvatarImage
                src={student.avatar || '/placeholder.svg'}
                alt={student.name}
              />
              <AvatarFallback className='bg-[#fdd36b] text-lg font-bold text-gray-800'>
                {student.name
                  .split(' ')
                  .map((n) => n[0])
                  .join('')}
              </AvatarFallback>
            </Avatar>
            <div>
              {/* <h2 className='text-2xl font-bold text-gray-900'>
                {student.name}
              </h2> */}
              <DialogTitle className='text-2xl font-bold text-gray-900'>
                {student.name}
              </DialogTitle>
              <p className='text-gray-600'>
                Age {student.age} • {student.level} Level
              </p>
              <Badge
                className={`mt-1 ${
                  student.paymentStatus === 'Paid'
                    ? 'border-green-200 bg-green-100 text-green-800'
                    : 'border-red-200 bg-red-100 text-red-800'
                }`}
              >
                {student.paymentStatus}
              </Badge>
            </div>
          </div>
          <div className='mt-auto'>
            <Badge variant='secondary' className='bg-white text-blue-800'>
              Sarah Johnson
            </Badge>
          </div>
        </DialogHeader>

        {/* Tags */}
        <div className='border-b bg-gray-50 px-6 py-4'>
          <div className='flex flex-wrap gap-2'>
            {student.lessons.map((lesson, index) => (
              <Badge
                key={index}
                variant='secondary'
                className='bg-blue-100 text-blue-800'
              >
                {lesson}
              </Badge>
            ))}
            <Badge
              variant='secondary'
              className='bg-purple-100 text-purple-800'
            >
              {student.level}
            </Badge>
            <Badge variant='secondary' className='bg-green-100 text-green-800'>
              Active Student
            </Badge>
          </div>
        </div>

        {/* Tabs */}
        <div className='flex-1 overflow-hidden'>
          <Tabs defaultValue='profile' className='flex h-full flex-col'>
            <TabsList className='grid w-full grid-cols-5 rounded-lg border-b'>
              <TabsTrigger
                className='data-[state=active]:bg-accent data-[sate=active]:border-b-4 data-[state=active]:rounded-none data-[state=active]:border-b-amber-500 data-[state=active]:text-amber-500'
                value='profile'
              >
                Profile
              </TabsTrigger>
              <TabsTrigger
                className='data-[state=active]:bg-accent data-[sate=active]:border-b-4 data-[state=active]:rounded-none data-[state=active]:border-b-amber-500 data-[state=active]:text-amber-500'
                value='lessons'
              >
                Lessons
              </TabsTrigger>
              <TabsTrigger
                className='data-[state=active]:bg-accent data-[sate=active]:border-b-4 data-[state=active]:rounded-none data-[state=active]:border-b-amber-500 data-[state=active]:text-amber-500'
                value='payments'
              >
                Payments
              </TabsTrigger>
              <TabsTrigger
                className='data-[state=active]:bg-accent data-[sate=active]:border-b-4 data-[state=active]:rounded-none data-[state=active]:border-b-amber-500 data-[state=active]:text-amber-500'
                value='horses'
              >
                Horse Assignments
              </TabsTrigger>
              <TabsTrigger
                className='data-[state=active]:bg-accent data-[sate=active]:border-b-4 data-[state=active]:rounded-none data-[state=active]:border-b-amber-500 data-[state=active]:text-amber-500'
                value='student-docs'
              >
                Student Docs
              </TabsTrigger>
            </TabsList>

            <div className='flex-1 overflow-y-auto'>
              {/* Profile Tab */}
              <TabsContent value='profile' className='space-y-6 p-6'>
                <ProfileTab student={student} />
              </TabsContent>

              {/* Lessons Tab */}
              <TabsContent value='lessons' className='space-y-6 p-6'>
                <LessonTab
                  upcomingLessons={upcomingLessons}
                  lessonHistory={lessonHistory}
                />
              </TabsContent>

              {/* Payments Tab */}
              <TabsContent value='payments' className='space-y-6 p-6'>
                <PaymentsTab summary={summary} history={history} />
              </TabsContent>

              {/* Horse Assignments Tab */}
              <TabsContent value='horses' className='space-y-6 p-6'>
                <HorseAssignment current={current} history={horseHistory} />
              </TabsContent>

              {/* Student Docs Tab */}
              <TabsContent value='student-docs' className='space-y-6 p-6'>
                <StudentDocuments />
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
};
