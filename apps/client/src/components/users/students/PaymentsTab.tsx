import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';

interface PaymentSummary {
  totalPaid: string;
  thisMonth: string;
  outstanding: string;
}

interface PaymentHistoryItem {
  date: string;
  amount: string;
  method: string;
  status: string;
}

interface PaymentsTabProps {
  summary: PaymentSummary;
  history: Array<PaymentHistoryItem>;
}

export const PaymentsTab: React.FC<PaymentsTabProps> = ({
  summary,
  history,
}) => {
  return (
    <div className='space-y-6'>
      {/* Summary Cards */}
      <div className='grid grid-cols-1 gap-4 md:grid-cols-3'>
        <Card>
          <CardContent className='p-4 text-center'>
            <p className='text-2xl font-bold text-green-600'>
              {summary.totalPaid}
            </p>
            <p className='text-sm text-gray-600'>Total Paid</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4 text-center'>
            <p className='text-2xl font-bold text-blue-600'>
              {summary.thisMonth}
            </p>
            <p className='text-sm text-gray-600'>This Month</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4 text-center'>
            <p className='text-2xl font-bold text-orange-600'>
              {summary.outstanding}
            </p>
            <p className='text-sm text-gray-600'>Outstanding</p>
          </CardContent>
        </Card>
      </div>

      {/* Payment History */}
      <Card>
        <CardHeader>
          <CardTitle>Payment History</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-3'>
            {history.map((payment, index) => (
              <div
                key={index}
                className='flex items-center justify-between rounded-lg border p-3'
              >
                <div>
                  <p className='font-medium'>{payment.amount}</p>
                  <p className='text-sm text-gray-600'>
                    {payment.date} • {payment.method}
                  </p>
                </div>
                <Badge className='bg-green-100 text-green-800'>
                  {payment.status}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
