'use client';

import type { CreateStudentFormData } from '@/schemas/createStudentSchema';
import type { UseFormReturn } from 'react-hook-form';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

export interface StudentInformationProps {
  form: UseFormReturn<CreateStudentFormData>;
}

export const StudentInformation = ({ form }: StudentInformationProps) => {
  const {
    register,
    formState: { errors },
    setValue,
    watch,
  } = form;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Student Information</CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
          <div>
            <Label htmlFor='firstName'>First Name *</Label>
            <Input
              id='firstName'
              {...register('firstName')}
              className={errors.firstName ? 'border-red-500' : ''}
            />
            {errors.firstName && (
              <p className='mt-1 text-sm text-red-500'>
                {errors.firstName.message}
              </p>
            )}
          </div>
          <div>
            <Label htmlFor='lastName'>Last Name *</Label>
            <Input
              id='lastName'
              {...register('lastName')}
              className={errors.lastName ? 'border-red-500' : ''}
            />
            {errors.lastName && (
              <p className='mt-1 text-sm text-red-500'>
                {errors.lastName.message}
              </p>
            )}
          </div>
        </div>

        <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
          <div>
            <Label htmlFor='email'>Email Address</Label>
            <Input
              id='email'
              type='email'
              {...register('email')}
              className={errors.email ? 'border-red-500' : ''}
            />
            {errors.email && (
              <p className='mt-1 text-sm text-red-500'>
                {errors.email.message}
              </p>
            )}
          </div>
          <div>
            <Label htmlFor='phone'>Phone Number</Label>
            <Input
              id='phone'
              type='tel'
              {...register('phone')}
              className={errors.phone ? 'border-red-500' : ''}
            />
            {errors.phone && (
              <p className='mt-1 text-sm text-red-500'>
                {errors.phone.message}
              </p>
            )}
          </div>
        </div>

        <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
          <div>
            <Label htmlFor='dateOfBirth'>Date of Birth *</Label>
            <Input
              id='dateOfBirth'
              type='date'
              {...register('dateOfBirth')}
              className={errors.dateOfBirth ? 'border-red-500' : ''}
            />
            {errors.dateOfBirth && (
              <p className='mt-1 text-sm text-red-500'>
                {errors.dateOfBirth.message}
              </p>
            )}
          </div>
          <div>
            <Label htmlFor='gender'>Gender *</Label>
            <Select
              value={watch('gender')}
              onValueChange={(value) => {
                setValue('gender', value as 'male' | 'female' | 'other');
              }}
            >
              <SelectTrigger className={errors.gender ? 'border-red-500' : ''}>
                <SelectValue placeholder='Select gender' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='male'>Male</SelectItem>
                <SelectItem value='female'>Female</SelectItem>
                <SelectItem value='other'>Other</SelectItem>
              </SelectContent>
            </Select>
            {errors.gender && (
              <p className='mt-1 text-sm text-red-500'>
                {errors.gender.message}
              </p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
