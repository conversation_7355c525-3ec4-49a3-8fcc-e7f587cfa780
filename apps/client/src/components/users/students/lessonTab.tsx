'use client';

import { Check, ChevronDown } from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
} from '@/components/ui/command';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

import { cn } from '@/lib/utils';

interface UpcomingLesson {
  date: string;
  time: string;
  type: string;
  instructor: string;
}

interface LessonHistory {
  date: string;
  type: string;
  status: string;
  rating: string;
}

interface LessonTabProps {
  upcomingLessons: Array<UpcomingLesson>;
  lessonHistory: Array<LessonHistory>;
}

export const LessonTab: React.FC<LessonTabProps> = ({
  upcomingLessons,
  lessonHistory,
}) => {
  const [open, setOpen] = useState(false);
  const [selectedLessons, setSelectedLessons] = useState<Array<string>>([]);

  const lessonOptions = [
    { value: 'none', label: 'None' },
    { value: 'all', label: 'All Lessons' },
    { value: 'intro', label: 'Intro Lessons' },
    { value: 'lesson-series', label: 'Lesson Series' },
    { value: 'camp', label: 'Camp' },
  ];

  const handleLessonSelection = (value: string) => {
    setSelectedLessons((prev) => {
      // If "None" is selected, unselect everything else
      if (value === 'none') {
        return prev.includes('none') ? [] : ['none'];
      }

      // If "All Lessons" is selected, select all options except "None"
      if (value === 'all') {
        const allSelected = lessonOptions
          .filter((option) => option.value !== 'none')
          .every((option) => prev.includes(option.value));

        if (allSelected) {
          // If all are already selected, unselect "All Lessons"
          return prev.filter((item) => item !== 'all');
        } else {
          // Select all options except "None"
          return lessonOptions
            .filter((option) => option.value !== 'none')
            .map((option) => option.value);
        }
      }

      // For other options
      const newSelection = prev.includes(value)
        ? prev.filter((item) => item !== value)
        : [...prev, value];

      // Remove "None" if any other option is selected
      if (newSelection.length > 0 && newSelection.includes('none')) {
        newSelection.splice(newSelection.indexOf('none'), 1);
      }

      // Check if all options except "None" are selected to include "All Lessons"
      const allOptionsExceptNone = lessonOptions
        .filter((option) => option.value !== 'none')
        .map((option) => option.value);

      const allOtherOptionsSelected = allOptionsExceptNone
        .filter((opt) => opt !== 'all')
        .every((opt) => newSelection.includes(opt));

      if (allOtherOptionsSelected && !newSelection.includes('all')) {
        newSelection.push('all');
      } else if (!allOtherOptionsSelected && newSelection.includes('all')) {
        newSelection.splice(newSelection.indexOf('all'), 1);
      }

      return newSelection;
    });
  };

  return (
    <div className='space-y-6'>
      <Card>
        <CardHeader>
          <CardTitle>Lesson Display Settings</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            <div className='space-y-2'>
              <Label htmlFor='lessons-select'>
                Which lessons should be bookable from parent portal?
              </Label>
              <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant='outline'
                    role='combobox'
                    aria-expanded={open}
                    className='w-full justify-between'
                  >
                    {selectedLessons.length > 0
                      ? `${selectedLessons.length} selected`
                      : 'Select lesson types...'}
                    <ChevronDown className='ml-2 size-4 shrink-0 opacity-50' />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className='w-full p-0'>
                  <Command>
                    {/* <CommandInput placeholder='Search lesson types...' /> */}
                    <CommandEmpty>No lesson type found.</CommandEmpty>
                    <CommandGroup>
                      {lessonOptions.map((option) => (
                        <CommandItem
                          key={option.value}
                          onSelect={() => {
                            handleLessonSelection(option.value);
                          }}
                        >
                          <Check
                            className={cn(
                              'mr-2 size-4',
                              selectedLessons.includes(option.value)
                                ? 'opacity-100'
                                : 'opacity-0',
                            )}
                          />
                          {option.label}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
        {/* Upcoming Lessons */}
        <Card>
          <CardHeader>
            <CardTitle>Upcoming Lessons</CardTitle>
          </CardHeader>
          <CardContent className='space-y-3'>
            {upcomingLessons.map((lesson, index) => (
              <div
                key={index}
                className='flex items-center justify-between rounded-lg bg-gray-50 p-3'
              >
                <div>
                  <p className='font-medium'>{lesson.type}</p>
                  <p className='text-sm text-gray-600'>
                    {lesson.date} at {lesson.time}
                  </p>
                  <p className='text-sm text-gray-600'>
                    Instructor: {lesson.instructor}
                  </p>
                </div>
                <Badge variant='outline'>Scheduled</Badge>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Lesson History */}
        <Card>
          <CardHeader>
            <CardTitle>Lesson History</CardTitle>
          </CardHeader>
          <CardContent className='space-y-3'>
            {lessonHistory.map((lesson, index) => (
              <div
                key={index}
                className='flex items-center justify-between rounded-lg bg-gray-50 p-3'
              >
                <div>
                  <p className='font-medium'>{lesson.type}</p>
                  <p className='text-sm text-gray-600'>{lesson.date}</p>
                  <p className='text-sm text-gray-600'>
                    Performance: {lesson.rating}
                  </p>
                </div>
                <Badge className='bg-green-100 text-green-800'>
                  {lesson.status}
                </Badge>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
