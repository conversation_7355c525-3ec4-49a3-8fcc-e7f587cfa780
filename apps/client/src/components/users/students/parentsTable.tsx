import { Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';

import type { AdminGetParents } from '../../../../../shared';

export interface ParentTableProps {
  parents: Array<AdminGetParents.GetParents.Parent>;
  parentSearch: string;
  selectedParent: string | null; // can be null if nothing is selected
  setSelectedParent: (id: string) => void;
  pagination?: AdminGetParents.GetParents.Pagination;
  currentPage: number;
  setCurrentPage: (page: number) => void;
  pageSize: number;
  isLoading?: boolean;
}

export const ParentsTable = ({
  parents,
  parentSearch,
  selectedParent,
  setSelectedParent,
  pagination,
  currentPage,
  setCurrentPage,
  pageSize,
  isLoading = false,
}: ParentTableProps) => {
  const filteredParents = parents.filter(
    (parent) =>
      `${parent.first_name} ${parent.last_name}`
        .toLowerCase()
        .includes(parentSearch.toLowerCase()) ||
      parent.email.toLowerCase().includes(parentSearch.toLowerCase()),
  );

  return (
    <div className='space-y-4'>
      <div className='overflow-hidden rounded-lg border bg-white'>
        <table className='w-full'>
          <thead className='bg-gray-50'>
            <tr>
              <th className='px-4 py-3 text-left text-sm font-medium text-gray-900'>
                Parent Name
              </th>
              <th className='px-4 py-3 text-left text-sm font-medium text-gray-900'>
                Email
              </th>
              <th className='px-4 py-3 text-left text-sm font-medium text-gray-900'>
                Phone
              </th>
              <th className='px-4 py-3 text-left text-sm font-medium text-gray-900'>
                Address
              </th>
              <th className='px-4 py-3 text-left text-sm font-medium text-gray-900'>
                Students
              </th>
              <th className='px-4 py-3 text-left text-sm font-medium text-gray-900'>
                Select
              </th>
            </tr>
          </thead>
          <tbody className='divide-y divide-gray-200'>
            {isLoading ? (
              <tr>
                <td colSpan={6} className='px-4 py-12 text-center'>
                  <div className='flex items-center justify-center'>
                    <Loader2 className='mr-2 h-6 w-6 animate-spin' />
                    <span className='text-sm text-gray-600'>
                      Loading parents...
                    </span>
                  </div>
                </td>
              </tr>
            ) : filteredParents.length === 0 ? (
              <tr>
                <td colSpan={6} className='px-4 py-12 text-center'>
                  <div className='text-sm text-gray-500'>
                    {parentSearch
                      ? 'No parents found matching your search.'
                      : 'No parents found.'}
                  </div>
                </td>
              </tr>
            ) : (
              filteredParents.map((parent) => (
                <tr
                  key={parent.id}
                  className={`cursor-pointer hover:bg-gray-50 ${
                    selectedParent === parent.id ? 'bg-blue-50' : ''
                  }`}
                  onClick={() => {
                    setSelectedParent(parent.id);
                  }}
                >
                  <td className='px-4 py-3 text-sm font-medium text-gray-900'>
                    {`${parent.first_name} ${parent.last_name}`}
                  </td>
                  <td className='px-4 py-3 text-sm text-gray-600'>
                    {parent.email}
                  </td>
                  <td className='px-4 py-3 text-sm text-gray-600'>
                    {parent.phone ?? 'N/A'}
                  </td>
                  <td className='px-4 py-3 text-sm text-gray-600'>
                    {parent.address ?? 'N/A'}
                  </td>
                  <td className='px-4 py-3 text-sm text-gray-600'>
                    <div className='flex flex-wrap gap-1'>
                      {/* Note: students property doesn't exist in the API type, this needs to be handled differently */}
                      <span className='inline-block rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-700'>
                        No students data
                      </span>
                    </div>
                  </td>
                  <td className='px-4 py-3'>
                    <div
                      className={`h-4 w-4 rounded-full border-2 ${
                        selectedParent === parent.id
                          ? 'border-blue-500 bg-blue-500'
                          : 'border-gray-300'
                      }`}
                    >
                      {selectedParent === parent.id && (
                        <div className='h-full w-full scale-50 rounded-full bg-white'></div>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className='flex items-center justify-between rounded-lg border bg-white p-4'>
          <div className='flex items-center gap-2'>
            <span className='text-sm text-gray-600'>
              Showing {(currentPage - 1) * pageSize + 1} to{' '}
              {Math.min(currentPage * pageSize, pagination.total)} of{' '}
              {pagination.total} results
            </span>
          </div>
          <div className='flex items-center gap-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => {
                setCurrentPage(currentPage - 1);
              }}
              disabled={!pagination.hasPrevPage}
            >
              Previous
            </Button>
            <span className='text-sm text-gray-600'>
              Page {currentPage} of {pagination.totalPages}
            </span>
            <Button
              variant='outline'
              size='sm'
              onClick={() => {
                setCurrentPage(currentPage + 1);
              }}
              disabled={!pagination.hasNextPage}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
