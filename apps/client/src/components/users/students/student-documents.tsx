import { FileText, Plus, Trash2, Upload, XIcon } from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

export const StudentDocuments = () => {
  const [isEditing] = useState(false);
  const [healthData, setHealthData] = useState<{
    lastCheckup: Date | null;
    nextCheckupDue: Date | null;
    vaccinations: string;
    healthStatus: string;
    documents: Array<{
      id: number;
      name: string;
      uploadDate: string;
      type: string;
    }>;
  }>({
    lastCheckup: new Date('2024-01-15'),
    nextCheckupDue: new Date('2024-04-15'),
    vaccinations: 'Up to date',
    healthStatus: 'Excellent',
    documents: [
      {
        id: 1,
        name: 'Record 2024.pdf',
        uploadDate: '2024-01-15',
        type: 'document',
      },
      {
        id: 2,
        name: 'Agreement 2025.pdf',
        uploadDate: '2024-01-15',
        type: 'document',
      },
    ],
  });
  const [newDocument, setNewDocument] = useState<File | null>(null);

  const handleAddDocument = (file: File) => {
    const newDoc = {
      id: Date.now(),
      name: file.name,
      uploadDate: new Date().toISOString().split('T')[0],
      type: 'document',
    };
    setHealthData((prev) => ({
      ...prev,
      documents: [...prev.documents, newDoc],
    }));
    setNewDocument(null);
  };

  const handleCancelUpload = () => {
    setNewDocument(null);
  };

  const handleUpload = (file: File | null) => {
    setNewDocument(file);
  };

  const handleDeleteDocument = (id: number) => {
    setHealthData((prev) => ({
      ...prev,
      documents: prev.documents.filter((doc) => doc.id !== id),
    }));
  };

  // const toggleEdit = () => {
  //   setIsEditing((prev) => !prev);
  // };

  return (
    <div className='border-t pt-4'>
      <div className='mb-4 flex items-center justify-between'>
        <h4 className='font-medium text-gray-900'>Documents</h4>
        <div className='flex gap-2'>
          <input
            type='file'
            id='document-upload'
            className='hidden'
            accept='.pdf,.doc,.docx,.jpg,.jpeg,.png'
            onChange={(e) => {
              handleUpload(e.target.files?.[0] ?? null);
            }}
          />
          <Button
            variant='outline'
            size='sm'
            onClick={() => document.getElementById('document-upload')?.click()}
            className='flex items-center gap-2'
          >
            <Upload className='h-4 w-4' />
            Upload
          </Button>
        </div>
      </div>

      <div className='space-y-2'>
        {healthData.documents.map((doc) => (
          <div
            key={doc.id}
            className='flex items-center justify-between rounded-lg bg-gray-50 p-3'
          >
            <div className='flex items-center gap-3'>
              <FileText className='h-5 w-5 text-gray-500' />
              <div>
                <p className='text-sm font-medium text-gray-900'>{doc.name}</p>
                <p className='text-xs text-gray-500'>
                  Uploaded {doc.uploadDate}
                </p>
              </div>
            </div>
            <div className='flex items-center gap-2'>
              <Badge variant='outline' className='text-xs'>
                {doc.type}
              </Badge>
              {isEditing && (
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => {
                    handleDeleteDocument(doc.id);
                  }}
                >
                  <Trash2 className='h-4 w-4 text-red-500' />
                </Button>
              )}
            </div>
          </div>
        ))}

        {newDocument && (
          <div className='flex items-center justify-between rounded-lg border border-blue-200 bg-blue-50 p-3'>
            <div className='flex items-center gap-3'>
              <FileText className='h-5 w-5 text-blue-500' />
              <div>
                <p className='text-sm font-medium text-blue-900'>
                  {newDocument.name}
                </p>
                <p className='text-xs text-blue-600'>Ready to upload</p>
              </div>
            </div>
            <div className='flex items-center gap-2'>
              <Button
                size='sm'
                onClick={() => {
                  handleAddDocument(newDocument);
                }}
              >
                <Plus className='h-4 w-4' />
              </Button>
              <Button variant='ghost' size='sm' onClick={handleCancelUpload}>
                <XIcon className='h-4 w-4' />
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
