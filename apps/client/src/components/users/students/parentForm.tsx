import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { useAuth } from '@/hooks/useAuthStore';
import { useParentMutation } from '@/hooks/useParentMutation';

import { createTempPass } from '@/utils/createRandomPass';

const parentFormSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(1, 'Phone number is required'),
  address: z.string().optional(),
});

type ParentFormData = z.infer<typeof parentFormSchema>;

export const ParentForm = () => {
  const { user } = useAuth();
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<ParentFormData>({
    resolver: zodResolver(parentFormSchema),
  });

  const parentMutation = useParentMutation();

  const onSubmit = async (data: ParentFormData) => {
    try {
      // Transform the form data to match AuthSignUp.typePayload structure
      const payload = {
        first_name: data.firstName,
        last_name: data.lastName,
        email: data.email,
        phone: data.phone,
        address: data.address,
        password: createTempPass(), // Generate a temporary password
      };

      await parentMutation.mutateAsync({
        data: payload,
        studio_id: user?.studio_id ?? '',
      });

      reset();
      toast.success('Parent account created successfully!', {
        description: `Account for ${data.firstName} ${data.lastName} has been created.`,
      });
    } catch (error) {
      if (error instanceof Error) {
        console.error(error.message);
        toast.error('Failed to create parent account', {
          description: error.message || 'Please try again later.',
        });
      } else {
        toast.error('Failed to create parent account', {
          description: 'An unexpected error occurred. Please try again.',
        });
      }
    }
  };

  return (
    <div>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Card className='mt-4'>
          <CardHeader>
            <CardTitle className='text-lg'>New Parent Information</CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
              <div>
                <Label htmlFor='parentFirstName' className='mb-2 block'>
                  First Name *
                </Label>
                <Input
                  id='parentFirstName'
                  placeholder='Enter first name'
                  {...register('firstName')}
                  className={errors.firstName ? 'border-red-500' : ''}
                />
                {errors.firstName && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.firstName.message}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor='parentLastName' className='mb-2 block'>
                  Last Name *
                </Label>
                <Input
                  id='parentLastName'
                  placeholder='Enter last name'
                  {...register('lastName')}
                  className={errors.lastName ? 'border-red-500' : ''}
                />
                {errors.lastName && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.lastName.message}
                  </p>
                )}
              </div>
            </div>
            <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
              <div>
                <Label htmlFor='parentEmail' className='mb-2 block'>
                  Email Address *
                </Label>
                <Input
                  id='parentEmail'
                  type='email'
                  placeholder='<EMAIL>'
                  {...register('email')}
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.email.message}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor='parentPhone' className='mb-2 block'>
                  Phone Number *
                </Label>
                <Input
                  id='parentPhone'
                  type='tel'
                  placeholder='+****************'
                  {...register('phone')}
                  className={errors.phone ? 'border-red-500' : ''}
                />
                {errors.phone && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.phone.message}
                  </p>
                )}
              </div>
            </div>
            <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
              <div>
                <Label htmlFor='parentAddress' className='mb-2 block'>
                  Address
                </Label>
                <Input
                  id='parentAddress'
                  placeholder='123 Main St, City, State 12345'
                  {...register('address')}
                  className={errors.address ? 'border-red-500' : ''}
                />
                {errors.address && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.address.message}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
          <div className='flex justify-center p-6 pt-0 pb-0'>
            <Button
              type='submit'
              className='w-48'
              disabled={isSubmitting || parentMutation.isPending}
            >
              {isSubmitting || parentMutation.isPending ? (
                <>
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  Creating...
                </>
              ) : (
                'Create Account'
              )}
            </Button>
          </div>
        </Card>
      </form>
    </div>
  );
};
