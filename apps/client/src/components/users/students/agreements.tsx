import type { CreateStudentFormData } from '@/schemas/createStudentSchema';
import type { UseFormReturn } from 'react-hook-form';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

export interface AgreementsProps {
  form: UseFormReturn<CreateStudentFormData>;
}

export const Agreements: React.FC<AgreementsProps> = ({ form }) => {
  const {
    formState: { errors },
    setValue,
    watch,
  } = form;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Agreements & Consent</CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='flex items-center space-x-2'>
          <Checkbox
            id='waiverSigned'
            checked={watch('waiverSigned')}
            onCheckedChange={(checked) => {
              setValue('waiverSigned', checked as boolean);
            }}
          />
          <Label htmlFor='waiverSigned' className='text-sm'>
            Parent/Guardian has read and agreed to the liability waiver and
            terms of service *
          </Label>
        </div>
        {errors.waiverSigned && (
          <p className='mt-1 text-sm text-red-500'>
            {errors.waiverSigned.message}
          </p>
        )}

        <div className='flex items-center space-x-2'>
          <Checkbox
            id='photoConsent'
            checked={watch('photoConsent') ?? false}
            onCheckedChange={(checked) => {
              setValue('photoConsent', checked as boolean);
            }}
          />
          <Label htmlFor='photoConsent' className='text-sm'>
            Parent/Guardian consents to photos being taken during lessons for
            promotional purposes
          </Label>
        </div>
        {errors.photoConsent && (
          <p className='mt-1 text-sm text-red-500'>
            {errors.photoConsent.message}
          </p>
        )}
      </CardContent>
    </Card>
  );
};
