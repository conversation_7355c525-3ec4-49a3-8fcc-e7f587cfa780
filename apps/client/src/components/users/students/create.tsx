import {
  type CreateStudentFormData,
  createStudentFormSchema,
} from '@/schemas/createStudentSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigate } from '@tanstack/react-router';
import { Upload } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { useParents } from '@/hooks/useParents';
import { useCreateStudentMutation } from '@/hooks/useStudentMutation';

import { uploadFile } from '@/lib/supabase';

import { Agreements } from './agreements';
import { MedicalInformation } from './medicalInformation';
import { ParentForm } from './parentForm';
import { ParentsTable } from './parentsTable';
import { RidingInformation } from './ridingInformation';
import { StudentInformation } from './studentInformation';

export const CreateStudent = () => {
  const navigate = useNavigate();
  const [selectedParent, setSelectedParent] = useState<string | null>(null);
  const [showParentForm, _] = useState(false);
  const [parentSearch, setParentSearch] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  // React Hook Form setup
  const form = useForm<CreateStudentFormData>({
    resolver: zodResolver(createStudentFormSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      dateOfBirth: '',
      gender: undefined,
      ridingLevel: undefined,
      previousExperience: '',
      goals: '',
      medicalConditions: '',
      allergies: '',
      profileImage: '',
      waiverSigned: false,
      photoConsent: false,
    },
  });

  const createStudentMutation = useCreateStudentMutation();

  const handleImageUpload = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size must be less than 5MB');
      return;
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    setIsUploading(true);

    try {
      // Generate unique filename with timestamp
      const timestamp = Date.now();
      const fileExtension = file.name.split('.').pop();
      const fileName = `student-profiles/${timestamp}.${fileExtension}`;

      // Upload to Supabase Storage
      const { url, error } = await uploadFile(file, 'avatars', fileName);

      if (error) {
        console.error('Upload error:', error);
        toast.error('Failed to upload image. Please try again.');
        return;
      }

      if (url) {
        // Set the Supabase URL as the profile image
        setProfileImage(url);
        form.setValue('profileImage', url);
        toast.success('Image uploaded successfully!');
      }
    } catch (error) {
      console.error('Upload error:', error);
      toast.error('Failed to upload image. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleSubmit = async (data: CreateStudentFormData) => {
    if (!selectedParent) {
      toast.error('Please select a parent/guardian');
      return;
    }

    try {
      const result = await createStudentMutation.mutateAsync({
        first_name: data.firstName,
        last_name: data.lastName,
        riding_level: data.ridingLevel,
        date_of_birth: data.dateOfBirth,
        gender: data.gender,
        email: data.email ?? undefined,
        phone: data.phone ?? undefined,
        profile_image: data.profileImage ?? undefined,
        previous_experience: data.previousExperience ?? undefined,
        riding_goals: data.goals ?? undefined,
        medical_conditions: data.medicalConditions ?? undefined,
        allergies: data.allergies ?? undefined,
        parent_id: selectedParent,
      });

      if (result.data) {
        toast.success('Student created successfully!');
        form.reset();
        setProfileImage(null);
        setSelectedParent(null);
        void navigate({ to: '/users/students' });
      }
    } catch (error) {
      console.error('Create student error:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to create student',
      );
    }
  };

  const { data, isLoading } = useParents({
    page: currentPage,
    limit: pageSize,
  });

  const parents = data?.parents;

  return (
    <div className='min-h-screen bg-gray-50 p-6'>
      <div className='mx-auto max-w-full space-y-6'>
        {/* Header */}
        <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6'>
          {/* Parent Selection - First Step */}
          <Card>
            <CardHeader>
              <CardTitle>Select Parent/Guardian</CardTitle>
              <p className='text-sm text-gray-600'>
                Choose an existing parent or create a new one
              </p>
            </CardHeader>
            <CardContent className='space-y-4'>
              {/* Search Box */}
              <div className='relative'>
                <Input
                  type='text'
                  placeholder='Search parents by name or email...'
                  value={parentSearch}
                  onChange={(e) => {
                    setParentSearch(e.target.value);
                    setCurrentPage(1); // Reset to first page when searching
                  }}
                  className='pl-10'
                />
                <div className='absolute top-1/2 left-3 -translate-y-1/2 transform'>
                  <svg
                    className='h-4 w-4 text-gray-400'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'
                    />
                  </svg>
                </div>
              </div>

              {/* Parents Table */}
              <div className='overflow-hidden rounded-lg border'>
                <ParentsTable
                  parents={parents ?? []}
                  parentSearch={parentSearch}
                  selectedParent={selectedParent}
                  setSelectedParent={(id: string) => {
                    setSelectedParent(id);
                  }}
                  pagination={data?.pagination}
                  currentPage={currentPage}
                  setCurrentPage={setCurrentPage}
                  pageSize={pageSize}
                  isLoading={isLoading}
                />
              </div>

              {/* <div className='border-t pt-4'> */}
              {/*   <Button */}
              {/*     type='button' */}
              {/*     variant='outline' */}
              {/*     onClick={() => { */}
              {/*       setShowParentForm(!showParentForm); */}
              {/*     }} */}
              {/*     className='w-full' */}
              {/*   > */}
              {/*     {showParentForm ? 'Cancel' : 'Create New Parent'} */}
              {/*   </Button> */}
              {/* </div> */}

              {showParentForm && <ParentForm />}
            </CardContent>
          </Card>

          {/* Only show student form if parent is selected or being created */}
          {(selectedParent ?? showParentForm) && (
            <>
              {/* Profile Photo */}
              <Card>
                <CardHeader>
                  <CardTitle>Student Profile Photo</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className='flex items-center gap-6'>
                    <Avatar className='h-24 w-24'>
                      <AvatarImage
                        src={profileImage ?? ''}
                        alt='Student profile'
                      />
                      <AvatarFallback className='bg-gray-200 text-lg text-gray-600'>
                        {form.watch('firstName')[0] || 'F'}
                        {form.watch('lastName')[0] || 'L'}
                      </AvatarFallback>
                    </Avatar>
                    <div className='space-y-2'>
                      <Label htmlFor='photo-upload' className='cursor-pointer'>
                        <div
                          className={`flex items-center gap-2 rounded-lg px-4 py-2 text-white transition-colors ${
                            isUploading
                              ? 'cursor-not-allowed bg-gray-400'
                              : 'bg-blue-600 hover:bg-blue-700'
                          }`}
                        >
                          <Upload className='h-4 w-4' />
                          {isUploading ? 'Uploading...' : 'Upload Photo'}
                        </div>
                      </Label>
                      <Input
                        id='photo-upload'
                        type='file'
                        accept='image/*'
                        onChange={handleImageUpload}
                        className='hidden'
                        disabled={isUploading}
                      />
                      <p className='text-sm text-gray-500'>
                        JPG, PNG or GIF (max 5MB)
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Student Personal Information */}
              <StudentInformation form={form} />

              {/* Riding Information */}
              <RidingInformation form={form} />

              {/* Medical Information */}
              <MedicalInformation form={form} />
              {/* Agreements */}
              <Agreements form={form} />
            </>
          )}

          {/* Submit Buttons */}
          {(selectedParent ?? showParentForm) && (
            <div className='flex justify-end gap-4'>
              <Button
                type='button'
                variant='outline'
                onClick={() => void navigate({ to: '/users/students' })}
              >
                Cancel
              </Button>
              <Button
                type='submit'
                className='bg-blue-600 hover:bg-blue-700'
                disabled={
                  !form.watch('waiverSigned') ||
                  (!selectedParent && !showParentForm) ||
                  createStudentMutation.isPending ||
                  isUploading
                }
              >
                {createStudentMutation.isPending
                  ? 'Creating...'
                  : isUploading
                    ? 'Uploading Image...'
                    : 'Create Student'}
              </Button>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};
