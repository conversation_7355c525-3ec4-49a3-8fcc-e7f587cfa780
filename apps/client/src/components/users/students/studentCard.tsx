import { Calendar, MapPin, MoreVertical, Phone } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface StudentInstructor {
  id: string;
  name: string;
  avatar: string;
}

interface StudentData {
  id: string;
  name: string;
  contact: string;
  location: string;
  avatar: string;
  instructors: Array<StudentInstructor>;
  lessons: Array<string>;
  paymentStatus: string;
  upcomingLessons: number;
}

interface StudentCardProps {
  student: StudentData;
  handleViewDetails: (studentId: string) => void;
}

export const StudentCard: React.FC<StudentCardProps> = ({
  student,
  handleViewDetails,
}) => {
  return (
    <div className='rounded-xl bg-[#fef7e6] p-6 shadow-sm transition-shadow hover:shadow-md'>
      <div className='mb-4 flex items-start justify-between'>
        <div className='flex items-center gap-3'>
          <Avatar className='h-14 w-14 border-2 border-white'>
            <AvatarImage
              src={student.avatar || '/placeholder.svg'}
              alt={student.name}
            />
            <AvatarFallback className='bg-gray-200 text-gray-700'>
              {student.name
                .split(' ')
                .map((n) => n[0])
                .join('')}
            </AvatarFallback>
          </Avatar>
          <div>
            <h3 className='text-lg font-bold text-gray-900'>{student.name}</h3>
            <div className='flex items-center gap-1 text-sm text-gray-700'>
              <Phone className='h-3 w-3' />
              <span>{student.contact}</span>
            </div>
            <div className='flex items-center gap-1 text-sm text-gray-700'>
              <MapPin className='h-3 w-3' />
              <span>{student.location}</span>
            </div>
          </div>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' size='sm' className='h-8 w-8 p-0'>
              <MoreVertical className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuItem>Edit Student</DropdownMenuItem>
            <DropdownMenuItem>Send Message</DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className='text-red-600'>
              Remove Student
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Instructors */}
      <div className='mb-4'>
        <h4 className='mb-2 font-medium text-gray-800'>Instructors</h4>
        <div className='flex flex-wrap gap-1'>
          {student.instructors.map((instructor) => (
            <div
              key={instructor.id}
              className='flex items-center gap-1 rounded-full bg-white/80 px-2 py-1'
            >
              <Avatar className='h-5 w-5'>
                <AvatarImage
                  src={instructor.avatar || '/placeholder.svg'}
                  alt={instructor.name}
                />
                <AvatarFallback className='text-[8px]'>
                  {instructor.name[0]}
                </AvatarFallback>
              </Avatar>
              <span className='text-xs font-medium'>{instructor.name}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Lessons */}
      <div className='mb-4'>
        <h4 className='mb-2 font-medium text-gray-800'>Lessons</h4>
        <div className='flex flex-wrap gap-1'>
          {student.lessons.map((lesson, index) => (
            <Badge
              key={index}
              variant='outline'
              className='border-gray-700 bg-transparent text-gray-800'
            >
              {lesson}
            </Badge>
          ))}
        </div>
      </div>

      {/* Payment Status */}
      <div className='mb-4 flex items-center justify-between'>
        <div>
          <span className='text-sm text-gray-700'>Payment Status:</span>
          <Badge
            className={`ml-2 ${
              student.paymentStatus === 'Paid'
                ? 'border-green-200 bg-green-100 text-green-800'
                : 'border-red-200 bg-red-100 text-red-800'
            }`}
          >
            {student.paymentStatus}
          </Badge>
        </div>
        <div className='flex items-center gap-1'>
          <Calendar className='h-4 w-4 text-gray-700' />
          <span className='text-sm text-gray-700'>
            {student.upcomingLessons} upcoming
          </span>
        </div>
      </div>

      {/* Action Button */}
      <Button
        variant='outline'
        className='w-full border-gray-700 bg-white text-gray-800 hover:bg-gray-50 hover:text-gray-900'
        onClick={() => {
          handleViewDetails(student.id);
        }}
      >
        View Details
      </Button>
    </div>
  );
};
