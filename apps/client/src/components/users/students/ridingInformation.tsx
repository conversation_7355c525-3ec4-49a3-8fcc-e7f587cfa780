'use client';

import type { CreateStudentFormData } from '@/schemas/createStudentSchema';
import type { UseFormReturn } from 'react-hook-form';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

export interface RidingInformationProps {
  form: UseFormReturn<CreateStudentFormData>;
}

export const RidingInformation: React.FC<RidingInformationProps> = ({
  form,
}) => {
  const {
    register,
    formState: { errors },
    setValue,
    watch,
  } = form;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Riding Information</CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div>
          <Label htmlFor='ridingLevel'>Riding Level *</Label>
          <Select
            value={watch('ridingLevel')}
            onValueChange={(value) => {
              setValue(
                'ridingLevel',
                value as 'beginner' | 'intermediate' | 'advanced',
              );
            }}
          >
            <SelectTrigger
              className={errors.ridingLevel ? 'border-red-500' : ''}
            >
              <SelectValue placeholder='Select riding level' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='beginner'>Beginner</SelectItem>
              <SelectItem value='intermediate'>Intermediate</SelectItem>
              <SelectItem value='advanced'>Advanced</SelectItem>
            </SelectContent>
          </Select>
          {errors.ridingLevel && (
            <p className='mt-1 text-sm text-red-500'>
              {errors.ridingLevel.message}
            </p>
          )}
        </div>

        <div>
          <Label htmlFor='previousExperience'>Previous Experience</Label>
          <Textarea
            id='previousExperience'
            {...register('previousExperience')}
            placeholder='Describe any previous riding experience...'
            rows={3}
            className={errors.previousExperience ? 'border-red-500' : ''}
          />
          {errors.previousExperience && (
            <p className='mt-1 text-sm text-red-500'>
              {errors.previousExperience.message}
            </p>
          )}
        </div>

        <div>
          <Label htmlFor='goals'>Riding Goals</Label>
          <Textarea
            id='goals'
            {...register('goals')}
            placeholder='What would you like to achieve through riding lessons?'
            rows={3}
            className={errors.goals ? 'border-red-500' : ''}
          />
          {errors.goals && (
            <p className='mt-1 text-sm text-red-500'>{errors.goals.message}</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
