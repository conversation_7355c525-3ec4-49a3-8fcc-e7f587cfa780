import type { CreateStudentFormData } from '@/schemas/createStudentSchema';
import type { UseFormReturn } from 'react-hook-form';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

export interface MedicalInformationProps {
  form: UseFormReturn<CreateStudentFormData>;
}

export const MedicalInformation: React.FC<MedicalInformationProps> = ({
  form,
}) => {
  const {
    register,
    formState: { errors },
  } = form;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Medical Information</CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div>
          <Label htmlFor='medicalConditions'>Medical Conditions</Label>
          <Textarea
            id='medicalConditions'
            {...register('medicalConditions')}
            placeholder='List any medical conditions we should be aware of...'
            rows={3}
            className={errors.medicalConditions ? 'border-red-500' : ''}
          />
          {errors.medicalConditions && (
            <p className='mt-1 text-sm text-red-500'>
              {errors.medicalConditions.message}
            </p>
          )}
        </div>

        <div>
          <Label htmlFor='allergies'>Allergies</Label>
          <Textarea
            id='allergies'
            {...register('allergies')}
            placeholder='List any allergies...'
            rows={2}
            className={errors.allergies ? 'border-red-500' : ''}
          />
          {errors.allergies && (
            <p className='mt-1 text-sm text-red-500'>
              {errors.allergies.message}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
