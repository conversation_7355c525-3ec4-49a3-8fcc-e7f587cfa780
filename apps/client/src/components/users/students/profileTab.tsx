'use client';

import { DogIcon as Horse, Mail, MapPin, Phone, User } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface StudentProfile {
  name: string;
  age: number;
  email: string;
  contact: string;
  location: string;
  level: string;
  joinDate: string;
  assignedHorse: string;
  emergencyContact: string;
}

interface ProfileTabProps {
  student: StudentProfile;
}

export const ProfileTab: React.FC<ProfileTabProps> = ({ student }) => {
  return (
    <div className='space-y-6'>
      <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <User className='h-5 w-5' />
              Personal Information
            </CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='grid grid-cols-2 gap-4'>
              <div>
                <Label className='text-sm font-medium text-gray-600'>
                  Full Name
                </Label>
                <p className='text-gray-900'>{student.name}</p>
              </div>
              <div>
                <Label className='text-sm font-medium text-gray-600'>Age</Label>
                <p className='text-gray-900'>{student.age} years old</p>
              </div>
            </div>
            <div>
              <Label className='text-sm font-medium text-gray-600'>Email</Label>
              <p className='flex items-center gap-2 text-gray-900'>
                <Mail className='h-4 w-4' />
                {student.email}
              </p>
            </div>
            <div>
              <Label className='text-sm font-medium text-gray-600'>Phone</Label>
              <p className='flex items-center gap-2 text-gray-900'>
                <Phone className='h-4 w-4' />
                {student.contact}
              </p>
            </div>
            <div>
              <Label className='text-sm font-medium text-gray-600'>
                Location
              </Label>
              <p className='flex items-center gap-2 text-gray-900'>
                <MapPin className='h-4 w-4' />
                {student.location}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Riding Details */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Horse className='h-5 w-5' />
              Riding Details
            </CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div>
              <Label className='text-sm font-medium text-gray-600'>
                Skill Level
              </Label>
              <p className='text-gray-900'>{student.level}</p>
            </div>
            <div>
              <Label className='text-sm font-medium text-gray-600'>
                Join Date
              </Label>
              <p className='text-gray-900'>
                {new Date(student.joinDate).toLocaleDateString()}
              </p>
            </div>
            <div>
              <Label className='text-sm font-medium text-gray-600'>
                Assigned Horse
              </Label>
              <p className='text-gray-900'>{student.assignedHorse}</p>
            </div>
            <div>
              <Label className='text-sm font-medium text-gray-600'>
                Emergency Contact
              </Label>
              <p className='text-sm text-gray-900'>
                {student.emergencyContact}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Notes */}
      <Card>
        <CardHeader>
          <CardTitle>Notes & Comments</CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="Add notes about the student's progress, behavior, or special requirements..."
            className='min-h-[100px]'
            defaultValue='Emma is a dedicated student who shows great improvement in jumping techniques. She works well with Thunder and has a natural affinity for horses. Recommend advancing to intermediate jumping lessons next month.'
          />
        </CardContent>
      </Card>
    </div>
  );
};
