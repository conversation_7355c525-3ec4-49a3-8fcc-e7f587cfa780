import { useNavigate } from '@tanstack/react-router';
import { Search, User } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';

import { useStudents } from '@/hooks/useStudents';

import { StudentDetails } from './student-details';
import { StudentCard } from './studentCard';

// Transform API student data to match component interface
const transformStudentData = (apiStudent: {
  id: string;
  first_name: string;
  last_name: string;
  riding_level: string;
  date_of_birth: string;
  phone: string | null;
  email: string | null;
  profile_image: string | null;
  created_at: Date;
}) => ({
  id: apiStudent.id,
  name: `${apiStudent.first_name} ${apiStudent.last_name}`,
  contact: apiStudent.phone ?? '******-000-0000', // Fallback contact
  location: 'Austin, Texas', // Hardcoded as not available in API
  email: apiStudent.email ?? '<EMAIL>',
  age: apiStudent.date_of_birth
    ? new Date().getFullYear() -
      new Date(apiStudent.date_of_birth).getFullYear()
    : 15, // Calculated age or fallback
  level:
    apiStudent.riding_level.charAt(0).toUpperCase() +
    apiStudent.riding_level.slice(1),
  joinDate: new Date(apiStudent.created_at).toISOString().split('T')[0],
  avatar: apiStudent.profile_image ?? '/placeholder.svg?height=100&width=100',
  lessons: ['Dressage', 'Show Jumping'], // Hardcoded as not available in API
  instructors: [
    { id: '1', name: 'Sarah', avatar: '/placeholder.svg?height=40&width=40' },
    { id: '2', name: 'Michael', avatar: '/placeholder.svg?height=40&width=40' },
  ], // Hardcoded as not available in API
  assignedHorse: 'Thunder', // Hardcoded as not available in API
  emergencyContact: 'Emergency Contact - ******-999-8888', // Hardcoded as not available in API
  paymentStatus: 'Paid', // Hardcoded as not available in API
  upcomingLessons: Math.floor(Math.random() * 5) + 1, // Random number for demo
  completedLessons: Math.floor(Math.random() * 50) + 1, // Random number for demo
});

export const Students = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStudent, setSelectedStudent] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;

  // Fetch students data using the hook
  const {
    data: studentsResponse,
    isLoading,
    error,
  } = useStudents({
    page: currentPage,
    limit: itemsPerPage,
  });

  // Transform API data to component format
  const transformedStudents =
    studentsResponse?.students.map(transformStudentData) ?? [];

  // Filter students based on search term
  const filteredStudents = transformedStudents.filter((student) =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const handleViewDetails = (studentId: string) => {
    setSelectedStudent(studentId);
    setIsModalOpen(true);
  };

  const student = selectedStudent
    ? transformedStudents.find((s) => s.id === selectedStudent)
    : null;

  // Pagination calculations
  const totalPages = studentsResponse?.pagination.totalPages ?? 1;

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Generate page numbers for pagination
  const generatePageNumbers = () => {
    const pages = [];
    const maxVisible = 5;
    const startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2));
    const endPage = Math.min(totalPages, startPage + maxVisible - 1);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    return pages;
  };

  // Loading state
  if (isLoading) {
    return (
      <div className='min-h-screen bg-gray-50 p-6'>
        <div className='mx-auto max-w-7xl space-y-6'>
          <div className='flex items-center justify-center'>
            <div className='text-lg text-gray-600'>Loading students...</div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className='min-h-screen bg-gray-50 p-6'>
        <div className='mx-auto max-w-7xl space-y-6'>
          <div className='flex items-center justify-center'>
            <div className='text-lg text-red-600'>
              Error loading students: {error.message}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gray-50 p-6'>
      <div className='mx-auto max-w-7xl space-y-6'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div>
            <h1 className='text-3xl font-bold text-gray-900'>Students</h1>
            <p className='mt-1 text-gray-600'>
              Manage your riding school students
            </p>
          </div>
          <Button
            className='bg-blue-600 font-medium text-white hover:bg-blue-700'
            onClick={() => navigate({ to: '/users/create-student' })}
          >
            Add New Student
          </Button>
        </div>

        {/* Search */}
        <div className='relative max-w-md'>
          <Search className='absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400' />
          <Input
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
            }}
            placeholder='Search Students'
            className='h-11 rounded-full border-gray-200 bg-white pl-10'
          />
        </div>

        {/* Students Grid */}
        <div className='space-y-4'>
          {/* Results info */}
          {studentsResponse && (
            <div className='text-sm text-gray-600'>
              Showing {(currentPage - 1) * itemsPerPage + 1} to{' '}
              {Math.min(
                currentPage * itemsPerPage,
                studentsResponse.pagination.total,
              )}{' '}
              of {studentsResponse.pagination.total} students
            </div>
          )}

          <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
            {filteredStudents.map((student) => (
              <StudentCard
                key={student.id}
                student={student}
                handleViewDetails={handleViewDetails}
              />
            ))}
          </div>
        </div>

        {/* Empty State */}
        {filteredStudents.length === 0 && (
          <div className='rounded-xl bg-white p-12 text-center shadow-sm'>
            <div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100'>
              <User className='h-8 w-8 text-gray-400' />
            </div>
            <h3 className='mb-2 text-lg font-medium text-gray-900'>
              No students found
            </h3>
            <p className='mb-4 text-gray-500'>
              Try adjusting your search criteria.
            </p>
            <Button
              className='bg-[#fdd36b] text-gray-900 hover:bg-[#fdd36b]/90'
              onClick={() => navigate({ to: '/users/create-student' })}
            >
              Add First Student
            </Button>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className='mt-8 flex justify-center'>
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    href='#'
                    onClick={(e) => {
                      e.preventDefault();
                      if (currentPage > 1) {
                        handlePageChange(currentPage - 1);
                      }
                    }}
                    className={
                      currentPage === 1 ? 'pointer-events-none opacity-50' : ''
                    }
                  />
                </PaginationItem>

                {generatePageNumbers().map((page) => (
                  <PaginationItem key={page}>
                    <PaginationLink
                      href='#'
                      onClick={(e) => {
                        e.preventDefault();
                        handlePageChange(page);
                      }}
                      isActive={currentPage === page}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                ))}

                <PaginationItem>
                  <PaginationNext
                    href='#'
                    onClick={(e) => {
                      e.preventDefault();
                      if (currentPage < totalPages) {
                        handlePageChange(currentPage + 1);
                      }
                    }}
                    className={
                      currentPage === totalPages
                        ? 'pointer-events-none opacity-50'
                        : ''
                    }
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </div>

      {/* Student Details Modal */}
      {student && (
        <StudentDetails
          student={student}
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
          }}
        />
      )}
    </div>
  );
};
