'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface CurrentAssignment {
  avatarSrc: string;
  name: string;
  breedInfo: string;
  assignedSince: string;
  status: string;
}

interface AssignmentHistoryItem {
  horse: string;
  period: string;
  lessons: number;
  status: string;
}

interface HorseAssignmentProps {
  current: CurrentAssignment;
  history: Array<AssignmentHistoryItem>;
}

export const HorseAssignment: React.FC<HorseAssignmentProps> = ({
  current,
  history,
}) => {
  return (
    <div className='space-y-6'>
      {/* Current Assignment */}
      <Card>
        <CardHeader>
          <CardTitle>Current Assignment</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='flex items-center gap-4 rounded-lg border border-blue-200 bg-blue-50 p-4'>
            <Avatar className='h-12 w-12'>
              <AvatarImage src={current.avatarSrc} alt={current.name} />
              <AvatarFallback>
                {current.name
                  .split(' ')
                  .map((n) => n[0])
                  .join('')
                  .toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className='flex-1'>
              <h3 className='text-lg font-semibold'>{current.name}</h3>
              <p className='text-gray-600'>{current.breedInfo}</p>
              <p className='text-sm text-gray-500'>
                Assigned since: {current.assignedSince}
              </p>
            </div>
            <Badge className='bg-blue-100 text-blue-800'>
              {current.status}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Assignment History */}
      <Card>
        <CardHeader>
          <CardTitle>Assignment History</CardTitle>
        </CardHeader>
        <CardContent className='space-y-3'>
          {history.map((assignment, index) => (
            <div
              key={index}
              className='flex items-center justify-between rounded-lg border p-3'
            >
              <div>
                <p className='font-medium'>{assignment.horse}</p>
                <p className='text-sm text-gray-600'>{assignment.period}</p>
                <p className='text-sm text-gray-600'>
                  {assignment.lessons} lessons completed
                </p>
              </div>
              <Badge
                className={
                  assignment.status === 'Active'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-800'
                }
              >
                {assignment.status}
              </Badge>
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  );
};
