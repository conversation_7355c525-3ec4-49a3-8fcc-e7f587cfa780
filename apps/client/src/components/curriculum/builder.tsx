import { Plus } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';

import { AddLevelForm } from './addLevelForm';
import { LevelDetail } from './levelDetail';
import { LevelsTable } from './levelsTable';

interface CurriculumLevel {
  id: string;
  name: string;
  color: string;
  description: string;
  skills: Array<string>;
  order: number;
}

interface Task {
  id: string;
  name: string;
  completed: boolean;
  date: string;
  time: string;
  location: string;
  hasVideo: boolean;
  instructor?: string;
}

interface Checklist {
  id: string;
  name: string;
  tasks: Array<Task>;
  progress: number;
}

const initialLevels: Array<CurriculumLevel> = [
  {
    id: '1',
    name: 'Yellow Level',
    color: '#FCD34D',
    description: 'Basic horsemanship & safety',
    skills: ['Leading', 'Grooming', 'Basic Safety'],
    order: 1,
  },
  {
    id: '2',
    name: 'Red Level',
    color: '#F87171',
    description: 'Intermediate riding skills & control',
    skills: ['Mounting', 'Walking', 'Basic Steering', 'Dismounting'],
    order: 2,
  },
  {
    id: '3',
    name: 'Green Level',
    color: '#4ADE80',
    description: 'Advanced horsemanship techniques',
    skills: ['Trotting', 'Cantering', 'Advanced Control'],
    order: 3,
  },
];

const sampleChecklists: Array<Checklist> = [
  {
    id: '1',
    name: 'Basic Trotting Checklist',
    progress: 59,
    tasks: [
      {
        id: '1',
        name: 'Task 1',
        completed: true,
        date: 'April 18th',
        time: '4pm - 5pm',
        location: 'Ground Arena',
        hasVideo: true,
        instructor: 'Sarah',
      },
      {
        id: '2',
        name: 'Task 2',
        completed: true,
        date: 'April 18th',
        time: '4pm - 5pm',
        location: 'Ground Arena',
        hasVideo: true,
        instructor: 'Sarah',
      },
      {
        id: '3',
        name: 'Task 3',
        completed: true,
        date: 'April 18th',
        time: '4pm - 5pm',
        location: 'Ground Arena',
        hasVideo: true,
        instructor: 'Sarah',
      },
      {
        id: '4',
        name: 'Task 4',
        completed: true,
        date: 'April 18th',
        time: '4pm - 5pm',
        location: 'Ground Arena',
        hasVideo: true,
        instructor: 'Sarah',
      },
      {
        id: '5',
        name: 'Task 5',
        completed: false,
        date: 'April 18th',
        time: '4pm - 5pm',
        location: 'Ground Arena',
        hasVideo: false,
      },
      {
        id: '6',
        name: 'Task 6',
        completed: false,
        date: 'April 18th',
        time: '4pm - 5pm',
        location: 'Ground Arena',
        hasVideo: false,
      },
      {
        id: '7',
        name: 'Task 7',
        completed: false,
        date: 'April 18th',
        time: '4pm - 5pm',
        location: 'Ground Arena',
        hasVideo: false,
      },
    ],
  },
  {
    id: '2',
    name: 'Advanced Jumping Checklist',
    progress: 59,
    tasks: [
      {
        id: '8',
        name: 'Task 1',
        completed: true,
        date: 'April 18th',
        time: '4pm - 5pm',
        location: 'Ground Arena',
        hasVideo: true,
        instructor: 'Mike',
      },
      {
        id: '9',
        name: 'Task 1',
        completed: true,
        date: 'April 18th',
        time: '4pm - 5pm',
        location: 'Ground Arena',
        hasVideo: true,
        instructor: 'Mike',
      },
      {
        id: '10',
        name: 'Task 1',
        completed: true,
        date: 'April 18th',
        time: '4pm - 5pm',
        location: 'Ground Arena',
        hasVideo: true,
        instructor: 'Mike',
      },
      {
        id: '11',
        name: 'Task 1',
        completed: true,
        date: 'April 18th',
        time: '4pm - 5pm',
        location: 'Ground Arena',
        hasVideo: true,
        instructor: 'Mike',
      },
      {
        id: '12',
        name: 'Task 1',
        completed: false,
        date: 'April 18th',
        time: '4pm - 5pm',
        location: 'Ground Arena',
        hasVideo: false,
      },
      {
        id: '13',
        name: 'Task 1',
        completed: false,
        date: 'April 18th',
        time: '4pm - 5pm',
        location: 'Ground Arena',
        hasVideo: false,
      },
    ],
  },
];

const colorOptions = [
  { name: 'Yellow', value: '#FCD34D' },
  { name: 'Red', value: '#F87171' },
  { name: 'Green', value: '#4ADE80' },
  { name: 'Blue', value: '#60A5FA' },
  { name: 'Purple', value: '#A78BFA' },
  { name: 'Orange', value: '#FB923C' },
  { name: 'Pink', value: '#F472B6' },
  { name: 'Teal', value: '#2DD4BF' },
];

export const CurriculumBuilder = () => {
  const [levels, setLevels] = useState<Array<CurriculumLevel>>(initialLevels);
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedLevel, setSelectedLevel] = useState<CurriculumLevel | null>(
    null,
  );
  const [checklists, setChecklists] =
    useState<Array<Checklist>>(sampleChecklists);
  const [newLevel, setNewLevel] = useState({
    name: '',
    color: '#4ADE80',
    description: '',
    skills: '',
  });

  const handleAddLevel = () => {
    if (newLevel.name.trim()) {
      const level: CurriculumLevel = {
        id: Date.now().toString(),
        name: newLevel.name,
        color: newLevel.color,
        description: newLevel.description,
        skills: newLevel.skills
          .split(',')
          .map((s) => s.trim())
          .filter((s) => s),
        order: levels.length + 1,
      };
      setLevels([...levels, level]);
      setNewLevel({ name: '', color: '#4ADE80', description: '', skills: '' });
      setShowAddForm(false);
    }
  };

  const handleDeleteLevel = (id: string) => {
    setLevels(levels.filter((level) => level.id !== id));
  };

  const handleLevelClick = (level: CurriculumLevel) => {
    setSelectedLevel(level);
  };

  const toggleTask = (checklistId: string, taskId: string) => {
    setChecklists(
      checklists.map((checklist) => {
        if (checklist.id === checklistId) {
          const updatedTasks = checklist.tasks.map((task) =>
            task.id === taskId ? { ...task, completed: !task.completed } : task,
          );
          const completedTasks = updatedTasks.filter(
            (task) => task.completed,
          ).length;
          const progress = Math.round(
            (completedTasks / updatedTasks.length) * 100,
          );
          return { ...checklist, tasks: updatedTasks, progress };
        }
        return checklist;
      }),
    );
  };

  const totalSkills = levels.reduce(
    (acc, level) => acc + level.skills.length,
    0,
  );
  const uniqueColors = new Set(levels.map((l) => l.color)).size;

  return (
    <div className='mx-auto max-w-7xl p-6'>
      {/* Header */}
      <div className='mb-6 flex items-center justify-between'>
        <div>
          <h1 className='text-2xl font-semibold text-gray-900'>
            Curriculum Builder
          </h1>
          <p className='mt-1 text-sm text-gray-600'>
            Manage horsemanship levels and skills
          </p>
        </div>
        <Button
          onClick={() => {
            setShowAddForm(true);
          }}
          variant='outline'
          size='sm'
          className='h-8 border-gray-300 px-3 text-sm'
        >
          <Plus className='mr-1 size-4' />
          Add New Level
        </Button>
      </div>

      {/* Add Form */}
      {showAddForm && (
        <AddLevelForm
          setShowAddForm={setShowAddForm}
          newLevel={newLevel}
          setNewLevel={setNewLevel}
          colorOptions={colorOptions}
          handleAddLevel={handleAddLevel}
        />
      )}

      <LevelsTable
        levels={levels}
        handleLevelClick={handleLevelClick}
        handleDeleteLevel={handleDeleteLevel}
      />
      {/* Footer Summary */}
      <div className='mt-8 flex justify-end'>
        <div className='text-xs text-gray-500'>
          {levels.length} Levels | {totalSkills} Skills | {uniqueColors} Colors
        </div>
      </div>

      {/* Level Detail Modal - Sleeker Version */}
      {selectedLevel && (
        <LevelDetail
          selectedLevel={selectedLevel}
          setSelectedLevel={setSelectedLevel}
          checklists={checklists}
          toggleTask={toggleTask}
        />
      )}
    </div>
  );
};
