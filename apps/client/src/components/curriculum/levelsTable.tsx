import { Edit, Trash2 } from 'lucide-react';
import type React from 'react';

import { But<PERSON> } from '@/components/ui/button';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';

// Icons

export interface Level {
  id: string;
  color: string;
  name: string;
  order: number;
  description: string;
  skills: Array<string>;
}

interface LevelsTableProps {
  levels: Array<Level>;
  handleLevelClick: (level: Level) => void;
  handleDeleteLevel: (id: string) => void;
}

export const LevelsTable: React.FC<LevelsTableProps> = ({
  levels,
  handleLevelClick,
  handleDeleteLevel,
}) => {
  return (
    <div className='w-full overflow-x-auto'>
      <Table className='w-full text-left text-sm'>
        <TableHeader>
          <TableRow className='border-b border-gray-200 text-xs font-medium tracking-wide text-gray-500 uppercase'>
            <TableHead className='px-3 py-2'>Color</TableHead>
            <TableHead className='px-3 py-2'>Name</TableHead>
            <TableHead className='px-3 py-2'>Level No.</TableHead>
            <TableHead className='px-3 py-2'>Description</TableHead>
            <TableHead className='px-3 py-2'>Skills</TableHead>
            <TableHead className='px-3 py-2 text-right'>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {levels.map((level) => (
            <TableRow
              key={level.id}
              className='group cursor-pointer border-b border-gray-100 hover:bg-gray-50'
              onClick={() => {
                handleLevelClick(level);
              }}
            >
              {/* Color */}
              <TableCell className='px-4 py-3'>
                <div className='flex items-center'>
                  <div
                    className='size-4 rounded-full'
                    style={{ backgroundColor: level.color }}
                  />
                </div>
              </TableCell>

              {/* Name */}
              <TableCell className='px-1 py-3 font-medium text-gray-900'>
                {level.name}
              </TableCell>

              {/* Level Number */}
              <TableCell className='px-5 py-3 text-gray-600'>
                {level.order}
              </TableCell>

              {/* Description */}
              <TableCell className='px-3 py-3 text-gray-700'>
                {level.description}
              </TableCell>

              {/* Skills */}
              <TableCell className='px-1 py-3'>
                <div className='flex flex-wrap gap-1'>
                  {level.skills.map((skill, index) => (
                    <span
                      key={index}
                      className='inline-block border border-gray-200 bg-gray-100 px-2 py-0.5 text-xs text-gray-700'
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </TableCell>

              {/* Actions */}
              <TableCell className='px-2 py-3 text-right'>
                <div className='flex justify-end gap-1 opacity-0 transition-opacity group-hover:opacity-100'>
                  <Button
                    variant='ghost'
                    size='sm'
                    className='size-6 p-0 hover:bg-gray-200'
                    onClick={(e) => {
                      e.stopPropagation();
                      // Add edit logic here
                    }}
                  >
                    <Edit className='size-3 text-gray-500' />
                  </Button>
                  <Button
                    variant='ghost'
                    size='sm'
                    className='size-6 p-0 hover:bg-red-100'
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteLevel(level.id);
                    }}
                  >
                    <Trash2 className='size-3 text-red-500' />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
