import { X } from 'lucide-react';
import type React from 'react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

export interface ColorOption {
  value: string;
  name: string;
}

export interface NewLevel {
  name: string;
  color: string;
  description: string;
  skills: string;
}

interface AddLevelFormProps {
  setShowAddForm: (show: boolean) => void;
  newLevel: NewLevel;
  setNewLevel: (level: NewLevel) => void;
  colorOptions: Array<ColorOption>;
  handleAddLevel: () => void;
}

export const AddLevelForm: React.FC<AddLevelFormProps> = ({
  setShowAddForm,
  newLevel,
  setNewLevel,
  colorOptions,
  handleAddLevel,
}) => {
  return (
    <div className='mb-6 border-l-4 border-blue-500 bg-gray-50 p-4'>
      <div className='mb-3 flex items-center justify-between'>
        <span className='text-sm font-medium text-gray-900'>Add New Level</span>
        <Button
          variant='ghost'
          size='sm'
          onClick={() => {
            setShowAddForm(false);
          }}
          className='size-6 p-0'
        >
          <X className='size-4' />
        </Button>
      </div>

      <div className='mb-3 grid grid-cols-5 gap-3'>
        {/* Level Name */}
        <Input
          placeholder='Level name'
          value={newLevel.name}
          onChange={(e) => {
            setNewLevel({ ...newLevel, name: e.target.value });
          }}
          className='h-8 text-sm'
        />

        {/* Color Select */}
        <Select
          value={newLevel.color}
          onValueChange={(value) => {
            setNewLevel({ ...newLevel, color: value });
          }}
        >
          <SelectTrigger className='h-8 text-sm'>
            <SelectValue placeholder='Select color' />
          </SelectTrigger>
          <SelectContent>
            {colorOptions.map((color) => (
              <SelectItem key={color.value} value={color.value}>
                <div className='flex items-center gap-2'>
                  <div
                    className='size-3 rounded-full'
                    style={{ backgroundColor: color.value }}
                  />
                  {color.name}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Description */}
        <Input
          placeholder='Description'
          value={newLevel.description}
          onChange={(e) => {
            setNewLevel({ ...newLevel, description: e.target.value });
          }}
          className='h-8 text-sm'
        />

        {/* Skills */}
        <Input
          placeholder='Skills (comma-separated)'
          value={newLevel.skills}
          onChange={(e) => {
            setNewLevel({ ...newLevel, skills: e.target.value });
          }}
          className='h-8 text-sm'
        />

        {/* Save Button */}
        <Button
          onClick={handleAddLevel}
          size='sm'
          className='h-8 bg-blue-600 hover:bg-blue-700'
        >
          Save
        </Button>
      </div>
    </div>
  );
};
