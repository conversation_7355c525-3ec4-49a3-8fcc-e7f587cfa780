import { <PERSON><PERSON><PERSON><PERSON>, MoreH<PERSON><PERSON>tal, Trash2 } from 'lucide-react';
import type React from 'react';

import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Sheet, SheetContent } from '@/components/ui/sheet';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface Task {
  id: string;
  name: string;
  completed: boolean;
  hasVideo?: boolean;
}

interface Checklist {
  id: string;
  name: string;
  progress: number;
  tasks: Array<Task>;
}

interface CurriculumLevel {
  id: string;
  name: string;
  color: string;
  description: string;
  skills: Array<string>;
  order: number;
}

interface LevelDetailProps {
  selectedLevel: CurriculumLevel | null;
  setSelectedLevel: React.Dispatch<
    React.SetStateAction<CurriculumLevel | null>
  >;
  checklists: Array<Checklist>;
  toggleTask: (checklistId: string, taskId: string) => void;
}

export const LevelDetail: React.FC<LevelDetailProps> = ({
  selectedLevel,
  setSelectedLevel,
  checklists,
  toggleTask,
}) => {
  if (!selectedLevel) return null;

  return (
    <Sheet
      open={!!selectedLevel}
      onOpenChange={() => {
        setSelectedLevel(null);
      }}
    >
      <SheetContent side='right' className='p-0'>
        {/* Custom Header */}
        <div className='flex items-center justify-between border-b border-gray-200 px-4 py-3'>
          <div className='flex items-center gap-2'>
            <div
              className='h-5 w-1 rounded-full'
              style={{ backgroundColor: selectedLevel.color }}
            />
            <h2 className='text-lg font-semibold text-gray-900'>
              {selectedLevel.name}
            </h2>
          </div>
        </div>

        {/* Action Buttons */}
        <div className='flex gap-2 border-b border-gray-100 px-4 py-2'>
          <Button variant='ghost' size='sm' className='h-7 text-xs'>
            <CheckSquare className='mr-1 size-3' />
            Add Checklist
          </Button>
        </div>

        {/* Content */}
        <div className='flex-1 overflow-y-auto'>
          <Tabs defaultValue='checklists' className='w-full'>
            <TabsList className='grid w-full grid-cols-2 rounded-none border-b'>
              <TabsTrigger value='checklists' className='text-xs'>
                Checklists
              </TabsTrigger>
              <TabsTrigger value='attachments' className='text-xs'>
                Attachments
              </TabsTrigger>
            </TabsList>

            <TabsContent value='attachments' className='space-y-2 p-4'>
              {[1, 2].map((_, i) => (
                <div
                  key={i}
                  className='flex items-center gap-3 rounded bg-gray-50 p-2'
                >
                  <img
                    src='/placeholder.svg?height=40&width=60'
                    alt='Attachment'
                    className='h-8 w-12 rounded object-cover'
                  />
                  <div className='flex-1'>
                    <div className='text-xs font-medium'>Image.jpeg</div>
                    <div className='text-xs text-gray-500'>
                      April 18th • 5pm • Ground Arena
                    </div>
                  </div>
                  <Button variant='ghost' size='sm' className='size-6 p-0'>
                    <MoreHorizontal className='size-3' />
                  </Button>
                </div>
              ))}
            </TabsContent>

            <TabsContent value='checklists' className='p-0 pt-2'>
              {checklists.map((checklist) => (
                <div key={checklist.id} className='mb-6'>
                  <div className='flex items-center justify-between px-4 py-2'>
                    <div className='space-y-1'>
                      <h3 className='text-sm font-semibold text-gray-900'>
                        {checklist.name}
                      </h3>
                    </div>
                    <div className='flex gap-1'>
                      <Button
                        variant='ghost'
                        size='sm'
                        className='h-6 px-2 text-xs'
                      >
                        Hide Checked
                      </Button>
                      <Button
                        variant='ghost'
                        size='sm'
                        className='size-6 p-0 text-red-500'
                      >
                        <Trash2 className='size-3' />
                      </Button>
                    </div>
                  </div>

                  <div className='mt-1 space-y-0'>
                    {checklist.tasks.map((task) => (
                      <div
                        key={task.id}
                        className={`flex items-center gap-2 border-b border-gray-100 px-4 py-2 hover:bg-gray-50 ${
                          task.completed ? 'bg-gray-50' : ''
                        }`}
                      >
                        <Checkbox
                          checked={task.completed}
                          onCheckedChange={() => {
                            toggleTask(checklist.id, task.id);
                          }}
                          className='size-4'
                        />
                        <div className='min-w-0 flex-1'>
                          <input
                            className='w-full border-none bg-transparent text-xs font-medium text-gray-900 outline-none'
                            defaultValue={task.name}
                          />
                          <input
                            className='w-full border-none bg-transparent text-xs text-gray-500 outline-none'
                            defaultValue={`${task.name} Subheading`}
                          />
                        </div>
                        {task.completed && task.hasVideo && (
                          <div className='relative flex-shrink-0'>
                            <img
                              src='/placeholder.svg?height=24&width=36'
                              alt='Video thumbnail'
                              className='h-6 w-9 rounded object-cover'
                            />
                          </div>
                        )}
                        <Button
                          variant='ghost'
                          size='sm'
                          className='size-5 flex-shrink-0 p-0'
                        >
                          <span className='text-xs text-gray-400'>Edit</span>
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </TabsContent>
          </Tabs>
        </div>
      </SheetContent>
    </Sheet>
  );
};
