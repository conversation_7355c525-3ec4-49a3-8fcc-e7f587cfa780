import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

const activities = [
  {
    id: 1,
    user: { name: '<PERSON>', avatar: '/placeholder.svg?height=40&width=40' },
    lesson: 'Advanced Jumping',
    timestamp: '18 April 2025, 8am - 9am',
    status: 'Lesson Completed',
    statusColor: 'bg-green-100 text-green-700',
  },
  {
    id: 2,
    user: { name: '<PERSON>', avatar: '/placeholder.svg?height=40&width=40' },
    lesson: 'Basic Trotting',
    timestamp: '18 April 2025, 8am - 9am',
    status: 'New Lesson Booked',
    statusColor: 'bg-purple-100 text-purple-700',
  },
  {
    id: 3,
    user: { name: '<PERSON>', avatar: '/placeholder.svg?height=40&width=40' },
    lesson: 'Advanced Jumping',
    timestamp: '17 April 2025, 8am - 9am',
    status: 'Lesson Completed',
    statusColor: 'bg-green-100 text-green-700',
  },
  {
    id: 4,
    user: { name: '<PERSON>', avatar: '/placeholder.svg?height=40&width=40' },
    lesson: 'Advanced Jumping',
    timestamp: '18 April 2025, 8am - 9am',
    status: 'Lesson Completed',
    statusColor: 'bg-green-100 text-green-700',
  },
];

export function RecentActivity() {
  return (
    <div className='rounded-lg border border-gray-200 bg-white p-6'>
      <div className='mb-6 flex items-center justify-between'>
        <h2 className='text-xl font-semibold text-gray-900'>Recent Activity</h2>
        <Button
          variant='ghost'
          className='font-normal text-gray-500 hover:text-gray-700'
        >
          View All <span className='ml-1'>→</span>
        </Button>
      </div>

      <div className='space-y-6'>
        {activities.map((activity) => (
          <div key={activity.id} className='flex items-start gap-4'>
            {/* User Avatar */}
            <Avatar className='h-12 w-12 flex-shrink-0'>
              <AvatarImage
                src={activity.user.avatar || '/placeholder.svg'}
                alt={activity.user.name}
              />
              <AvatarFallback className='bg-gray-200 font-medium text-gray-600'>
                {activity.user.name[0]}
              </AvatarFallback>
            </Avatar>

            {/* Content */}
            <div className='min-w-0 flex-1'>
              {/* User name and lesson */}
              <div className='mb-2'>
                <span className='text-lg font-semibold text-gray-900'>
                  {activity.user.name}
                </span>
              </div>
              <div className='mb-3'>
                <span className='text-lg font-semibold text-gray-900'>
                  {activity.lesson}
                </span>
              </div>
            </div>

            {/* Right side - Timestamp and Status */}
            <div className='flex flex-shrink-0 flex-col items-end gap-2'>
              <span className='text-sm text-gray-500'>
                {activity.timestamp}
              </span>
              <Badge
                className={`${activity.statusColor} rounded-full border-0 px-3 py-1 font-medium`}
              >
                {activity.status}
              </Badge>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
