import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'recharts';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';

const advertisingData = [
  {
    weekOf: '05/20/24',
    amountSpent: 1250,
    leads: 45,
    costPerLead: 27.78,
    introsSignups: 12,
    introConversionPercent: 26.7,
    costPerIntro: 104.17,
    subscriptions: 8,
    subConvPercent: 66.7,
    costPerSubscription: 156.25,
  },
  {
    weekOf: '05/13/24',
    amountSpent: 980,
    leads: 38,
    costPerLead: 25.79,
    introsSignups: 9,
    introConversionPercent: 23.7,
    costPerIntro: 108.89,
    subscriptions: 6,
    subConvPercent: 66.7,
    costPerSubscription: 163.33,
  },
  {
    weekOf: '05/06/24',
    amountSpent: 1100,
    leads: 42,
    costPerLead: 26.19,
    introsSignups: 11,
    introConversionPercent: 26.2,
    costPerIntro: 100.0,
    subscriptions: 7,
    subConvPercent: 63.6,
    costPerSubscription: 157.14,
  },
];
export const AdvertisingMetrics = () => {
  const adChartData = advertisingData.map((row) => ({
    week: row.weekOf,
    costPerLead: row.costPerLead,
    costPerSubscription: row.costPerSubscription,
    leads: row.leads,
    subscriptions: row.subscriptions,
  }));
  return (
    <div className='space-y-10'>
      {/* Advertising Performance Chart */}
      <div className='rounded-lg bg-white shadow-sm ring-1 ring-gray-200'>
        <div className='border-b border-gray-100 p-6'>
          <h3 className='text-lg font-semibold text-gray-900'>
            Cost Per Lead vs Cost Per Subscription
          </h3>
        </div>
        <div className='p-6'>
          <ResponsiveContainer width='100%' height={300}>
            <BarChart data={adChartData}>
              <CartesianGrid strokeDasharray='3 3' stroke='#f3f4f6' />
              <XAxis dataKey='week' stroke='#6b7280' />
              <YAxis stroke='#6b7280' />
              <Tooltip
                formatter={(value: number) => [`$${value}`, '']}
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                }}
              />
              <Legend />
              <Bar dataKey='costPerLead' fill='#fbbf24' name='Cost Per Lead' />
              <Bar
                dataKey='costPerSubscription'
                fill='#4f46e5'
                name='Cost Per Subscription'
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      <div className='rounded-lg bg-white shadow-sm ring-1 ring-gray-200'>
        <div className='p-6'>
          <div className='overflow-x-auto'>
            <Table>
              <TableHeader>
                <TableRow className='bg-gray-50'>
                  <TableHead className='font-semibold text-gray-700'>
                    Week of
                  </TableHead>
                  <TableHead className='text-right font-semibold text-gray-700'>
                    Amount Spent
                  </TableHead>
                  <TableHead className='text-right font-semibold text-gray-700'>
                    Leads
                  </TableHead>
                  <TableHead className='text-right font-semibold text-gray-700'>
                    Cost Per Lead
                  </TableHead>
                  <TableHead className='text-right font-semibold text-gray-700'>
                    Intros/Sign-ups
                  </TableHead>
                  <TableHead className='text-right font-semibold text-gray-700'>
                    Intro Conversion %
                  </TableHead>
                  <TableHead className='text-right font-semibold text-gray-700'>
                    Cost Per Intro
                  </TableHead>
                  <TableHead className='text-right font-semibold text-gray-700'>
                    Subscriptions
                  </TableHead>
                  <TableHead className='text-right font-semibold text-gray-700'>
                    Sub Conv %
                  </TableHead>
                  <TableHead className='text-right font-semibold text-gray-700'>
                    Cost Per Subscription
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {advertisingData.map((row, index) => (
                  <TableRow key={index} className='even:bg-gray-50'>
                    <TableCell className='text-gray-900'>
                      {row.weekOf}
                    </TableCell>
                    <TableCell className='text-right font-medium text-gray-900'>
                      ${row.amountSpent}
                    </TableCell>
                    <TableCell className='text-right font-medium text-gray-900'>
                      {row.leads}
                    </TableCell>
                    <TableCell className='text-right text-gray-900'>
                      ${row.costPerLead}
                    </TableCell>
                    <TableCell className='text-right font-medium text-gray-900'>
                      {row.introsSignups}
                    </TableCell>
                    <TableCell className='text-right text-gray-900'>
                      {row.introConversionPercent}%
                    </TableCell>
                    <TableCell className='text-right text-gray-900'>
                      ${row.costPerIntro}
                    </TableCell>
                    <TableCell className='text-right font-medium text-gray-900'>
                      {row.subscriptions}
                    </TableCell>
                    <TableCell className='text-right text-gray-900'>
                      {row.subConvPercent}%
                    </TableCell>
                    <TableCell className='text-right text-gray-900'>
                      ${row.costPerSubscription}
                    </TableCell>
                  </TableRow>
                ))}
                <TableRow className='bg-gray-100 font-semibold'>
                  <TableCell className='text-gray-900'>May Totals</TableCell>
                  <TableCell className='text-right text-gray-900'>
                    $3,330
                  </TableCell>
                  <TableCell className='text-right text-gray-900'>
                    125
                  </TableCell>
                  <TableCell className='text-right text-gray-900'>
                    $26.64
                  </TableCell>
                  <TableCell className='text-right text-gray-900'>32</TableCell>
                  <TableCell className='text-right text-gray-900'>
                    25.6%
                  </TableCell>
                  <TableCell className='text-right text-gray-900'>
                    $104.06
                  </TableCell>
                  <TableCell className='text-right text-gray-900'>21</TableCell>
                  <TableCell className='text-right text-gray-900'>
                    65.6%
                  </TableCell>
                  <TableCell className='text-right text-gray-900'>
                    $158.57
                  </TableCell>
                </TableRow>
                <TableRow className='bg-gray-100 font-semibold'>
                  <TableCell className='text-gray-900'>April Totals</TableCell>
                  <TableCell className='text-right text-gray-900'>
                    $1,890
                  </TableCell>
                  <TableCell className='text-right text-gray-900'>72</TableCell>
                  <TableCell className='text-right text-gray-900'>
                    $26.25
                  </TableCell>
                  <TableCell className='text-right text-gray-900'>18</TableCell>
                  <TableCell className='text-right text-gray-900'>
                    25.0%
                  </TableCell>
                  <TableCell className='text-right text-gray-900'>
                    $105.00
                  </TableCell>
                  <TableCell className='text-right text-gray-900'>12</TableCell>
                  <TableCell className='text-right text-gray-900'>
                    66.7%
                  </TableCell>
                  <TableCell className='text-right text-gray-900'>
                    $157.50
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>
      </div>

      {/* Meta Integration Status */}
      <div className='rounded-lg bg-green-50 shadow-sm ring-1 ring-green-200'>
        <div className='p-6'>
          <div className='flex items-center gap-3'>
            <div className='h-3 w-3 animate-pulse rounded-full bg-green-500'></div>
            <div>
              <p className='font-medium text-green-800'>
                Meta Ads Integration Active
              </p>
              <p className='text-sm text-green-600'>
                Last sync: {new Date().toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
