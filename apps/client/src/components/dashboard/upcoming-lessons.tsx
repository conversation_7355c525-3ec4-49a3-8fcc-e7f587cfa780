import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';

import { cn } from '@/lib/utils';

// Sample lesson data with dates across multiple days
const allLessons = [
  // Sunday - April 13th
  {
    id: 1,
    title: 'Weekend Trail Ride',
    date: new Date(2025, 3, 13), // April 13th, 2025
    time: '9am - 11am',
    location: 'Mountain Trail',
    student: { name: '<PERSON>', avatar: '/placeholder.svg?height=32&width=32' },
    horse: { name: '<PERSON>', avatar: '/placeholder.svg?height=32&width=32' },
    invigilator: {
      name: '<PERSON>',
      avatar: '/placeholder.svg?height=32&width=32',
    },
    image: '/placeholder.svg?height=120&width=120',
    color: 'green',
  },

  // Monday - April 14th
  {
    id: 2,
    title: 'Basic Riding Fundamentals',
    date: new Date(2025, 3, 14), // April 14th, 2025
    time: '10am - 11am',
    location: 'Training Arena',
    student: { name: '<PERSON>', avatar: '/placeholder.svg?height=32&width=32' },
    horse: { name: 'Gen<PERSON>', avatar: '/placeholder.svg?height=32&width=32' },
    invigilator: {
      name: 'Mike',
      avatar: '/placeholder.svg?height=32&width=32',
    },
    image: '/placeholder.svg?height=120&width=120',
    color: 'blue',
  },
  {
    id: 3,
    title: 'Intermediate Dressage',
    date: new Date(2025, 3, 14), // April 14th, 2025
    time: '2pm - 3pm',
    location: 'Main Arena',
    student: { name: 'Sophie', avatar: '/placeholder.svg?height=32&width=32' },
    horse: { name: 'Elegance', avatar: '/placeholder.svg?height=32&width=32' },
    invigilator: {
      name: 'Anna',
      avatar: '/placeholder.svg?height=32&width=32',
    },
    image: '/placeholder.svg?height=120&width=120',
    color: 'purple',
  },

  // Tuesday - April 15th
  {
    id: 4,
    title: 'Show Jumping Practice',
    date: new Date(2025, 3, 15), // April 15th, 2025
    time: '11am - 12pm',
    location: 'Jumping Arena',
    student: { name: 'Lucas', avatar: '/placeholder.svg?height=32&width=32' },
    horse: { name: 'Thunder', avatar: '/placeholder.svg?height=32&width=32' },
    invigilator: { name: 'Tom', avatar: '/placeholder.svg?height=32&width=32' },
    image: '/placeholder.svg?height=120&width=120',
    color: 'orange',
  },

  // Wednesday - April 16th
  {
    id: 5,
    title: 'Beginner Lesson',
    date: new Date(2025, 3, 16), // April 16th, 2025
    time: '9am - 10am',
    location: 'Training Arena',
    student: { name: 'Mia', avatar: '/placeholder.svg?height=32&width=32' },
    horse: { name: 'Calm', avatar: '/placeholder.svg?height=32&width=32' },
    invigilator: {
      name: 'Jessica',
      avatar: '/placeholder.svg?height=32&width=32',
    },
    image: '/placeholder.svg?height=120&width=120',
    color: 'green',
  },
  {
    id: 6,
    title: 'Advanced Jumping',
    date: new Date(2025, 3, 16), // April 16th, 2025
    time: '3pm - 4pm',
    location: 'Competition Arena',
    student: { name: 'Ryan', avatar: '/placeholder.svg?height=32&width=32' },
    horse: { name: 'Lightning', avatar: '/placeholder.svg?height=32&width=32' },
    invigilator: {
      name: 'David',
      avatar: '/placeholder.svg?height=32&width=32',
    },
    image: '/placeholder.svg?height=120&width=120',
    color: 'red',
  },

  // Thursday - April 17th
  {
    id: 7,
    title: 'Advanced Jumping',
    date: new Date(2025, 3, 17), // April 17th, 2025
    time: '4pm - 5pm',
    location: 'Ground Arena',
    student: { name: 'John', avatar: '/placeholder.svg?height=32&width=32' },
    horse: { name: 'Hunter', avatar: '/placeholder.svg?height=32&width=32' },
    invigilator: {
      name: 'Jessica',
      avatar: '/placeholder.svg?height=32&width=32',
    },
    image: '/placeholder.svg?height=120&width=120',
    color: 'purple',
  },
  {
    id: 8,
    title: 'Basic Trotting',
    date: new Date(2025, 3, 17), // April 17th, 2025
    time: '6pm - 7pm',
    location: 'Ground Arena',
    student: { name: 'Emma', avatar: '/placeholder.svg?height=32&width=32' },
    horse: { name: 'Star', avatar: '/placeholder.svg?height=32&width=32' },
    invigilator: {
      name: 'Mike',
      avatar: '/placeholder.svg?height=32&width=32',
    },
    image: '/placeholder.svg?height=120&width=120',
    color: 'green',
  },

  // Friday - April 18th
  {
    id: 9,
    title: 'Dressage Training',
    date: new Date(2025, 3, 18), // April 18th, 2025
    time: '10am - 11am',
    location: 'Main Arena',
    student: { name: 'Sarah', avatar: '/placeholder.svg?height=32&width=32' },
    horse: { name: 'Grace', avatar: '/placeholder.svg?height=32&width=32' },
    invigilator: {
      name: 'Anna',
      avatar: '/placeholder.svg?height=32&width=32',
    },
    image: '/placeholder.svg?height=120&width=120',
    color: 'blue',
  },
  {
    id: 10,
    title: 'Cross Country Prep',
    date: new Date(2025, 3, 18), // April 18th, 2025
    time: '2pm - 4pm',
    location: 'Cross Country Course',
    student: { name: 'Jake', avatar: '/placeholder.svg?height=32&width=32' },
    horse: { name: 'Brave', avatar: '/placeholder.svg?height=32&width=32' },
    invigilator: { name: 'Tom', avatar: '/placeholder.svg?height=32&width=32' },
    image: '/placeholder.svg?height=120&width=120',
    color: 'green',
  },

  // Saturday - April 19th
  {
    id: 11,
    title: 'Trail Riding Adventure',
    date: new Date(2025, 3, 19), // April 19th, 2025
    time: '8am - 10am',
    location: 'Forest Trail',
    student: { name: 'Olivia', avatar: '/placeholder.svg?height=32&width=32' },
    horse: { name: 'Adventure', avatar: '/placeholder.svg?height=32&width=32' },
    invigilator: {
      name: 'Sarah',
      avatar: '/placeholder.svg?height=32&width=32',
    },
    image: '/placeholder.svg?height=120&width=120',
    color: 'green',
  },
  {
    id: 12,
    title: 'Competition Preparation',
    date: new Date(2025, 3, 19), // April 19th, 2025
    time: '1pm - 3pm',
    location: 'Competition Arena',
    student: { name: 'Ethan', avatar: '/placeholder.svg?height=32&width=32' },
    horse: { name: 'Champion', avatar: '/placeholder.svg?height=32&width=32' },
    invigilator: {
      name: 'David',
      avatar: '/placeholder.svg?height=32&width=32',
    },
    image: '/placeholder.svg?height=120&width=120',
    color: 'purple',
  },
  {
    id: 13,
    title: 'Evening Ride',
    date: new Date(2025, 3, 19), // April 19th, 2025
    time: '5pm - 6pm',
    location: 'Sunset Trail',
    student: { name: 'Chloe', avatar: '/placeholder.svg?height=32&width=32' },
    horse: { name: 'Sunset', avatar: '/placeholder.svg?height=32&width=32' },
    invigilator: {
      name: 'Anna',
      avatar: '/placeholder.svg?height=32&width=32',
    },
    image: '/placeholder.svg?height=120&width=120',
    color: 'orange',
  },
];

// Helper function to get week dates
function getWeekDates(startDate: Date) {
  const week = [];
  const start = new Date(startDate);

  // Get the Sunday of the week
  const day = start.getDay();
  const diff = start.getDate() - day;
  const sunday = new Date(start.setDate(diff));

  for (let i = 0; i < 7; i++) {
    const date = new Date(sunday);
    date.setDate(sunday.getDate() + i);
    week.push(date);
  }

  return week;
}

// Helper function to format week range
function formatWeekRange(dates: Array<Date>) {
  const start = dates[0];
  const end = dates[6];

  const startMonth = start.toLocaleDateString('en-US', { month: 'short' });
  const endMonth = end.toLocaleDateString('en-US', { month: 'short' });
  const startDay = start.getDate();
  const endDay = end.getDate();
  const year = end.getFullYear();

  if (startMonth === endMonth) {
    return `${startDay}${getOrdinalSuffix(startDay)} ${startMonth} - ${endDay}${getOrdinalSuffix(endDay)} ${startMonth} ${year}`;
  } else {
    return `${startDay}${getOrdinalSuffix(startDay)} ${startMonth} - ${endDay}${getOrdinalSuffix(endDay)} ${endMonth} ${year}`;
  }
}

// Helper function to get ordinal suffix
function getOrdinalSuffix(day: number) {
  if (day > 3 && day < 21) return 'th';
  switch (day % 10) {
    case 1:
      return 'st';
    case 2:
      return 'nd';
    case 3:
      return 'rd';
    default:
      return 'th';
  }
}

// Helper function to check if date has events
function hasEvents(date: Date) {
  return allLessons.some((lesson) => {
    return (
      lesson.date.getDate() === date.getDate() &&
      lesson.date.getMonth() === date.getMonth() &&
      lesson.date.getFullYear() === date.getFullYear()
    );
  });
}

// Helper function to get lessons for a specific date
function getLessonsForDate(date: Date) {
  return allLessons.filter((lesson) => {
    return (
      lesson.date.getDate() === date.getDate() &&
      lesson.date.getMonth() === date.getMonth() &&
      lesson.date.getFullYear() === date.getFullYear()
    );
  });
}

// Helper function to format lesson date
function formatLessonDate(date: Date) {
  return date.toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric',
  });
}

const weekDays = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];

export function UpcomingLessons() {
  // Initialize with April 13-19, 2025 week
  const [currentWeekStart, setCurrentWeekStart] = useState(
    () => new Date(2025, 3, 13),
  );
  const [selectedDate, setSelectedDate] = useState(() => new Date(2025, 3, 17)); // April 17th, 2025

  // Get current week dates
  const weekDates = getWeekDates(currentWeekStart);
  const weekRange = formatWeekRange(weekDates);

  // Get lessons for selected date
  const selectedLessons = getLessonsForDate(selectedDate);

  // Function to handle previous week
  const handlePrevWeek = () => {
    const newStart = new Date(currentWeekStart);
    newStart.setDate(currentWeekStart.getDate() - 7);
    setCurrentWeekStart(newStart);
  };

  // Function to handle next week
  const handleNextWeek = () => {
    const newStart = new Date(currentWeekStart);
    newStart.setDate(currentWeekStart.getDate() + 7);
    setCurrentWeekStart(newStart);
  };

  // Function to handle date selection
  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
  };

  // Function to check if two dates are the same day
  const isSameDay = (date1: Date, date2: Date) => {
    return (
      date1.getDate() === date2.getDate() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getFullYear() === date2.getFullYear()
    );
  };

  return (
    <div className='rounded-lg border border-gray-200 bg-white p-6'>
      <div className='mb-6 flex items-center justify-between'>
        <h2 className='text-2xl font-semibold text-gray-700'>
          Upcoming Lessons
        </h2>
        <Button
          variant='ghost'
          className='font-normal text-gray-500 hover:text-gray-700'
        >
          View All <span className='ml-1'>→</span>
        </Button>
      </div>

      {/* Week Navigation */}
      <div className='mb-6 flex items-center justify-center space-x-4'>
        <Button
          variant='outline'
          size='icon'
          className='h-10 w-10 rounded-md border-0 bg-gray-100 hover:bg-gray-200'
          onClick={handlePrevWeek}
        >
          <ChevronLeft className='h-5 w-5' />
        </Button>
        <span className='min-w-[200px] text-center font-medium text-gray-900'>
          {weekRange}
        </span>
        <Button
          variant='outline'
          size='icon'
          className='h-10 w-10 rounded-md border-0 bg-gray-100 hover:bg-gray-200'
          onClick={handleNextWeek}
        >
          <ChevronRight className='h-5 w-5' />
        </Button>
      </div>

      {/* Calendar Days */}
      <div className='mb-6 grid grid-cols-7 gap-2'>
        {weekDates.map((date, index) => {
          const isSelected = isSameDay(date, selectedDate);
          const hasLesson = hasEvents(date);

          return (
            <button
              key={`${weekDays[index]}-${date.getDate()}`}
              onClick={() => {
                handleDateSelect(date);
              }}
              className={cn(
                'flex h-20 flex-col items-center justify-center rounded-lg transition-colors',
                isSelected
                  ? 'bg-gray-900 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200',
              )}
            >
              <span className='text-sm font-medium'>{weekDays[index]}</span>

              {/* Dot indicator */}
              {hasLesson && (
                <div
                  className={cn(
                    'my-1 h-1.5 w-1.5 rounded-full',
                    isSelected ? 'bg-yellow-400' : 'bg-gray-400',
                  )}
                />
              )}

              <span className='text-lg font-semibold'>{date.getDate()}</span>
            </button>
          );
        })}
      </div>

      {/* Lesson Cards or Empty State */}
      {selectedLessons.length > 0 ? (
        <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
          {selectedLessons.map((lesson) => (
            <div
              key={lesson.id}
              className='relative overflow-hidden rounded-lg bg-[#FFF8E7]'
            >
              <div className='p-5'>
                <div className='mb-4 flex items-start justify-between'>
                  <div>
                    <h3 className='mb-1 text-xl font-semibold text-gray-900'>
                      {lesson.title}
                    </h3>
                    <p className='text-sm text-gray-600'>
                      {formatLessonDate(lesson.date)} • {lesson.time} •{' '}
                      {lesson.location}
                    </p>
                  </div>
                  <div className='flex h-24 w-24 items-center justify-center overflow-hidden rounded-lg bg-gradient-to-br from-purple-400 to-purple-600'>
                    <img
                      src={lesson.image || '/placeholder.svg'}
                      alt={lesson.title}
                      className='h-full w-full object-cover'
                    />
                  </div>
                </div>

                <div className='space-y-3'>
                  <div className='flex items-start justify-between'>
                    <div className='w-1/3'>
                      <span className='text-sm font-medium text-gray-700'>
                        Student
                      </span>
                    </div>
                    <div className='w-1/3'>
                      <span className='text-sm font-medium text-gray-700'>
                        Horse
                      </span>
                    </div>
                    <div className='w-1/3'>
                      <span className='text-sm font-medium text-gray-700'>
                        Invigilator
                      </span>
                    </div>
                  </div>

                  <div className='flex items-center justify-between'>
                    <div className='flex items-center gap-2 rounded-full border border-gray-200 bg-white/80 px-3 py-1.5'>
                      <img
                        src={lesson.student.avatar || '/placeholder.svg'}
                        alt={lesson.student.name}
                        className='h-6 w-6 rounded-full object-cover'
                      />
                      <span className='text-sm font-medium'>
                        {lesson.student.name}
                      </span>
                    </div>

                    <div className='flex items-center gap-2 rounded-full border border-gray-200 bg-white/80 px-3 py-1.5'>
                      <img
                        src={lesson.horse.avatar || '/placeholder.svg'}
                        alt={lesson.horse.name}
                        className='h-6 w-6 rounded-full object-cover'
                      />
                      <span className='text-sm font-medium'>
                        {lesson.horse.name}
                      </span>
                    </div>

                    <div className='flex items-center gap-2 rounded-full border border-gray-200 bg-white/80 px-3 py-1.5'>
                      <img
                        src={lesson.invigilator.avatar || '/placeholder.svg'}
                        alt={lesson.invigilator.name}
                        className='h-6 w-6 rounded-full object-cover'
                      />
                      <span className='text-sm font-medium'>
                        {lesson.invigilator.name}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className='flex flex-col items-center justify-center py-12 text-center'>
          <div className='mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100'>
            <svg
              className='h-8 w-8 text-gray-400'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'
              />
            </svg>
          </div>
          <h3 className='mb-2 text-lg font-medium text-gray-900'>
            No lessons scheduled
          </h3>
          <p className='mb-4 text-gray-500'>
            There are no lessons scheduled for {formatLessonDate(selectedDate)}.
          </p>
          <Button className='bg-[#fdd36b] text-gray-900 hover:bg-[#fdd36b]/90'>
            Schedule a Lesson
          </Button>
        </div>
      )}
    </div>
  );
}
