import { ChevronDown } from 'lucide-react';
import {
  Bar,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

import { Button } from '../ui/button';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '../ui/collapsible';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';

const churnData = [
  {
    month: 'January',
    membersAtStart: 50,
    membersLost: 5,
    membersAdded: 6,
    churnPercent: 10.0,
    details: [
      { name: '', reasonLeft: '', primaryInstructor: '', exitInterview: '' },
      { name: '', reasonLeft: '', primaryInstructor: '', exitInterview: '' },
      { name: '', reasonLeft: '', primaryInstructor: '', exitInterview: '' },
      { name: '', reasonLeft: '', primaryInstructor: '', exitInterview: '' },
    ],
  },
  {
    month: 'February',
    membersAtStart: 51,
    membersLost: 4,
    membersAdded: 8,
    churnPercent: 7.8,
    details: [
      { name: '', reasonLeft: '', primaryInstructor: '', exitInterview: '' },
      { name: '', reasonLeft: '', primaryInstructor: '', exitInterview: '' },
      { name: '', reasonLeft: '', primaryInstructor: '', exitInterview: '' },
      { name: '', reasonLeft: '', primaryInstructor: '', exitInterview: '' },
    ],
  },
];

const churnChartData = [
  { month: 'Jan', churnRate: 10.0, membersLost: 5 },
  { month: 'Feb', churnRate: 7.8, membersLost: 4 },
  { month: 'Mar', churnRate: 9.2, membersLost: 6 },
  { month: 'Apr', churnRate: 6.5, membersLost: 4 },
  { month: 'May', churnRate: 8.2, membersLost: 5 },
];

export const Churn = () => {
  return (
    <div className='space-y-10'>
      {/* Churn Rate Chart */}
      <div className='rounded-lg bg-white shadow-sm ring-1 ring-gray-200'>
        <div className='border-b border-gray-100 p-6'>
          <h3 className='text-lg font-semibold text-gray-900'>
            Monthly Churn Rate Trend
          </h3>
        </div>
        <div className='p-6'>
          <ResponsiveContainer width='100%' height={300}>
            <LineChart data={churnChartData}>
              <CartesianGrid strokeDasharray='3 3' stroke='#f3f4f6' />
              <XAxis dataKey='month' stroke='#6b7280' />
              <YAxis stroke='#6b7280' />
              <Tooltip
                formatter={(value, name) => [
                  name === 'churnRate' ? `${String(value)}%` : value,
                  name === 'churnRate' ? 'Churn Rate' : 'Members Lost',
                ]}
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                }}
              />
              <Legend />
              <Line
                type='monotone'
                dataKey='churnRate'
                stroke='#ef4444'
                strokeWidth={3}
                name='Churn Rate %'
              />
              <Bar dataKey='membersLost' fill='#f87171' name='Members Lost' />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      <div className='rounded-lg bg-white shadow-sm ring-1 ring-gray-200'>
        <div className='border-b border-gray-100 p-6'>
          <h3 className='text-lg font-semibold text-gray-900'>
            Monthly Churn Summary
          </h3>
        </div>
        <div className='p-6'>
          <Table>
            <TableHeader>
              <TableRow className='bg-gray-50'>
                <TableHead className='font-semibold text-gray-700'>
                  Month
                </TableHead>
                <TableHead className='text-right font-semibold text-gray-700'>
                  Members at Start
                </TableHead>
                <TableHead className='text-right font-semibold text-gray-700'>
                  Members Lost
                </TableHead>
                <TableHead className='text-right font-semibold text-gray-700'>
                  Members Added
                </TableHead>
                <TableHead className='text-right font-semibold text-gray-700'>
                  Churn %
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {churnData.map((row, index) => (
                <TableRow key={index} className='even:bg-gray-50'>
                  <TableCell className='font-medium text-gray-900'>
                    {row.month}
                  </TableCell>
                  <TableCell className='text-right'>
                    <input
                      type='number'
                      defaultValue={row.membersAtStart}
                      className='w-full rounded border-none bg-transparent px-1 text-right text-gray-900 outline-none focus:bg-indigo-50'
                    />
                  </TableCell>
                  <TableCell className='text-right'>
                    <input
                      type='number'
                      defaultValue={row.membersLost}
                      className='w-full rounded border-none bg-transparent px-1 text-right text-gray-900 outline-none focus:bg-indigo-50'
                    />
                  </TableCell>
                  <TableCell className='text-right'>
                    <input
                      type='number'
                      defaultValue={row.membersAdded}
                      className='w-full rounded border-none bg-transparent px-1 text-right text-gray-900 outline-none focus:bg-indigo-50'
                    />
                  </TableCell>
                  <TableCell className='text-right font-semibold text-gray-900'>
                    {row.churnPercent}%
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {churnData.map((monthData, monthIndex) => (
        <Collapsible key={monthIndex}>
          <CollapsibleTrigger asChild>
            <Button
              variant='outline'
              className='w-full justify-between bg-white text-gray-900 ring-1 ring-gray-200 hover:bg-gray-50'
            >
              {monthData.month} Churn Detail
              <ChevronDown className='h-4 w-4' />
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <div className='mt-2 rounded-lg bg-white shadow-sm ring-1 ring-gray-200'>
              <div className='p-6'>
                <Table>
                  <TableHeader>
                    <TableRow className='bg-gray-50'>
                      <TableHead className='font-semibold text-gray-700'>
                        Name
                      </TableHead>
                      <TableHead className='font-semibold text-gray-700'>
                        Reason Left
                      </TableHead>
                      <TableHead className='font-semibold text-gray-700'>
                        Primary Instructor
                      </TableHead>
                      <TableHead className='font-semibold text-gray-700'>
                        Exit Interview?
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {monthData.details.map((detail, detailIndex) => (
                      <TableRow key={detailIndex} className='even:bg-gray-50'>
                        <TableCell>
                          <input
                            type='text'
                            defaultValue={detail.name}
                            placeholder='Enter name'
                            className='w-full rounded border-none bg-transparent px-1 text-gray-900 outline-none focus:bg-indigo-50'
                          />
                        </TableCell>
                        <TableCell>
                          <input
                            type='text'
                            defaultValue={detail.reasonLeft}
                            placeholder='Enter reason'
                            className='w-full rounded border-none bg-transparent px-1 text-gray-900 outline-none focus:bg-indigo-50'
                          />
                        </TableCell>
                        <TableCell>
                          <input
                            type='text'
                            defaultValue={detail.primaryInstructor}
                            placeholder='Enter instructor'
                            className='w-full rounded border-none bg-transparent px-1 text-gray-900 outline-none focus:bg-indigo-50'
                          />
                        </TableCell>
                        <TableCell>
                          <input
                            type='text'
                            defaultValue={detail.exitInterview}
                            placeholder='Yes/No'
                            className='w-full rounded border-none bg-transparent px-1 text-gray-900 outline-none focus:bg-indigo-50'
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
      ))}
    </div>
  );
};
