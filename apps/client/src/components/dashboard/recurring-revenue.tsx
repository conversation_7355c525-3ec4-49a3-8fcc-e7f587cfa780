import {
  Cartesian<PERSON>rid,
  Legend,
  Line,
  <PERSON><PERSON><PERSON>,
  ResponsiveC<PERSON>r,
  <PERSON>lt<PERSON>,
  XAxis,
  YAxis,
} from 'recharts';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';

const recurringRevenueData = {
  goalTable: [
    {
      stripeAccount: 'Main',
      product: 'Core Tuition 1×/week',
      activeSubscribers: 98,
      failedPayments: 3,
      currentSubs: 95,
      monthlyPrice: 129,
      expectedMRR: 12250,
    },
    {
      stripeAccount: 'Main',
      product: 'Premium Tuition 2×/week',
      activeSubscribers: 45,
      failedPayments: 1,
      currentSubs: 44,
      monthlyPrice: 199,
      expectedMRR: 8756,
    },
    {
      stripeAccount: 'Secondary',
      product: 'Group Sessions',
      activeSubscribers: 32,
      failedPayments: 2,
      currentSubs: 30,
      monthlyPrice: 79,
      expectedMRR: 2370,
    },
  ],
  weeklyDashboard: [
    {
      weekOf: '02/22/24',
      stripeAccount: 'Main',
      product: 'Core Tuition 1×/week',
      activeSubscribers: 98,
      failedPayments: 3,
      currentSubs: 95,
      monthlyPrice: 129,
      expectedMRR: 12250,
    },
  ],
  chartData: [
    { month: 'Jan', mrr: 38500 },
    { month: 'Feb', mrr: 41200 },
    { month: 'Mar', mrr: 43800 },
    { month: 'Apr', mrr: 44100 },
    { month: 'May', mrr: 45250 },
  ],
};

export const RecurringRevenue = () => {
  return (
    <div className='space-y-10'>
      {/* MRR Trend Chart */}
      <div className='rounded-lg bg-white shadow-sm ring-1 ring-gray-200'>
        <div className='border-b border-gray-100 p-6'>
          <h3 className='text-lg font-semibold text-gray-900'>
            Monthly Recurring Revenue Trend
          </h3>
        </div>
        <div className='p-6'>
          <ResponsiveContainer width='100%' height={300}>
            <LineChart data={recurringRevenueData.chartData}>
              <CartesianGrid strokeDasharray='3 3' stroke='#f3f4f6' />
              <XAxis dataKey='month' stroke='#6b7280' />
              <YAxis stroke='#6b7280' />
              <Tooltip
                formatter={(value) => [`$${value.toLocaleString()}`, 'MRR']}
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                }}
              />
              <Legend />
              <Line
                type='monotone'
                dataKey='mrr'
                stroke='#4f46e5'
                strokeWidth={3}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      <div className='rounded-lg bg-white shadow-sm ring-1 ring-gray-200'>
        <div className='border-b border-gray-100 p-6'>
          <h3 className='text-xl font-semibold text-gray-900'>THE GOAL</h3>
        </div>
        <div className='p-6'>
          <div className='overflow-x-auto'>
            <Table>
              <TableHeader>
                <TableRow className='bg-gray-50'>
                  <TableHead className='font-semibold text-gray-700'>
                    Stripe Account
                  </TableHead>
                  <TableHead className='font-semibold text-gray-700'>
                    Product
                  </TableHead>
                  <TableHead className='text-right font-semibold text-gray-700'>
                    Active Subscribers
                  </TableHead>
                  <TableHead className='text-right font-semibold text-gray-700'>
                    Failed Payments
                  </TableHead>
                  <TableHead className='text-right font-semibold text-gray-700'>
                    Current Subs
                  </TableHead>
                  <TableHead className='text-right font-semibold text-gray-700'>
                    Monthly Price
                  </TableHead>
                  <TableHead className='text-right font-semibold text-gray-700'>
                    Expected MRR
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recurringRevenueData.goalTable.map((row, index) => (
                  <TableRow key={index} className='even:bg-gray-50'>
                    <TableCell>
                      <input
                        type='text'
                        defaultValue={row.stripeAccount}
                        className='w-full rounded border-none bg-transparent px-1 text-gray-900 outline-none focus:bg-indigo-50'
                      />
                    </TableCell>
                    <TableCell>
                      <input
                        type='text'
                        defaultValue={row.product}
                        className='w-full rounded border-none bg-transparent px-1 text-gray-900 outline-none focus:bg-indigo-50'
                      />
                    </TableCell>
                    <TableCell className='text-right'>
                      <input
                        type='number'
                        defaultValue={row.activeSubscribers}
                        className='w-full rounded border-none bg-transparent px-1 text-right text-gray-900 outline-none focus:bg-indigo-50'
                      />
                    </TableCell>
                    <TableCell className='text-right'>
                      <input
                        type='number'
                        defaultValue={row.failedPayments}
                        className='w-full rounded border-none bg-transparent px-1 text-right text-gray-900 outline-none focus:bg-indigo-50'
                      />
                    </TableCell>
                    <TableCell className='text-right'>
                      <input
                        type='number'
                        defaultValue={row.currentSubs}
                        className='w-full rounded border-none bg-transparent px-1 text-right text-gray-900 outline-none focus:bg-indigo-50'
                      />
                    </TableCell>
                    <TableCell className='text-right'>
                      <input
                        type='number'
                        defaultValue={row.monthlyPrice}
                        className='w-full rounded border-none bg-transparent px-1 text-right text-gray-900 outline-none focus:bg-indigo-50'
                      />
                    </TableCell>
                    <TableCell className='text-right font-semibold text-gray-900'>
                      ${row.expectedMRR.toLocaleString()}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>

      <hr className='border-gray-200' />

      <div className='rounded-lg bg-white shadow-sm ring-1 ring-gray-200'>
        <div className='border-b border-gray-100 p-6'>
          <h3 className='text-lg font-semibold text-gray-900'>
            Weekly Dashboard
          </h3>
        </div>
        <div className='p-6'>
          <div className='overflow-x-auto'>
            <Table>
              <TableHeader>
                <TableRow className='bg-gray-50'>
                  <TableHead className='font-semibold text-gray-700'>
                    Week of
                  </TableHead>
                  <TableHead className='font-semibold text-gray-700'>
                    Stripe Account
                  </TableHead>
                  <TableHead className='font-semibold text-gray-700'>
                    Product
                  </TableHead>
                  <TableHead className='text-right font-semibold text-gray-700'>
                    Active Subscribers
                  </TableHead>
                  <TableHead className='text-right font-semibold text-gray-700'>
                    Failed Payments
                  </TableHead>
                  <TableHead className='text-right font-semibold text-gray-700'>
                    Current Subs
                  </TableHead>
                  <TableHead className='text-right font-semibold text-gray-700'>
                    Monthly Price
                  </TableHead>
                  <TableHead className='text-right font-semibold text-gray-700'>
                    Expected MRR
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recurringRevenueData.weeklyDashboard.map((row, index) => (
                  <TableRow key={index} className='even:bg-gray-50'>
                    <TableCell>
                      <input
                        type='text'
                        defaultValue={row.weekOf}
                        className='w-full rounded border-none bg-transparent px-1 text-gray-900 outline-none focus:bg-indigo-50'
                      />
                    </TableCell>
                    <TableCell>
                      <input
                        type='text'
                        defaultValue={row.stripeAccount}
                        className='w-full rounded border-none bg-transparent px-1 text-gray-900 outline-none focus:bg-indigo-50'
                      />
                    </TableCell>
                    <TableCell>
                      <input
                        type='text'
                        defaultValue={row.product}
                        className='w-full rounded border-none bg-transparent px-1 text-gray-900 outline-none focus:bg-indigo-50'
                      />
                    </TableCell>
                    <TableCell className='text-right'>
                      <input
                        type='number'
                        defaultValue={row.activeSubscribers}
                        className='w-full rounded border-none bg-transparent px-1 text-right text-gray-900 outline-none focus:bg-indigo-50'
                      />
                    </TableCell>
                    <TableCell className='text-right'>
                      <input
                        type='number'
                        defaultValue={row.failedPayments}
                        className='w-full rounded border-none bg-transparent px-1 text-right text-gray-900 outline-none focus:bg-indigo-50'
                      />
                    </TableCell>
                    <TableCell className='text-right'>
                      <input
                        type='number'
                        defaultValue={row.currentSubs}
                        className='w-full rounded border-none bg-transparent px-1 text-right text-gray-900 outline-none focus:bg-indigo-50'
                      />
                    </TableCell>
                    <TableCell className='text-right'>
                      <input
                        type='number'
                        defaultValue={row.monthlyPrice}
                        className='w-full rounded border-none bg-transparent px-1 text-right text-gray-900 outline-none focus:bg-indigo-50'
                      />
                    </TableCell>
                    <TableCell className='text-right font-semibold text-gray-900'>
                      ${row.expectedMRR.toLocaleString()}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
    </div>
  );
};
