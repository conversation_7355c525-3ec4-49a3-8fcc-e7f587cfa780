import { ChevronDown } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  showDropdown?: boolean;
  dropdownValue?: string;
}

export function StatsCard({
  title,
  value,
  icon,
  showDropdown = false,
  dropdownValue = 'This Week',
}: StatsCardProps) {
  return (
    <div className='rounded-lg border border-gray-200 bg-white p-6'>
      <div className='mb-4 flex items-center justify-between'>
        <div className='flex items-center gap-3'>
          <div className='rounded-lg bg-gray-50 p-2'>{icon}</div>
          <span className='font-medium text-gray-600'>{title}</span>
        </div>
        {showDropdown && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='ghost' className='text-sm text-gray-500'>
                {dropdownValue}
                <ChevronDown className='ml-1 h-4 w-4' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem>This Week</DropdownMenuItem>
              <DropdownMenuItem>Last Week</DropdownMenuItem>
              <DropdownMenuItem>This Month</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
      <div className='text-4xl font-bold text-gray-900'>{value}</div>
    </div>
  );
}
