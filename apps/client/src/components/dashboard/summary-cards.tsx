const summaryData = {
  currentMRR: 45250,
  activeSubscribers: 187,
  avgCostPerLead: 23.5,
  monthlyChurnPercent: 8.2,
};

export const SummaryCards = () => {
  return (
    <div className='mb-10 grid grid-cols-2 gap-6 lg:grid-cols-4'>
      <div className='space-y-1 rounded-xl bg-white p-6 shadow-sm ring-1 ring-gray-200 transition hover:bg-indigo-50'>
        <div className='text-sm font-medium text-gray-600'>Current MRR</div>
        <div className='text-2xl font-semibold text-gray-900'>
          ${summaryData.currentMRR.toLocaleString()}
        </div>
      </div>
      <div className='space-y-1 rounded-xl bg-white p-6 shadow-sm ring-1 ring-gray-200 transition hover:bg-indigo-50'>
        <div className='text-sm font-medium text-gray-600'>
          Active Subscribers
        </div>
        <div className='text-2xl font-semibold text-gray-900'>
          {summaryData.activeSubscribers}
        </div>
      </div>
      <div className='space-y-1 rounded-xl bg-white p-6 shadow-sm ring-1 ring-gray-200 transition hover:bg-indigo-50'>
        <div className='text-sm font-medium text-gray-600'>
          Avg. Cost Per Lead
        </div>
        <div className='text-2xl font-semibold text-gray-900'>
          ${summaryData.avgCostPerLead}
        </div>
      </div>
      <div className='space-y-1 rounded-xl bg-white p-6 shadow-sm ring-1 ring-gray-200 transition hover:bg-indigo-50'>
        <div className='text-sm font-medium text-gray-600'>Monthly Churn %</div>
        <div className='text-2xl font-semibold text-gray-900'>
          {summaryData.monthlyChurnPercent}%
        </div>
      </div>
    </div>
  );
};
