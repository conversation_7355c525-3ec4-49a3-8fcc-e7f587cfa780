import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>A<PERSON>s,
  YAxi<PERSON>,
} from 'recharts';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';

const upgradesData = [
  {
    weekOf: '05/20/24',
    entries: [
      {
        name: '<PERSON>',
        oldProduct: 'Core 1×/week',
        oldMonthlyPrice: 129,
        newProduct: 'Premium 2×/week',
        newMonthlyPrice: 199,
        additionalMRR: 70,
      },
      {
        name: '<PERSON>',
        oldProduct: 'Group Sessions',
        oldMonthlyPrice: 79,
        newProduct: 'Core 1×/week',
        newMonthlyPrice: 129,
        additionalMRR: 50,
      },
    ],
  },
  {
    weekOf: '05/13/24',
    entries: [
      {
        name: '<PERSON>',
        oldProduct: 'Core 1×/week',
        oldMonthlyPrice: 129,
        newProduct: 'Premium 2×/week',
        newMonthlyPrice: 199,
        additionalMRR: 70,
      },
    ],
  },
];

const upgradesChartData = [
  { week: '05/06', additionalMRR: 90 },
  { week: '05/13', additionalMRR: 70 },
  { week: '05/20', additionalMRR: 120 },
  { week: '05/27', additionalMRR: 85 },
];

export const Upgrades = () => {
  return (
    <div className='space-y-10'>
      {/* Upgrades Chart */}
      <div className='rounded-lg bg-white shadow-sm ring-1 ring-gray-200'>
        <div className='border-b border-gray-100 p-6'>
          <h3 className='text-lg font-semibold text-gray-900'>
            Weekly Additional MRR from Upgrades
          </h3>
        </div>
        <div className='p-6'>
          <ResponsiveContainer width='100%' height={300}>
            <BarChart data={upgradesChartData}>
              <CartesianGrid strokeDasharray='3 3' stroke='#f3f4f6' />
              <XAxis dataKey='week' stroke='#6b7280' />
              <YAxis stroke='#6b7280' />
              <Tooltip
                formatter={(value: number) => [`$${value}`, 'Additional MRR']}
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                }}
              />
              <Legend />
              <Bar
                dataKey='additionalMRR'
                fill='#10b981'
                name='Additional MRR'
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {upgradesData.map((weekData, weekIndex) => {
        const totalAdditionalMRR = weekData.entries.reduce(
          (sum, entry) => sum + entry.additionalMRR,
          0,
        );

        return (
          <div
            key={weekIndex}
            className='rounded-lg bg-white shadow-sm ring-1 ring-gray-200'
          >
            <div className='border-b border-gray-100 p-6'>
              <h3 className='text-lg font-semibold text-gray-900'>
                Week of {weekData.weekOf}
              </h3>
            </div>
            <div className='p-6'>
              <Table>
                <TableHeader>
                  <TableRow className='bg-gray-50'>
                    <TableHead className='font-semibold text-gray-700'>
                      Name
                    </TableHead>
                    <TableHead className='font-semibold text-gray-700'>
                      Old Product
                    </TableHead>
                    <TableHead className='text-right font-semibold text-gray-700'>
                      Old Monthly Price
                    </TableHead>
                    <TableHead className='font-semibold text-gray-700'>
                      New Product
                    </TableHead>
                    <TableHead className='text-right font-semibold text-gray-700'>
                      New Monthly Price
                    </TableHead>
                    <TableHead className='text-right font-semibold text-gray-700'>
                      Additional MRR
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {weekData.entries.map((entry, entryIndex) => (
                    <TableRow key={entryIndex} className='even:bg-gray-50'>
                      <TableCell>
                        <input
                          type='text'
                          defaultValue={entry.name}
                          className='w-full rounded border-none bg-transparent px-1 text-gray-900 outline-none focus:bg-indigo-50'
                        />
                      </TableCell>
                      <TableCell>
                        <input
                          type='text'
                          defaultValue={entry.oldProduct}
                          className='w-full rounded border-none bg-transparent px-1 text-gray-900 outline-none focus:bg-indigo-50'
                        />
                      </TableCell>
                      <TableCell className='text-right'>
                        <input
                          type='number'
                          defaultValue={entry.oldMonthlyPrice}
                          className='w-full rounded border-none bg-transparent px-1 text-right text-gray-900 outline-none focus:bg-indigo-50'
                        />
                      </TableCell>
                      <TableCell>
                        <input
                          type='text'
                          defaultValue={entry.newProduct}
                          className='w-full rounded border-none bg-transparent px-1 text-gray-900 outline-none focus:bg-indigo-50'
                        />
                      </TableCell>
                      <TableCell className='text-right'>
                        <input
                          type='number'
                          defaultValue={entry.newMonthlyPrice}
                          className='w-full rounded border-none bg-transparent px-1 text-right text-gray-900 outline-none focus:bg-indigo-50'
                        />
                      </TableCell>
                      <TableCell className='text-right font-semibold'>
                        <input
                          type='number'
                          defaultValue={entry.additionalMRR}
                          className='w-full rounded border-none bg-transparent px-1 text-right font-semibold text-gray-900 outline-none focus:bg-indigo-50'
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              <div className='mt-4 text-right'>
                <div className='inline-block rounded bg-gray-900 px-4 py-2 font-semibold text-white'>
                  Total Additional MRR: ${totalAdditionalMRR}
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};
