'use client';

import { Calendar, DollarSign, FileText, User } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { useInvoices } from '@/hooks/useInvoices';

interface Invoice {
  id: string;
  status: string;
  type: string;
  final_amount: string;
  created_at: string;
  user?: {
    first_name: string;
    last_name: string;
    email: string;
  };
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

interface InvoicesResponse {
  invoices: Array<Invoice>;
  pagination: Pagination;
}

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'paid':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'failed':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'partially_paid':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getTypeIcon = (type: string) => {
  switch (type.toLowerCase()) {
    case 'subscription':
      return <Calendar className='h-4 w-4' />;
    case 'one_time':
      return <FileText className='h-4 w-4' />;
    default:
      return <FileText className='h-4 w-4' />;
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
};

const formatAmount = (amount: string) => {
  const numAmount = parseFloat(amount);
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(numAmount);
};

export const InvoiceList = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const {
    data: invoicesData,
    isLoading,
    error,
  } = useInvoices({
    page: currentPage,
    limit: 10,
    status: statusFilter === 'all' ? undefined : statusFilter,
  }) as {
    data: InvoicesResponse | undefined;
    isLoading: boolean;
    error: Error | null;
  };

  const invoices = invoicesData?.invoices ?? [];
  const pagination = invoicesData?.pagination;

  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    setCurrentPage(1); // Reset to first page when filtering
  };

  if (isLoading) {
    return (
      <div className='flex items-center justify-center p-8'>
        <div className='text-center'>
          <div className='mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600'></div>
          <p className='mt-2 text-gray-600'>Loading invoices...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='flex items-center justify-center p-8'>
        <div className='text-center'>
          <p className='text-red-600'>
            Error loading invoices: {error.message}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-2xl font-bold text-gray-900'>Recent Invoices</h2>
          <p className='text-gray-600'>
            {pagination?.total ?? 0} total invoices
          </p>
        </div>
        <div className='flex items-center gap-4'>
          <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
            <SelectTrigger className='w-48'>
              <SelectValue placeholder='Filter by status' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All Statuses</SelectItem>
              <SelectItem value='paid'>Paid</SelectItem>
              <SelectItem value='pending'>Pending</SelectItem>
              <SelectItem value='failed'>Failed</SelectItem>
              <SelectItem value='partially_paid'>Partially Paid</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Invoices Table */}
      <Card>
        <CardContent className='p-0'>
          <div className='overflow-x-auto'>
            <table className='w-full'>
              <thead>
                <tr className='border-b border-gray-200'>
                  <th className='px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase'>
                    Client
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase'>
                    Type
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase'>
                    Amount
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase'>
                    Status
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase'>
                    Date
                  </th>
                </tr>
              </thead>
              <tbody className='divide-y divide-gray-200 bg-white'>
                {invoices.length === 0 ? (
                  <tr>
                    <td
                      colSpan={5}
                      className='px-6 py-12 text-center text-gray-500'
                    >
                      <FileText className='mx-auto mb-4 h-12 w-12 text-gray-300' />
                      <p className='text-lg font-medium'>No invoices found</p>
                      <p className='text-sm'>
                        {statusFilter
                          ? `No invoices with status "${statusFilter}"`
                          : 'Create your first invoice to get started'}
                      </p>
                    </td>
                  </tr>
                ) : (
                  invoices.map((invoice: Invoice) => (
                    <tr key={invoice.id} className='hover:bg-gray-50'>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='flex items-center'>
                          <div className='h-8 w-8 flex-shrink-0'>
                            <div className='flex h-8 w-8 items-center justify-center rounded-full bg-blue-100'>
                              <User className='h-4 w-4 text-blue-600' />
                            </div>
                          </div>
                          <div className='ml-4'>
                            <div className='text-sm font-medium text-gray-900'>
                              {invoice.user
                                ? `${invoice.user.first_name} ${invoice.user.last_name}`
                                : 'Unknown Client'}
                            </div>
                            <div className='text-sm text-gray-500'>
                              {invoice.user?.email ?? 'No email'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='flex items-center'>
                          {getTypeIcon(invoice.type)}
                          <span className='ml-2 text-sm text-gray-900 capitalize'>
                            {invoice.type.replace('_', ' ')}
                          </span>
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='flex items-center'>
                          <DollarSign className='mr-1 h-4 w-4 text-gray-400' />
                          <span className='text-sm font-medium text-gray-900'>
                            {formatAmount(invoice.final_amount)}
                          </span>
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <span
                          className={`inline-flex rounded-full border px-2 py-1 text-xs font-semibold ${getStatusColor(
                            invoice.status,
                          )}`}
                        >
                          {invoice.status.replace('_', ' ').toUpperCase()}
                        </span>
                      </td>
                      <td className='px-6 py-4 text-sm whitespace-nowrap text-gray-500'>
                        {formatDate(invoice.created_at)}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className='flex items-center justify-between'>
          <div className='text-sm text-gray-700'>
            Showing {(pagination.page - 1) * pagination.limit + 1} to{' '}
            {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
            {pagination.total} results
          </div>
          <div className='flex items-center space-x-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => {
                setCurrentPage(currentPage - 1);
              }}
              disabled={!pagination.hasPrevPage}
            >
              Previous
            </Button>
            <span className='text-sm text-gray-700'>
              Page {pagination.page} of {pagination.totalPages}
            </span>
            <Button
              variant='outline'
              size='sm'
              onClick={() => {
                setCurrentPage(currentPage + 1);
              }}
              disabled={!pagination.hasNextPage}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
