import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';

import { cn } from '@/lib/utils';

import { type LessonSchedulingFormData } from './lesson-scheduling-form';

interface StepOneProps {
  formData: {
    lessonType: string;
    customEventType?: string;
    eventDate: Date | null;
    eventHour: string;
    eventMinute: string;
    eventAmPm: string;
    isRecurring: boolean;
    repeatFrequency?: string;
    repeatDays?: Array<string>;
    endType?: 'never' | 'on' | 'after';
    endDate?: Date | null;
    endOccurrences?: number | null;
  };
  errors: Record<string, string>;
  updateFormData: (key: keyof LessonSchedulingFormData, value: unknown) => void;
}

export const StepFirst = ({
  formData,
  errors,
  updateFormData,
}: StepOneProps) => {
  return (
    <div className='space-y-4'>
      <h3 className='text-lg font-semibold'>Event Information</h3>

      <div className='space-y-2'>
        <Label htmlFor='lessonType'>Select Event Type *</Label>
        <Select
          value={formData.lessonType}
          onValueChange={(value) => {
            updateFormData('lessonType', value);
          }}
        >
          <SelectTrigger
            className={cn('w-full', errors.lessonType && 'border-red-500')}
          >
            <SelectValue placeholder='Select lesson type' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='intro-lesson'>Intro Lesson</SelectItem>
            <SelectItem value='lesson-series'>Lesson Series</SelectItem>
            <SelectItem value='camp'>Camp</SelectItem>
            <SelectItem value='custom-event'>Create Custom Event</SelectItem>
          </SelectContent>
        </Select>
        {errors.lessonType && (
          <p className='text-xs text-red-500'>{errors.lessonType}</p>
        )}
      </div>

      {formData.lessonType === 'custom-event' && (
        <div className='space-y-2'>
          <Label htmlFor='customEventType'>Custom Event Type *</Label>
          <Input
            id='customEventType'
            value={formData.customEventType}
            onChange={(e) => {
              updateFormData('customEventType', e.target.value);
            }}
            placeholder='Enter custom event type'
            className={cn(errors.customEventType && 'border-red-500')}
          />
        </div>
      )}

      {/* Date and Time Grid */}
      <div className='grid grid-cols-2 gap-4'>
        <div className='space-y-2'>
          <Label htmlFor='eventDate'>Event Date *</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                id='eventDate'
                variant='outline'
                className={cn(
                  'w-full justify-start text-left font-normal',
                  !formData.eventDate && 'text-muted-foreground',
                  errors.eventDate && 'border-red-500',
                )}
              >
                <CalendarIcon className='mr-2 h-4 w-4' />
                {formData.eventDate ? (
                  format(formData.eventDate, 'PPP')
                ) : (
                  <span>Select date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className='w-auto p-0'>
              <Calendar
                mode='single'
                selected={formData.eventDate ?? undefined}
                onSelect={(date) => {
                  if (date) {
                    updateFormData('eventDate', date);
                  }
                }}
                autoFocus
              />
            </PopoverContent>
          </Popover>
          {errors.eventDate && (
            <p className='text-xs text-red-500'>{errors.eventDate}</p>
          )}
        </div>

        <div className='space-y-2'>
          <Label htmlFor='eventTime'>Event Time *</Label>
          <div className='flex items-center gap-2'>
            <Select
              value={formData.eventHour}
              onValueChange={(value) => {
                updateFormData('eventHour', value);
              }}
            >
              <SelectTrigger
                className={cn('w-20', errors.eventHour && 'border-red-500')}
              >
                <SelectValue placeholder='Hour' />
              </SelectTrigger>
              <SelectContent className='max-h-72 min-w-20'>
                {Array.from({ length: 12 }, (_, i) => i + 1).map((hour) => {
                  const formatted = hour.toString().padStart(2, '0');
                  return (
                    <SelectItem key={formatted} value={formatted}>
                      {formatted}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>

            <span className='text-muted-foreground'>:</span>

            <Select
              value={formData.eventMinute}
              onValueChange={(value) => {
                updateFormData('eventMinute', value);
              }}
            >
              <SelectTrigger
                className={cn('w-20', errors.eventMinute && 'border-red-500')}
              >
                <SelectValue placeholder='Min' />
              </SelectTrigger>
              <SelectContent className='max-h-72 min-w-20'>
                {[0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55].map(
                  (minute) => {
                    const formatted = minute.toString().padStart(2, '0');
                    return (
                      <SelectItem key={formatted} value={formatted}>
                        {formatted}
                      </SelectItem>
                    );
                  },
                )}
              </SelectContent>
            </Select>

            <Select
              value={formData.eventAmPm}
              onValueChange={(value) => {
                updateFormData('eventAmPm', value);
              }}
            >
              <SelectTrigger
                className={cn('w-20', errors.eventAmPm && 'border-red-500')}
              >
                <SelectValue placeholder='AM/PM' />
              </SelectTrigger>
              <SelectContent className='max-h-72 min-w-20'>
                <SelectItem value='AM'>AM</SelectItem>
                <SelectItem value='PM'>PM</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {(errors.eventHour || errors.eventMinute || errors.eventAmPm) && (
            <p className='text-xs text-red-500'>Please select a valid time</p>
          )}
        </div>
      </div>

      {/* Recurring Section */}
      <div className='space-y-4 rounded-md border p-4'>
        <div className='flex items-center justify-between'>
          <Label htmlFor='isRecurring' className='font-medium'>
            Make this a recurring lesson
          </Label>
          <Switch
            id='isRecurring'
            checked={formData.isRecurring}
            onCheckedChange={(checked) => {
              updateFormData('isRecurring', checked);
            }}
          />
        </div>

        {formData.isRecurring && (
          <div className='space-y-4 pt-2'>
            <div className='space-y-2'>
              <Label htmlFor='repeatFrequency'>Repeat every</Label>
              <Select
                value={formData.repeatFrequency}
                onValueChange={(value) => {
                  updateFormData('repeatFrequency', value);
                }}
              >
                <SelectTrigger
                  className={cn(
                    'w-full',
                    errors.repeatFrequency && 'border-red-500',
                  )}
                >
                  <SelectValue placeholder='Select frequency' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='every-day'>Every Day</SelectItem>
                  <SelectItem value='week'>Week</SelectItem>
                  <SelectItem value='2weeks'>2 Weeks</SelectItem>
                  <SelectItem value='month'>Month</SelectItem>
                </SelectContent>
              </Select>
              {errors.repeatFrequency && (
                <p className='text-xs text-red-500'>{errors.repeatFrequency}</p>
              )}
            </div>

            <div className='space-y-2'>
              <Label className='inline-block'>Repeat on</Label>
              <ToggleGroup
                type='multiple'
                variant='outline'
                className='justify-between'
                value={formData.repeatDays}
                onValueChange={(value) => {
                  updateFormData('repeatDays', value);
                }}
              >
                <ToggleGroupItem
                  value='sun'
                  aria-label='Sunday'
                  className='data-[state=on]:bg-primary cursor-pointer transition-colors data-[state=on]:text-white'
                >
                  S
                </ToggleGroupItem>
                <ToggleGroupItem
                  value='mon'
                  aria-label='Monday'
                  className='data-[state=on]:bg-primary cursor-pointer transition-colors data-[state=on]:text-white'
                >
                  M
                </ToggleGroupItem>
                <ToggleGroupItem
                  value='tue'
                  aria-label='Tuesday'
                  className='data-[state=on]:bg-primary cursor-pointer transition-colors data-[state=on]:text-white'
                >
                  T
                </ToggleGroupItem>
                <ToggleGroupItem
                  value='wed'
                  aria-label='Wednesday'
                  className='data-[state=on]:bg-primary cursor-pointer transition-colors data-[state=on]:text-white'
                >
                  W
                </ToggleGroupItem>
                <ToggleGroupItem
                  value='thu'
                  aria-label='Thursday'
                  className='data-[state=on]:bg-primary cursor-pointer transition-colors data-[state=on]:text-white'
                >
                  T
                </ToggleGroupItem>
                <ToggleGroupItem
                  value='fri'
                  aria-label='Friday'
                  className='data-[state=on]:bg-primary cursor-pointer transition-colors data-[state=on]:text-white'
                >
                  F
                </ToggleGroupItem>
                <ToggleGroupItem
                  value='sat'
                  aria-label='Saturday'
                  className='data-[state=on]:bg-primary cursor-pointer transition-colors data-[state=on]:text-white'
                >
                  S
                </ToggleGroupItem>
              </ToggleGroup>
              {errors.repeatDays && (
                <p className='text-xs text-red-500'>
                  Please select at least one day
                </p>
              )}
            </div>

            <div className='space-y-2'>
              <Label>Ends</Label>
              <RadioGroup
                value={formData.endType}
                onValueChange={(value) => {
                  updateFormData('endType', value);
                }}
                className='space-y-3'
              >
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='never' id='never' />
                  <Label htmlFor='never'>Never</Label>
                </div>

                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='on' id='on' />
                  <Label htmlFor='on'>On</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'ml-2 w-[180px] justify-start text-left font-normal',
                          !formData.endDate && 'text-muted-foreground',
                          formData.endType !== 'on' && 'opacity-50',
                          errors.endDate && formData.endType === 'on'
                            ? 'border-red-500'
                            : '',
                        )}
                        disabled={formData.endType !== 'on'}
                      >
                        <CalendarIcon className='mr-2 h-4 w-4' />
                        {formData.endDate ? (
                          format(formData.endDate, 'PPP')
                        ) : (
                          <span>Select end date</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className='w-auto p-0'>
                      <Calendar
                        mode='single'
                        selected={formData.endDate ?? undefined}
                        onSelect={(date) => {
                          if (date) {
                            updateFormData('endDate', date);
                          }
                        }}
                        disabled={formData.endType !== 'on'}
                        autoFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='after' id='after' />
                  <Label htmlFor='after'>After</Label>
                  <Input
                    type='number'
                    className={cn(
                      'ml-2 w-20',
                      formData.endType !== 'after' && 'opacity-50',
                      errors.endOccurrences &&
                        formData.endType === 'after' &&
                        'border-red-500',
                    )}
                    value={formData.endOccurrences?.toString() ?? '1'}
                    onChange={(e) => {
                      updateFormData(
                        'endOccurrences',
                        parseInt(e.target.value) || 1,
                      );
                    }}
                    disabled={formData.endType !== 'after'}
                    min={1}
                  />
                  <span className='text-muted-foreground'>occurrences</span>
                </div>
              </RadioGroup>
              {errors.endDate && formData.endType === 'on' && (
                <p className='text-xs text-red-500'>
                  Please select an end date
                </p>
              )}
              {errors.endOccurrences && formData.endType === 'after' && (
                <p className='text-xs text-red-500'>
                  Please enter a valid number of occurrences
                </p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
