import { useEffect, useState } from 'react';
import { type LessonType } from 'shared/src/types/lessons';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';

import { useCreateLesson } from '@/hooks/useCreateLesson';

import { StepFirst } from './step-one';
import { StepThree } from './step-three';
import { StepTwo } from './step-two';

// Define the validation schema using Zod
const lessonSchedulingSchema = z.object({
  // Step 1: Personal Info
  eventName: z
    .string()
    .min(3, { message: 'Event name must be at least 3 characters.' })
    .max(100, { message: 'Event name must not exceed 100 characters.' }),
  eventDate: z.date({ required_error: 'Event date is required.' }).nullable(),
  eventHour: z.string().min(1, { message: 'Hour is required.' }),
  eventMinute: z.string().min(1, { message: 'Minute is required.' }),
  eventAmPm: z.string().min(1, { message: 'AM/PM is required.' }),

  // Recurring options
  isRecurring: z.boolean().default(false),
  repeatFrequency: z.string().optional(),
  repeatDays: z.array(z.string()).optional(),
  endType: z.enum(['never', 'on', 'after']).optional(),
  endDate: z.date().nullable().optional(),
  endOccurrences: z.number().min(1).optional(),

  // Step 2: Lesson Details
  lessonType: z.string().min(1, { message: 'Lesson type is required.' }),
  customEventType: z.string().optional(),
  arena: z.string().min(1, { message: 'Arena is required.' }),
  curriculum: z.union([
    z.string().min(1, { message: 'Curriculum is required.' }),
    z
      .array(z.string())
      .min(1, { message: 'At least one curriculum is required.' }),
  ]),
  ageRange: z.string().min(1, { message: 'Age range is required.' }),
  minAge: z.string().optional(),
  maxAge: z.string().optional(),
  instructor: z.string().min(1, { message: 'Instructor is required.' }),

  // Step 3: Additional Details
  // Requirements (optional)
  // requireFormCompletion: z.boolean().default(false),
  // requirePayment: z.boolean().default(false),

  // Additional details (optional)
  lessonNotes: z.string().optional(),
});

// Type for form data based on the schema
export type LessonSchedulingFormData = z.infer<typeof lessonSchedulingSchema>;

interface LessonSchedulingFormProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  slotInfo?: { start: Date; end: Date } | null;
}

export const LessonSchedulingForm = ({
  open,
  setOpen,
  slotInfo,
}: LessonSchedulingFormProps) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<LessonSchedulingFormData>({
    eventName: '',
    eventDate: null,
    eventHour: '',
    eventMinute: '',
    eventAmPm: 'AM',
    isRecurring: false,
    repeatFrequency: 'week',
    repeatDays: [],
    endType: 'never',
    endDate: null,
    endOccurrences: 1,
    lessonType: '',
    customEventType: '',
    arena: '',
    curriculum: ['none'],
    ageRange: '',
    minAge: '',
    maxAge: '',
    instructor: '',
    // requireFormCompletion: false,
    // requirePayment: false,
    lessonNotes: '',
  });
  // Form validation errors state
  const [errors, setErrors] = useState<Record<string, string>>({});

  const { mutateAsync: createLesson } = useCreateLesson();

  // Initialize form with slot info when it changes
  useEffect(() => {
    if (slotInfo && open) {
      const startDate = new Date(slotInfo.start);
      let hours = startDate.getHours();
      const isPM = hours >= 12;
      hours = hours % 12;
      hours = hours === 0 ? 12 : hours; // Convert 0 to 12 for 12 AM
      const formattedHours = hours.toString().padStart(2, '0');

      // Round minutes to nearest 5
      const roundedMinutes = (Math.round(startDate.getMinutes() / 5) * 5) % 60;
      const formattedMinutes = roundedMinutes.toString().padStart(2, '0');

      setFormData((prev) => ({
        ...prev,
        eventDate: startDate,
        eventHour: formattedHours,
        eventMinute: formattedMinutes,
        eventAmPm: isPM ? 'PM' : 'AM',
      }));
    }
  }, [slotInfo, open]);

  const totalSteps = 3;
  const progress = (currentStep / totalSteps) * 100;

  const updateFormData = (
    field: keyof LessonSchedulingFormData,
    value: unknown,
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error for this field when user types
    if (errors[field]) {
      setErrors((prev) => {
        const { [field]: _, ...rest } = prev;
        return rest;
      });
    }
  };

  const validateStep = () => {
    try {
      switch (currentStep) {
        case 1: {
          // Validate only step 1 fields
          const step1Schema = {
            lessonType: lessonSchedulingSchema.shape.lessonType,
            customEventType: lessonSchedulingSchema.shape.customEventType,
            eventDate: lessonSchedulingSchema.shape.eventDate,
            eventHour: lessonSchedulingSchema.shape.eventHour,
            eventMinute: lessonSchedulingSchema.shape.eventMinute,
            eventAmPm: lessonSchedulingSchema.shape.eventAmPm,
          };

          // Add recurring validation if enabled
          if (formData.isRecurring) {
            Object.assign(step1Schema, {
              repeatFrequency: lessonSchedulingSchema.shape.repeatFrequency,
              repeatDays: lessonSchedulingSchema.shape.repeatDays,
              endType: lessonSchedulingSchema.shape.endType,
            });

            // Add end date validation if "on" is selected
            if (formData.endType === 'on') {
              Object.assign(step1Schema, {
                endDate: lessonSchedulingSchema.shape.endDate,
              });
            }

            // Add occurrences validation if "after" is selected
            if (formData.endType === 'after') {
              Object.assign(step1Schema, {
                endOccurrences: lessonSchedulingSchema.shape.endOccurrences,
              });
            }
          }

          z.object(step1Schema).parse(formData);
          return true;
        }
        case 2: {
          // Validate only step 2 fields
          z.object({
            eventName: lessonSchedulingSchema.shape.eventName,
            arena: lessonSchedulingSchema.shape.arena,
            curriculum: lessonSchedulingSchema.shape.curriculum,
            instructor: lessonSchedulingSchema.shape.instructor,
          }).parse(formData);
          return true;
        }
        case 3:
          // Validate only step 3 fields (all optional)
          z.object({
            // requireFormCompletion:
            //   lessonSchedulingSchema.shape.requireFormCompletion,
            // requirePayment: lessonSchedulingSchema.shape.requirePayment,
            lessonNotes: lessonSchedulingSchema.shape.lessonNotes,
          }).parse(formData);
          return true;
        default:
          return false;
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        // Convert Zod errors to a more usable format
        const newErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          const path = err.path.join('.');
          newErrors[path] = err.message;
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const nextStep = () => {
    if (validateStep() && currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    // Validate the entire form before submission
    if (!formData.eventDate) {
      return;
    }

    try {
      lessonSchedulingSchema.parse(formData);
      console.log('Form submitted:', formData);

      const payload = {
        title: formData.eventName,
        lesson_type: formData.lessonType as LessonType,
        date: formData.eventDate.toISOString(),
        start_time: formData.eventHour,
        end_time: formData.eventMinute,
        duration_minutes: 60,
        max_students: 1,
        instructor_ids: ['1'],
      };

      const lesson = await createLesson(payload);
      console.log('🚀 ~ handleSubmit ~ lesson:', lesson);

      // Create event if callback exists
      // if (onAddEvent ) {
      //   // Convert 12-hour to 24-hour format for hours
      //   let hours = parseInt(formData.eventHour);
      //   if (formData.eventAmPm === 'PM' && hours < 12) {
      //     hours += 12;
      //   } else if (formData.eventAmPm === 'AM' && hours === 12) {
      //     hours = 0;
      //   }

      //   const minutes = parseInt(formData.eventMinute);

      //   // Create start date by combining eventDate with time
      //   const startDate = new Date(formData.eventDate);
      //   startDate.setHours(hours);
      //   startDate.setMinutes(minutes);

      //   // Create end date (default to 1 hour later)
      //   const endDate = new Date(startDate);
      //   endDate.setHours(startDate.getHours() + 1);

      //   // Create and add the event
      //   onAddEvent({
      //     title: formData.eventName,
      //     groundName: formData.arena || 'Default Arena',
      //     start: startDate,
      //     end: endDate,
      //   });
      // }

      setOpen(false);
      setCurrentStep(1);
      setFormData({
        eventName: '',
        eventDate: null,
        eventHour: '',
        eventMinute: '',
        eventAmPm: 'AM',
        isRecurring: false,
        repeatFrequency: 'week',
        repeatDays: [],
        endType: 'never',
        endDate: null,
        endOccurrences: 1,
        lessonType: '',
        customEventType: '',
        arena: '',
        curriculum: ['none'],
        ageRange: '',
        minAge: '',
        maxAge: '',
        instructor: '',
        // requireFormCompletion: false,
        // requirePayment: false,
        lessonNotes: '',
      });
      setErrors({});
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          const path = err.path.join('.');
          newErrors[path] = err.message;
        });
        setErrors(newErrors);
      }
    }
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 1: {
        // Basic validation for step 1
        if (
          errors.lessonType ||
          errors.customEventType ||
          errors.eventDate ||
          errors.eventHour ||
          errors.eventMinute ||
          errors.eventAmPm ||
          !formData.lessonType ||
          !formData.eventDate ||
          !formData.eventHour ||
          !formData.eventMinute ||
          !formData.eventAmPm
        ) {
          return false;
        }
        if (
          formData.lessonType === 'create-custom-event' &&
          (!formData.customEventType || errors.customEventType)
        ) {
          return false;
        }

        // Additional validation for recurring options
        if (formData.isRecurring) {
          // Check frequency
          if (!formData.repeatFrequency || errors.repeatFrequency) {
            return false;
          }

          // Check repeat days
          if (
            !Array.isArray(formData.repeatDays) ||
            formData.repeatDays.length === 0
          ) {
            return false;
          }

          // Check end type
          if (!formData.endType || errors.endType) {
            return false;
          }

          // Validate end date if "on" is selected
          if (
            formData.endType === 'on' &&
            (!formData.endDate || errors.endDate)
          ) {
            return false;
          }

          // Validate occurrences if "after" is selected
          if (formData.endType === 'after') {
            if (
              !formData.endOccurrences ||
              errors.endOccurrences ||
              (typeof formData.endOccurrences === 'number' &&
                formData.endOccurrences <= 0)
            ) {
              return false;
            }
          }
        }

        return true;
      }
      case 2: {
        return (
          !errors.eventName &&
          !errors.arena &&
          !errors.curriculum &&
          !errors.instructor &&
          !!formData.eventName &&
          !!formData.arena &&
          !!formData.curriculum &&
          !!formData.instructor
        );
      }
      case 3:
        // No required fields on step 3
        return true;
      default:
        return false;
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {/* <DialogTrigger asChild>
          <Button size='lg'>Start Application</Button>
        </DialogTrigger> */}
      <DialogContent className=''>
        <DialogHeader>
          <DialogTitle>Schedule a Lesson</DialogTitle>
          <DialogDescription>
            Complete this 3-step form to schedule a lesson.
          </DialogDescription>
        </DialogHeader>

        {/* Progress Bar */}
        <div className='space-y-4'>
          <div className='flex items-center justify-between'>
            <span className='text-sm font-medium'>
              Step {currentStep} of {totalSteps}
            </span>
            <span className='text-muted-foreground text-sm'>
              {Math.round(progress)}% complete
            </span>
          </div>
          <Progress value={progress} className='w-full' />
        </div>

        {/* Step Indicators */}
        {/* <div className='flex items-center justify-between'>
          {steps.map((step) => {
            const Icon = step.icon;
            const isActive = currentStep === step.number;
            const isCompleted = currentStep > step.number;

            return (
              <div
                key={step.number}
                className='flex flex-col items-center space-y-2'
              >
                <div
                  className={`flex h-10 w-10 items-center justify-center rounded-full border-2 ${
                    isCompleted
                      ? 'border-green-500 bg-green-500 text-white'
                      : isActive
                        ? 'bg-primary border-primary text-primary-foreground'
                        : 'border-muted-foreground text-muted-foreground'
                  }`}
                >
                  {isCompleted ? (
                    <CheckCircle className='h-5 w-5' />
                  ) : (
                    <Icon className='h-5 w-5' />
                  )}
                </div>
                <span
                  className={`text-xs ${isActive ? 'font-medium' : 'text-muted-foreground'}`}
                >
                  {step.title}
                </span>
              </div>
            );
          })}
        </div> */}

        {/* Form Content */}
        <div className='space-y-6'>
          {currentStep === 1 && (
            <StepFirst
              formData={formData}
              errors={errors}
              updateFormData={updateFormData}
            />
          )}

          {currentStep === 2 && (
            <StepTwo
              formData={formData}
              errors={errors}
              updateFormData={updateFormData}
            />
          )}

          {currentStep === 3 && (
            <StepThree formData={formData} updateFormData={updateFormData} />
          )}
        </div>

        {/* Navigation Buttons */}
        <div className='flex justify-between pt-4'>
          <Button
            variant='outline'
            onClick={prevStep}
            disabled={currentStep === 1}
            className='cursor-pointer'
          >
            Previous
          </Button>

          {currentStep === totalSteps ? (
            <Button
              onClick={handleSubmit}
              disabled={!isStepValid()}
              className='cursor-pointer bg-green-600 hover:bg-green-700'
            >
              Submit
            </Button>
          ) : (
            <Button
              onClick={nextStep}
              disabled={!isStepValid()}
              className='cursor-pointer'
            >
              Next Step
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
