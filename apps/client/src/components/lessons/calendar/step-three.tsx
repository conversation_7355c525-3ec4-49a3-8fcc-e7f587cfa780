import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

import { type LessonSchedulingFormData } from './lesson-scheduling-form';

interface StepThreeProps {
  formData: {
    lessonNotes?: string;
    requireFormCompletion?: boolean;
    requirePayment?: boolean;
  };
  updateFormData: (
    key: keyof LessonSchedulingFormData,
    value: string | boolean,
  ) => void;
}

export const StepThree = ({ formData, updateFormData }: StepThreeProps) => {
  return (
    <div className='space-y-4'>
      <h3 className='text-lg font-semibold'>Additional Details</h3>

      {/* Requirements Section */}
      {/* <div className='space-y-2'>
        <Label className='font-medium'>Requirements</Label>
        <div className='flex flex-col space-y-2'>
          <div className='flex items-center space-x-2'>
            <Switch
              id='requireFormCompletion'
              checked={formData.requireFormCompletion}
              onCheckedChange={(checked) => {
                updateFormData('requireFormCompletion', checked);
              }}
            />
            <Label htmlFor='requireFormCompletion'>
              Require form completion
            </Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Switch
              id='requirePayment'
              checked={formData.requirePayment}
              onCheckedChange={(checked) => {
                updateFormData('requirePayment', checked);
              }}
            />
            <Label htmlFor='requirePayment'>Require payment</Label>
          </div>
        </div>
      </div> */}

      {/* Additional Details Section */}
      <div className='space-y-2'>
        <Label htmlFor='lessonNotes' className='font-medium'>
          Lesson Notes
        </Label>
        <Textarea
          id='lessonNotes'
          value={formData.lessonNotes ?? ''}
          onChange={(e) => {
            updateFormData('lessonNotes', e.target.value);
          }}
          placeholder='Add any specific instructions or notes for this lesson'
          rows={3}
        />
      </div>
    </div>
  );
};
