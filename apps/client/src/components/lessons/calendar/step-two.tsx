import { type LessonSchedulingFormData } from '@/components/lessons/calendar/lesson-scheduling-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { cn } from '@/lib/utils';

interface StepTwoProps {
  formData: {
    eventName: string;
    arena: string;
    curriculum: string | Array<string>;
    ageRange: string;
    minAge?: string;
    maxAge?: string;
    instructor: string;
  };
  errors: {
    eventName?: string;
    arena?: string;
    curriculum?: string;
    ageRange?: string;
    minAge?: string;
    maxAge?: string;
    instructor?: string;
  };
  updateFormData: (key: keyof LessonSchedulingFormData, value: unknown) => void;
}

export const StepTwo = ({ formData, errors, updateFormData }: StepTwoProps) => {
  return (
    <div className='space-y-4'>
      <h3 className='text-lg font-semibold'>Lesson Details</h3>

      {/* Event Name */}
      <div className='space-y-2'>
        <Label htmlFor='eventName'>Event Name *</Label>
        <Input
          id='eventName'
          value={formData.eventName}
          onChange={(e) => {
            updateFormData('eventName', e.target.value);
          }}
          placeholder='Enter event name'
          className={errors.eventName ? 'border-red-500' : ''}
        />
        {errors.eventName && (
          <p className='text-xs text-red-500'>{errors.eventName}</p>
        )}
      </div>

      {/* Arena */}
      <div className='space-y-2'>
        <Label htmlFor='arena'>Arena *</Label>
        <Select
          value={formData.arena}
          onValueChange={(value) => {
            updateFormData('arena', value);
          }}
        >
          <SelectTrigger
            className={cn('w-full', errors.arena && 'border-red-500')}
          >
            <SelectValue placeholder='Select arena' />
          </SelectTrigger>
          <SelectContent>
            {['main', 'practice', 'indoor', 'outdoor', 'round', 'trail'].map(
              (val) => (
                <SelectItem key={val} value={val}>
                  {val.charAt(0).toUpperCase() + val.slice(1)} Arena
                </SelectItem>
              ),
            )}
          </SelectContent>
        </Select>
        {errors.arena && <p className='text-xs text-red-500'>{errors.arena}</p>}
      </div>

      {/* Curriculum */}
      <div className='space-y-2'>
        <Label htmlFor='curriculum'>Curriculum requirement to book?</Label>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant='outline'
              className={cn(
                'w-full justify-between',
                errors.curriculum && 'border-red-500',
              )}
            >
              {Array.isArray(formData.curriculum) &&
              formData.curriculum.length > 0
                ? `${formData.curriculum.length} selected`
                : formData.curriculum
                  ? '1 selected'
                  : 'Select curriculum'}
              <span className='ml-2 h-4 w-4'>▼</span>
            </Button>
          </PopoverTrigger>
          <PopoverContent className='w-full p-0' align='start'>
            <div className='space-y-1 p-2'>
              {[
                { value: 'all', label: 'Allow all' },
                { value: 'none', label: 'None' },
                { value: 'basic', label: 'Basic Horsemanship' },
                { value: 'english', label: 'English Riding' },
                { value: 'western', label: 'Western Riding' },
                { value: 'dressage', label: 'Dressage Fundamentals' },
                { value: 'jumping-basic', label: 'Jumping Basics' },
                { value: 'jumping-advanced', label: 'Advanced Jumping' },
                { value: 'trail', label: 'Trail Safety' },
                { value: 'competition', label: 'Competition Preparation' },
              ].map((item) => {
                const values = Array.isArray(formData.curriculum)
                  ? formData.curriculum
                  : formData.curriculum
                    ? [formData.curriculum]
                    : [];
                const isSelected = values.includes(item.value);
                return (
                  <div
                    key={item.value}
                    className='hover:bg-muted flex items-center space-x-2 rounded-md p-1'
                    onClick={() => {
                      let newValues: Array<string> = [];
                      if (item.value === 'none') {
                        newValues = isSelected ? [] : ['none'];
                      } else if (item.value === 'all') {
                        newValues = isSelected
                          ? values.filter((v) => v !== 'all')
                          : [
                              'all',
                              'basic',
                              'english',
                              'western',
                              'dressage',
                              'jumping-basic',
                              'jumping-advanced',
                              'trail',
                              'competition',
                            ];
                      } else {
                        const filtered = values.filter((v) => v !== 'none');
                        if (isSelected) {
                          newValues = filtered.filter(
                            (v) => v !== item.value && v !== 'all',
                          );
                        } else {
                          newValues = [...filtered, item.value];
                          const allOptions = [
                            'basic',
                            'english',
                            'western',
                            'dressage',
                            'jumping-basic',
                            'jumping-advanced',
                            'trail',
                            'competition',
                          ];
                          if (
                            allOptions.every((opt) => newValues.includes(opt))
                          ) {
                            newValues = ['all', ...newValues];
                          }
                        }
                        if (newValues.length === 0) newValues = ['none'];
                      }
                      updateFormData('curriculum', newValues);
                    }}
                  >
                    <div
                      className={cn(
                        'flex h-4 w-4 items-center justify-center rounded border',
                        isSelected
                          ? 'border-primary bg-primary text-white'
                          : 'border-muted-foreground',
                      )}
                    >
                      {isSelected && '✓'}
                    </div>
                    <span>{item.label}</span>
                  </div>
                );
              })}
            </div>
          </PopoverContent>
        </Popover>
        {errors.curriculum && (
          <p className='text-xs text-red-500'>{errors.curriculum}</p>
        )}
      </div>

      {/* Age Range */}
      <div className='space-y-2'>
        <Label htmlFor='ageRange'>Age requirement to book?</Label>
        <Select
          value={formData.ageRange}
          onValueChange={(value) => {
            updateFormData('ageRange', value);
          }}
        >
          <SelectTrigger
            className={cn('w-full', errors.ageRange && 'border-red-500')}
          >
            <SelectValue placeholder='Select age range' />
          </SelectTrigger>
          <SelectContent>
            {[
              'all',
              '0-5',
              '6-10',
              '11-15',
              '16-20',
              '21-25',
              '26-30',
              '31-35',
              '36-40',
              'custom',
            ].map((val) => (
              <SelectItem key={val} value={val}>
                {val === 'all' ? 'Allow all ages' : val.replace('-', ' - ')}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.ageRange && (
          <p className='text-xs text-red-500'>{errors.ageRange}</p>
        )}
      </div>

      {/* Custom Age Range */}
      {formData.ageRange === 'custom' && (
        <div className='flex gap-2'>
          <div className='space-y-2'>
            <Label htmlFor='minAge'>Min Age *</Label>
            <Input
              id='minAge'
              type='number'
              value={formData.minAge}
              onChange={(e) => {
                updateFormData('minAge', e.target.value);
              }}
            />
          </div>
          <div className='space-y-2'>
            <Label htmlFor='maxAge'>Max Age *</Label>
            <Input
              id='maxAge'
              type='number'
              value={formData.maxAge}
              onChange={(e) => {
                updateFormData('maxAge', e.target.value);
              }}
            />
          </div>
        </div>
      )}

      {/* Instructor */}
      <div className='space-y-2'>
        <Label htmlFor='instructor'>Instructor *</Label>
        <Select
          value={formData.instructor}
          onValueChange={(value) => {
            updateFormData('instructor', value);
          }}
        >
          <SelectTrigger
            className={cn('w-full', errors.instructor && 'border-red-500')}
          >
            <SelectValue placeholder='Select instructor' />
          </SelectTrigger>
          <SelectContent>
            {['sarah', 'michael', 'emma', 'david', 'olivia'].map((val) => (
              <SelectItem key={val} value={val}>
                {val.charAt(0).toUpperCase() + val.slice(1)} Johnson
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.instructor && (
          <p className='text-xs text-red-500'>{errors.instructor}</p>
        )}
      </div>
    </div>
  );
};
