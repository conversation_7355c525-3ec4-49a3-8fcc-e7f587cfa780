import { format } from 'date-fns/format';
import { useState } from 'react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

export function SlotDialog({
  open,
  onOpenChange,
  slotInfo,
  onAddEvent,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  slotInfo: { start: Date; end: Date } | null;
  onAddEvent: (event: {
    title: string;
    groundName: string;
    start: Date;
    end: Date;
  }) => void;
}) {
  const [title, setTitle] = useState('');
  const [groundName, setGroundName] = useState('');

  const handleSave = () => {
    if (slotInfo && title) {
      onAddEvent({
        title,
        groundName,
        start: slotInfo.start,
        end: slotInfo.end,
      });
      onOpenChange(false);
      setTitle('');
      setGroundName('');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add New Event</DialogTitle>
          <DialogDescription>
            {slotInfo ? (
              <>
                Start: {format(slotInfo.start, 'h a')}
                <br />
                End: {format(slotInfo.end, 'h a')}
              </>
            ) : null}
          </DialogDescription>
        </DialogHeader>
        <input
          value={title}
          onChange={(e) => {
            setTitle(e.target.value);
          }}
          placeholder='Enter event title'
          className='w-full border p-2'
        />
        <input
          value={groundName}
          onChange={(e) => {
            setGroundName(e.target.value);
          }}
          placeholder='Enter ground name'
          className='mt-2 w-full border p-2'
        />
        <button onClick={handleSave}>Save Event</button>
      </DialogContent>
    </Dialog>
  );
}
