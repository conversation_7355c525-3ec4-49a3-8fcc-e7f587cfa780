'use client';

import { Search } from 'lucide-react';

import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface SearchAndFilterProps {
  searchTerm: string;
  setSearchTerm: (value: string) => void;

  selectedInstructor: string;
  setSelectedInstructor: (value: string) => void;
  instructorOptions: Array<{ label: string; value: string }>;

  selectedStatus: string;
  setSelectedStatus: (value: string) => void;
  statusOptions: Array<{ label: string; value: string }>;
}

export const SearchAndFilter = ({
  searchTerm,
  setSearchTerm,
  selectedInstructor,
  setSelectedInstructor,
  instructorOptions,
  selectedStatus,
  setSelectedStatus,
  statusOptions,
}: SearchAndFilterProps) => {
  return (
    <div className='rounded-lg border border-gray-200 bg-white p-6 shadow-sm'>
      <div className='flex flex-col items-center justify-between gap-4 md:flex-row'>
        {/* Search */}
        <div className='relative max-w-md flex-1'>
          <Search className='absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400' />
          <Input
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
            }}
            placeholder='Search Lesson'
            className='h-11 border-gray-200 bg-gray-50 pl-10 focus:border-[#5C67F2] focus:ring-[#5C67F2]'
          />
        </div>

        {/* Filters */}
        <div className='flex gap-4'>
          <Select
            value={selectedInstructor}
            onValueChange={setSelectedInstructor}
          >
            <SelectTrigger className='h-11 w-48 border-gray-200 bg-white focus:border-[#5C67F2] focus:ring-[#5C67F2]'>
              <SelectValue placeholder='Select Instructor' />
            </SelectTrigger>
            <SelectContent>
              {instructorOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedStatus} onValueChange={setSelectedStatus}>
            <SelectTrigger className='h-11 w-40 border-gray-200 bg-white focus:border-[#5C67F2] focus:ring-[#5C67F2]'>
              <SelectValue placeholder='Select Status' />
            </SelectTrigger>
            <SelectContent>
              {statusOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};
