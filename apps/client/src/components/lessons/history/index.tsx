import { useNavigate } from '@tanstack/react-router';
import { Calendar, DollarSign, Users } from 'lucide-react';
import { useState } from 'react';

import { Card, CardContent } from '@/components/ui/card';

import { HistoryCard } from './historyCard';
import { HistoryTable } from './historyTable';
import { SearchAndFilter } from './searchAndFilter';

interface LessonHistoryData {
  id: string;
  lessonName: string;
  dateTime: string;
  instructor: string;
  arena: string;
  students: number;
  status: 'Completed' | 'Cancelled' | 'In Progress';
  revenue: number;
}

const lessonHistoryData: Array<LessonHistoryData> = [
  {
    id: '1',
    lessonName: 'Beginner Group Lesson',
    dateTime: '18 Apr 2025, 8am - 9am',
    instructor: '<PERSON>',
    arena: 'Indoor',
    students: 4,
    status: 'Completed',
    revenue: 142.0,
  },
  {
    id: '2',
    lessonName: 'Advanced Jumping',
    dateTime: '18 Apr 2025, 8am - 9am',
    instructor: '<PERSON>',
    arena: 'Indoor',
    students: 10,
    status: 'Completed',
    revenue: 142.0,
  },
  {
    id: '3',
    lessonName: 'Private Dressage',
    dateTime: '18 Apr 2025, 8am - 9am',
    instructor: '<PERSON>',
    arena: 'Indoor',
    students: 6,
    status: 'Completed',
    revenue: 142.0,
  },
  {
    id: '4',
    lessonName: 'Trail Riding Group',
    dateTime: '18 Apr 2025, 8am - 9am',
    instructor: 'Sarah Johnson',
    arena: 'Indoor',
    students: 4,
    status: 'Completed',
    revenue: 142.0,
  },
  {
    id: '5',
    lessonName: 'Semi Private Lesson',
    dateTime: '18 Apr 2025, 8am - 9am',
    instructor: 'Sarah Johnson',
    arena: 'Indoor',
    students: 8,
    status: 'Completed',
    revenue: 142.0,
  },
];

const instructorOptions = [
  { value: 'all', label: 'All Instructors' },
  { value: 'sarah-johnson', label: 'Sarah Johnson' },
  { value: 'mike-davis', label: 'Mike Davis' },
  { value: 'anna-wilson', label: 'Anna Wilson' },
  { value: 'david-brown', label: 'David Brown' },
  { value: 'jessica-taylor', label: 'Jessica Taylor' },
];

const statusOptions = [
  { value: 'all', label: 'All Status' },
  { value: 'completed', label: 'Completed' },
  { value: 'cancelled', label: 'Cancelled' },
  { value: 'in-progress', label: 'In Progress' },
];

const timeRangeOptions = [
  { value: 'this-week', label: 'This Week' },
  { value: 'last-week', label: 'Last Week' },
  { value: 'this-month', label: 'This Month' },
  { value: 'last-month', label: 'Last Month' },
  { value: 'this-year', label: 'This Year' },
];

export function LessonsHistory() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedInstructor, setSelectedInstructor] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedTimeRange, setSelectedTimeRange] = useState('this-week');

  // Calculate summary statistics
  const totalLessons = lessonHistoryData.length;
  const totalRevenue = lessonHistoryData.reduce(
    (sum, lesson) => sum + lesson.revenue,
    0,
  );
  const totalStudents = lessonHistoryData.reduce(
    (sum, lesson) => sum + lesson.students,
    0,
  );

  // Filter lessons based on search and filters
  const filteredLessons = lessonHistoryData.filter((lesson) => {
    const matchesSearch = lesson.lessonName
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    const matchesInstructor =
      selectedInstructor === 'all' ||
      lesson.instructor.toLowerCase().replace(' ', '-') === selectedInstructor;
    const matchesStatus =
      selectedStatus === 'all' ||
      lesson.status.toLowerCase().replace(' ', '-') === selectedStatus;

    return matchesSearch && matchesInstructor && matchesStatus;
  });

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'In Progress':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleLessonClick = (lessonId: string) => {
    void navigate({ to: '/lessons/details', params: { lessonId } });
  };

  const stats = [
    {
      label: 'Total Lessons',
      icon: <Calendar className='h-5 w-5 text-blue-600' />,
      value: totalLessons,
      timeRangeOptions,
      selectedTimeRange,
      onTimeRangeChange: setSelectedTimeRange,
    },
    {
      label: 'Total Revenue',
      icon: <DollarSign className='h-5 w-5 text-green-600' />,
      value: totalRevenue.toLocaleString(),
      valuePrefix: '$',
    },
    {
      label: 'Total Students',
      icon: <Users className='h-5 w-5 text-purple-600' />,
      value: totalStudents,
    },
  ];

  return (
    <div className='min-h-screen bg-gray-50 p-6'>
      <div className='mx-auto max-w-7xl space-y-6'>
        {/* Search and Filters */}
        <SearchAndFilter
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          selectedInstructor={selectedInstructor}
          setSelectedInstructor={setSelectedInstructor}
          instructorOptions={instructorOptions}
          selectedStatus={selectedStatus}
          setSelectedStatus={setSelectedStatus}
          statusOptions={statusOptions}
        />

        {/* Summary Cards */}
        <div className='grid grid-cols-1 gap-6 md:grid-cols-3'>
          {stats.map((stat) => (
            <HistoryCard key={stat.label} {...stat} />
          ))}
        </div>

        {/* Lessons Table */}
        <HistoryTable
          lessons={filteredLessons}
          handleLessonClick={handleLessonClick}
          getStatusBadgeColor={getStatusBadgeColor}
        />

        {/* Empty State */}
        {filteredLessons.length === 0 && (
          <Card className='border-gray-200 bg-white shadow-sm'>
            <CardContent className='p-12 text-center'>
              <div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100'>
                <Calendar className='h-8 w-8 text-gray-400' />
              </div>
              <h3 className='mb-2 text-lg font-medium text-gray-900'>
                No lessons found
              </h3>
              <p className='text-gray-500'>
                Try adjusting your search or filter criteria.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
