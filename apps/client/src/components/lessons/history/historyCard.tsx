// DashboardStatCard.tsx
import { type ReactNode } from 'react';

import { Card, CardContent } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

export interface TimeRangeOption {
  value: string;
  label: string;
}

interface DashboardStatCardProps {
  icon: ReactNode;
  label: string;
  value: number | string;
  valuePrefix?: string;
  timeRangeOptions?: Array<TimeRangeOption>;
  selectedTimeRange?: string;
  onTimeRangeChange?: (value: string) => void;
}

export const HistoryCard = ({
  icon,
  label,
  value,
  valuePrefix,
  timeRangeOptions,
  selectedTimeRange,
  onTimeRangeChange,
}: DashboardStatCardProps) => {
  return (
    <Card className='border-gray-200 bg-white shadow-sm'>
      <CardContent className='p-6'>
        <div className='mb-4 flex items-center justify-between'>
          <div className='flex items-center gap-3'>
            <div>{icon}</div>
            <span className='font-medium text-gray-600'>{label}</span>
          </div>
          {timeRangeOptions && selectedTimeRange && onTimeRangeChange && (
            <Select value={selectedTimeRange} onValueChange={onTimeRangeChange}>
              <SelectTrigger className='h-8 w-32 border-gray-200 text-sm'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {timeRangeOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
        <div className='text-4xl font-bold text-gray-900'>
          {valuePrefix}
          {value}
        </div>
      </CardContent>
    </Card>
  );
};
