'use client';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

// adjust path if needed
interface Lesson {
  id: string;
  lessonName: string;
  dateTime: string;
  instructor: string;
  arena: string;
  students: number;
  status: string; // or you can use a union type: 'Scheduled' | 'Completed' | 'Cancelled' etc.
  revenue: number;
}
export interface LessonsTableProps {
  lessons: Array<Lesson>;
  handleLessonClick: (lessonId: string) => void;
  getStatusBadgeColor: (status: string) => string;
}

export const HistoryTable = ({
  lessons,
  handleLessonClick,
  getStatusBadgeColor,
}: LessonsTableProps) => {
  return (
    <Card className='border-gray-200 bg-white shadow-sm'>
      <CardContent className='p-0'>
        <Table>
          <TableHeader>
            <TableRow className='border-b border-gray-200'>
              <TableHead className='px-6 py-4 text-left font-semibold text-gray-900'>
                Lesson
              </TableHead>
              <TableHead className='px-6 py-4 text-left font-semibold text-gray-900'>
                Date & Time
              </TableHead>
              <TableHead className='px-6 py-4 text-left font-semibold text-gray-900'>
                Instructor
              </TableHead>
              <TableHead className='px-6 py-4 text-left font-semibold text-gray-900'>
                Arena
              </TableHead>
              <TableHead className='px-6 py-4 text-left font-semibold text-gray-900'>
                Students
              </TableHead>
              <TableHead className='px-6 py-4 text-left font-semibold text-gray-900'>
                Status
              </TableHead>
              <TableHead className='px-6 py-4 text-left font-semibold text-gray-900'>
                Revenue
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {lessons.map((lesson) => (
              <TableRow
                key={lesson.id}
                className='border-b border-gray-100 hover:bg-gray-50'
              >
                <TableCell className='px-6 py-4'>
                  <div
                    className='cursor-pointer font-medium text-gray-900 transition-colors hover:text-[#5C67F2]'
                    onClick={() => {
                      handleLessonClick(lesson.id);
                    }}
                  >
                    {lesson.lessonName}
                  </div>
                </TableCell>
                <TableCell className='px-6 py-4'>
                  <div className='text-gray-700'>{lesson.dateTime}</div>
                </TableCell>
                <TableCell className='px-6 py-4'>
                  <div className='text-gray-700'>{lesson.instructor}</div>
                </TableCell>
                <TableCell className='px-6 py-4'>
                  <div className='text-gray-700'>{lesson.arena}</div>
                </TableCell>
                <TableCell className='px-6 py-4'>
                  <div className='text-gray-700'>{lesson.students}</div>
                </TableCell>
                <TableCell className='px-6 py-4'>
                  <Badge
                    className={`${getStatusBadgeColor(
                      lesson.status,
                    )} rounded-full border px-3 py-1 font-medium`}
                  >
                    {lesson.status}
                  </Badge>
                </TableCell>
                <TableCell className='px-6 py-4'>
                  <div className='font-medium text-gray-900'>
                    ${lesson.revenue.toFixed(2)}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};
