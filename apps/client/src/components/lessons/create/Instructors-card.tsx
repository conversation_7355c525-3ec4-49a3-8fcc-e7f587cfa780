// InstructorsCard.tsx
import { Search, X } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';

interface InstructorOption {
  value: string;
  label: string;
}

export interface InstructorsCardProps {
  watchedInstructors: Array<string>;
  instructorOptions: Array<InstructorOption>;
  instructorSearch: string;
  setInstructorSearch: (value: string) => void;
  filteredInstructors: Array<InstructorOption>;
  addInstructor: (id: string) => void;
  removeInstructor: (id: string) => void;
}

export const InstructorsCard = ({
  watchedInstructors,
  instructorOptions,
  instructorSearch,
  setInstructorSearch,
  filteredInstructors,
  addInstructor,
  removeInstructor,
}: InstructorsCardProps) => {
  return (
    <Card className='border-gray-200 bg-white shadow-sm'>
      <CardHeader className='pb-4'>
        <CardTitle className='text-lg font-semibold text-gray-900'>
          Instructors
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        {/* Selected Instructors */}
        <div className='flex flex-wrap gap-2'>
          {watchedInstructors.map((instructorId) => {
            const instructor = instructorOptions.find(
              (i) => i.value === instructorId,
            );
            return (
              <Badge
                key={instructorId}
                variant='secondary'
                className='rounded-full border border-[#5C67F2]/20 bg-[#5C67F2]/10 px-3 py-1 text-[#5C67F2]'
              >
                {instructor?.label}
                <X
                  className='ml-2 h-3 w-3 cursor-pointer'
                  onClick={() => {
                    removeInstructor(instructorId);
                  }}
                />
              </Badge>
            );
          })}
        </div>

        {/* Search Instructors */}
        <div className='relative'>
          <Search className='absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400' />
          <Input
            value={instructorSearch}
            onChange={(e) => {
              setInstructorSearch(e.target.value);
            }}
            placeholder='Search instructors...'
            className='h-11 border-gray-300 bg-white pl-10 focus:border-[#5C67F2] focus:ring-[#5C67F2]'
          />
        </div>

        {/* Instructor Options */}
        {instructorSearch && (
          <div className='max-h-40 overflow-y-auto rounded-lg border border-gray-200'>
            {filteredInstructors.map((instructor) => (
              <div
                key={instructor.value}
                onClick={() => {
                  addInstructor(instructor.value);
                }}
                className='cursor-pointer border-b border-gray-100 p-3 last:border-b-0 hover:bg-gray-50'
              >
                {instructor.label}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
