// RequirementsCard.tsx
import { type Control, Controller } from 'react-hook-form';

import { type LessonFormData } from '@/components/lessons/create/lesson-details-form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';

interface RequirementsCardProps {
  control: Control<LessonFormData>;
}

export const RequirementsCard = ({ control }: RequirementsCardProps) => {
  return (
    <Card className='border-gray-200 bg-white shadow-sm'>
      <CardHeader className='pb-4'>
        <CardTitle className='text-lg font-semibold text-gray-900'>
          Requirements
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        {/* Require Form */}
        <div className='flex items-center space-x-3'>
          <Controller
            name='requireForm'
            control={control}
            render={({ field }) => (
              <input
                type='checkbox'
                id='requireForm'
                checked={field.value}
                onChange={field.onChange}
                className='h-4 w-4 rounded border-gray-300 text-[#5C67F2] focus:ring-[#5C67F2]'
              />
            )}
          />
          <Label
            htmlFor='requireForm'
            className='cursor-pointer text-sm font-medium text-gray-700'
          >
            Require form completion
          </Label>
        </div>

        {/* Require Payment */}
        <div className='flex items-center space-x-3'>
          <Controller
            name='requirePayment'
            control={control}
            render={({ field }) => (
              <input
                type='checkbox'
                id='requirePayment'
                checked={field.value}
                onChange={field.onChange}
                className='h-4 w-4 rounded border-gray-300 text-[#5C67F2] focus:ring-[#5C67F2]'
              />
            )}
          />
          <Label
            htmlFor='requirePayment'
            className='cursor-pointer text-sm font-medium text-gray-700'
          >
            Require payment
          </Label>
        </div>
      </CardContent>
    </Card>
  );
};
