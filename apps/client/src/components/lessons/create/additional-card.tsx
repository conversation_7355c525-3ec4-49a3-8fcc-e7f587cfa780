// AdditionalDetailsCard.tsx
import { type Control, Controller } from 'react-hook-form';

import { type LessonFormData } from '@/components/lessons/create/lesson-details-form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface AdditionalDetailsCardProps {
  control: Control<LessonFormData>;
}

export const AdditionalDetailsCard = ({
  control,
}: AdditionalDetailsCardProps) => {
  return (
    <Card className='border-gray-200 bg-white shadow-sm'>
      <CardHeader className='pb-4'>
        <CardTitle className='text-lg font-semibold text-gray-900'>
          Additional Details
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-6'>
        <div>
          <Label
            htmlFor='notes'
            className='mb-2 block text-sm font-medium text-gray-700'
          >
            Lesson Notes
          </Label>
          <Controller
            name='notes'
            control={control}
            render={({ field }) => (
              <Textarea
                {...field}
                placeholder='Add any special instructions or notes for this lesson...'
                className='min-h-[100px] resize-none border-gray-300 bg-white focus:border-[#5C67F2] focus:ring-[#5C67F2]'
              />
            )}
          />
        </div>
      </CardContent>
    </Card>
  );
};
