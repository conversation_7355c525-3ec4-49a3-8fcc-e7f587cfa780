import { Check } from 'lucide-react';
import { useState } from 'react';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { TooltipProvider } from '@/components/ui/tooltip';

import { DetailsHeader } from './detailsHeader';
import { EnrolledStudentsCard } from './enrolledStudentCard';
import { InstructorsAndScheduleCard } from './InstructorsAndScheduleCard';
import { LessonOverviewCard } from './lessonOverviewCard';
import { NotesAndAttachmentsCard } from './notesAndAttachmentsCard';

interface LessonDetailsProps {
  onBack?: () => void;
}

// Sample lesson data
const lessonData = {
  id: '1',
  title: 'Beginner Group Lesson',
  status: 'Scheduled' as const,
  instructors: [
    {
      id: '1',
      name: '<PERSON>',
      avatar: '/placeholder.svg?height=40&width=40',
      email: '<EMAIL>',
      phone: '(*************',
      timeBlock: '8:00 - 8:30 AM',
      assignedHorses: ['Thunder', 'Spirit'],
    },
    {
      id: '2',
      name: '<PERSON>',
      avatar: '/placeholder.svg?height=40&width=40',
      email: '<EMAIL>',
      phone: '(*************',
      timeBlock: '8:30 - 9:00 AM',
      assignedHorses: ['Grace', 'Hunter'],
    },
  ],
  arena: 'Indoor Arena',
  date: 'April 25, 2025',
  startTime: '8:00 AM',
  endTime: '9:00 AM',
  duration: '60 minutes',
  maxStudents: 6,
  currentStudents: 4,
  enrolledStudents: [
    {
      id: '1',
      name: 'Emma Thompson',
      age: 12,
      level: 'Beginner',
      avatar: '/placeholder.svg?height=32&width=32',
      attendance: true,
      feeStatus: 'Paid',
      assignedHorse: 'Thunder',
    },
    {
      id: '2',
      name: 'Jake Thompson',
      age: 10,
      level: 'Beginner',
      avatar: '/placeholder.svg?height=32&width=32',
      attendance: true,
      feeStatus: 'Paid',
      assignedHorse: 'Spirit',
    },
    {
      id: '3',
      name: 'Alex Johnson',
      age: 14,
      level: 'Intermediate',
      avatar: '/placeholder.svg?height=32&width=32',
      attendance: false,
      feeStatus: 'Unpaid',
      assignedHorse: undefined,
    },
    {
      id: '4',
      name: 'Sophia Wilson',
      age: 13,
      level: 'Beginner',
      avatar: '/placeholder.svg?height=32&width=32',
      attendance: true,
      feeStatus: 'Paid',
      assignedHorse: 'Grace',
    },
  ],
  schedule: {
    isRecurring: true,
    type: 'Weekly',
    recurrencePattern: 'weekly',
    selectedDays: ['friday'],
    dayOfWeek: 'Friday',
    startDate: 'March 15, 2025',
    endDate: 'June 15, 2025',
    nextOccurrence: 'May 2, 2025',
    totalSessions: 14,
    completedSessions: 6,
    upcomingSessions: [
      {
        date: 'Apr 25',
        fullDate: 'April 25, 2025',
        status: 'scheduled',
        isToday: false,
      },
      {
        date: 'May 2',
        fullDate: 'May 2, 2025',
        status: 'scheduled',
        isToday: false,
      },
      {
        date: 'May 9',
        fullDate: 'May 9, 2025',
        status: 'scheduled',
        isToday: false,
      },
      {
        date: 'May 16',
        fullDate: 'May 16, 2025',
        status: 'scheduled',
        isToday: false,
      },
      {
        date: 'May 23',
        fullDate: 'May 23, 2025',
        status: 'scheduled',
        isToday: false,
      },
      {
        date: 'May 30',
        fullDate: 'May 30, 2025',
        status: 'scheduled',
        isToday: false,
      },
      {
        date: 'Jun 6',
        fullDate: 'June 6, 2025',
        status: 'scheduled',
        isToday: false,
      },
      {
        date: 'Jun 13',
        fullDate: 'June 13, 2025',
        status: 'scheduled',
        isToday: false,
      },
    ],
    pastSessions: [
      { date: 'Mar 15', fullDate: 'March 15, 2025', status: 'completed' },
      { date: 'Mar 22', fullDate: 'March 22, 2025', status: 'completed' },
      { date: 'Mar 29', fullDate: 'March 29, 2025', status: 'completed' },
      { date: 'Apr 5', fullDate: 'April 5, 2025', status: 'completed' },
      { date: 'Apr 12', fullDate: 'April 12, 2025', status: 'completed' },
      { date: 'Apr 19', fullDate: 'April 19, 2025', status: 'completed' },
    ],
  },
  notes:
    'Focus on basic mounting and dismounting techniques. Emma needs extra attention with balance. Ensure all students wear proper safety equipment.',
  equipment: ['Helmet', 'Boots', 'Safety Vest', 'Gloves'],
  attachments: [
    { id: '1', name: 'Waiver_Forms.pdf', size: '2.4 MB', type: 'pdf' },
    { id: '2', name: 'Course_Map.jpg', size: '1.8 MB', type: 'image' },
  ],
  hasConflicts: false,
};

// const availableEquipment = [
//   'Helmet',
//   'Boots',
//   'Safety Vest',
//   'Gloves',
//   'Whip',
//   'Spurs',
//   'Saddle Pad',
//   'Bridle',
//   'Halter',
//   'Lead Rope',
// ];

export function LessonDetails({ onBack }: LessonDetailsProps) {
  const [notes, setNotes] = useState(lessonData.notes);
  // const [equipment, setEquipment] = useState(lessonData.equipment);
  const [students, setStudents] = useState(lessonData.enrolledStudents);

  const getSeatsColor = () => {
    const percentage =
      (lessonData.currentStudents / lessonData.maxStudents) * 100;
    if (percentage >= 90) return 'text-red-600';
    if (percentage >= 75) return 'text-orange-600';
    return 'text-gray-900';
  };

  const toggleAttendance = (studentId: string) => {
    setStudents((prev) =>
      prev.map((student) =>
        student.id === studentId
          ? { ...student, attendance: !student.attendance }
          : student,
      ),
    );
  };

  // const toggleEquipment = (item: string) => {
  //   setEquipment((prev) =>
  //     prev.includes(item) ? prev.filter((e) => e !== item) : [...prev, item],
  //   );
  // };

  // const getFeeStatusColor = (status: string) => {
  //   return status === 'Paid'
  //     ? 'bg-green-100 text-green-800 border-green-200'
  //     : 'bg-red-100 text-red-800 border-red-200';
  // };

  // const getSessionStatusColor = (status: string) => {
  //   switch (status) {
  //     case 'completed':
  //       return 'bg-green-100 text-green-800 border-green-200';
  //     case 'scheduled':
  //       return 'bg-blue-100 text-blue-800 border-blue-200';
  //     case 'cancelled':
  //       return 'bg-red-100 text-red-800 border-red-200';
  //     default:
  //       return 'bg-gray-100 text-gray-800 border-gray-200';
  //   }
  // };

  return (
    <TooltipProvider>
      <div className='min-h-screen bg-[#FAFAFB]'>
        {/* Header Bar */}
        <DetailsHeader onBack={onBack} lessonData={lessonData} />

        <div className='mx-auto max-w-7xl space-y-8 px-6 py-8'>
          {/* Lesson Overview Card */}

          <LessonOverviewCard
            title={lessonData.title}
            instructors={lessonData.instructors}
            arena={lessonData.arena}
            date={lessonData.date}
            startTime={lessonData.startTime}
            endTime={lessonData.endTime}
            duration={lessonData.duration}
            currentStudents={lessonData.currentStudents}
            maxStudents={lessonData.maxStudents}
            getSeatsColor={getSeatsColor}
          />
          {/* Tri-Column Info Area */}
          <div className='grid grid-cols-1 gap-8 lg:grid-cols-2'>
            {/* Column A: Enrolled Students */}
            <EnrolledStudentsCard
              students={students}
              lessonMaxStudents={lessonData.maxStudents}
              setStudents={setStudents}
              toggleAttendance={toggleAttendance}
            />

            {/* Column C: Instructors & Schedule */}
            <InstructorsAndScheduleCard
              instructors={lessonData.instructors}
              schedule={lessonData.schedule}
            />
          </div>

          {/* Secondary Sections */}
          <div className='space-y-8'>
            {/* Lesson Notes */}
            <NotesAndAttachmentsCard
              notes={notes}
              setNotes={setNotes}
              attachments={lessonData.attachments}
            />
          </div>
        </div>

        {/* Sticky Footer CTA */}
        <div className='sticky bottom-0 border-t border-gray-200 bg-white p-4 shadow-lg'>
          <div className='mx-auto flex max-w-7xl justify-center'>
            <Button
              size='lg'
              className='rounded-xl bg-blue-600 px-8 py-3 text-white shadow-lg hover:bg-blue-700'
            >
              {/* eslint-disable-next-line @typescript-eslint/no-unnecessary-condition */}
              {lessonData.status === 'Scheduled'
                ? 'Mark Attendance'
                : 'Update Lesson'}
            </Button>
          </div>
        </div>

        {/* Mobile Floating Action Button */}
        <div className='fixed right-6 bottom-6 lg:hidden'>
          <Button
            size='lg'
            className='h-14 w-14 rounded-full bg-blue-600 text-white shadow-lg hover:bg-blue-700'
          >
            <Check className='h-6 w-6' />
          </Button>
        </div>
      </div>
    </TooltipProvider>
  );
}
