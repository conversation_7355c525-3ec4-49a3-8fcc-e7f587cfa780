import { Calendar, MapPin, User, Users } from 'lucide-react';

import { Card, CardContent } from '@/components/ui/card';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface Instructor {
  id: string;
  name: string;
  email: string;
  phone: string;
}

interface LessonOverviewCardProps {
  title: string;
  instructors: Array<Instructor>;
  arena: string;
  date: string;
  startTime: string;
  endTime: string;
  duration: string;
  currentStudents: number;
  maxStudents: number;
  getSeatsColor: () => string;
}

export const LessonOverviewCard = ({
  title,
  instructors,
  arena,
  date,
  startTime,
  endTime,
  duration,
  currentStudents,
  maxStudents,
  getSeatsColor,
}: LessonOverviewCardProps) => {
  return (
    <Card className='rounded-xl border-gray-200 bg-white shadow-sm'>
      <CardContent className='p-8'>
        <h2 className='mb-8 text-3xl font-bold text-gray-900'>{title}</h2>

        <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4'>
          {/* Instructors */}
          <div className='flex items-center gap-4'>
            <div className='rounded-xl bg-blue-50 p-3'>
              <User className='h-5 w-5 text-blue-600' />
            </div>
            <div>
              <p className='mb-1 text-sm text-gray-500'>Instructors</p>
              <Tooltip>
                <TooltipTrigger asChild>
                  <p className='cursor-pointer font-semibold text-gray-900 hover:text-blue-600'>
                    {instructors.map((i) => i.name).join(', ')}
                  </p>
                </TooltipTrigger>
                <TooltipContent>
                  <div className='space-y-2'>
                    {instructors.map((instructor) => (
                      <div key={instructor.id}>
                        <p className='font-medium'>{instructor.name}</p>
                        <p className='text-xs text-gray-500'>
                          {instructor.email}
                        </p>
                        <p className='text-xs text-gray-500'>
                          {instructor.phone}
                        </p>
                      </div>
                    ))}
                  </div>
                </TooltipContent>
              </Tooltip>
            </div>
          </div>

          {/* Arena */}
          <div className='flex items-center gap-4'>
            <div className='rounded-xl bg-green-50 p-3'>
              <MapPin className='h-5 w-5 text-green-600' />
            </div>
            <div>
              <p className='mb-1 text-sm text-gray-500'>Arena</p>
              <p className='font-semibold text-gray-900'>{arena}</p>
            </div>
          </div>

          {/* Date & Time */}
          <div className='flex items-center gap-4'>
            <div className='rounded-xl bg-purple-50 p-3'>
              <Calendar className='h-5 w-5 text-purple-600' />
            </div>
            <div>
              <p className='mb-1 text-sm text-gray-500'>Date & Time</p>
              <p className='font-semibold text-gray-900'>{date}</p>
              <p className='text-sm text-gray-600'>
                {startTime} → {endTime} ({duration})
              </p>
            </div>
          </div>

          {/* Students */}
          <div className='flex items-center gap-4'>
            <div className='rounded-xl bg-orange-50 p-3'>
              <Users className='h-5 w-5 text-orange-600' />
            </div>
            <div>
              <p className='mb-1 text-sm text-gray-500'>Students</p>
              <p className={`font-semibold ${getSeatsColor()}`}>
                {currentStudents} / {maxStudents} seats
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
