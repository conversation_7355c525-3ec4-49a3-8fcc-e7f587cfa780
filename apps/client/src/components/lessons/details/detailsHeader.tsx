import {
  <PERSON>ertCircle,
  ArrowLeft,
  CheckCircle,
  Clock,
  Copy,
  Edit,
  MoreHorizontal,
  Printer,
  X,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface DetailsHeaderProps {
  onBack?: () => void;
  lessonData: {
    status: string;
  };
}

export const DetailsHeader = ({ onBack, lessonData }: DetailsHeaderProps) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Scheduled':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'In-Progress':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Completed':
        return <CheckCircle className='h-4 w-4' />;
      case 'Scheduled':
        return <Clock className='h-4 w-4' />;
      case 'Cancelled':
        return <X className='h-4 w-4' />;
      case 'In-Progress':
        return <AlertCircle className='h-4 w-4' />;
      default:
        return <Clock className='h-4 w-4' />;
    }
  };
  return (
    <div className='sticky top-0 z-10 border-b border-gray-200 bg-white shadow-sm'>
      <div className='mx-auto max-w-7xl px-6 py-4'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-4'>
            <Button
              variant='ghost'
              size='icon'
              onClick={onBack}
              className='h-10 w-10 rounded-full hover:bg-gray-100'
            >
              <ArrowLeft className='h-5 w-5' />
            </Button>
            <div>
              <h1 className='text-2xl font-semibold text-gray-900'>
                Lesson Details
              </h1>
              <p className='text-sm text-gray-500'>
                View & manage lesson information
              </p>
            </div>
            <Badge
              className={`${getStatusColor(lessonData.status)} ml-4 border px-3 py-1 font-medium`}
            >
              {getStatusIcon(lessonData.status)}
              <span className='ml-1'>{lessonData.status}</span>
            </Badge>
          </div>

          {/* Action Buttons */}
          <div className='flex items-center gap-2'>
            <Button variant='outline' size='sm' className='h-9'>
              <Edit className='mr-2 h-4 w-4' />
              Edit
            </Button>
            <Button variant='outline' size='sm' className='h-9'>
              <Copy className='mr-2 h-4 w-4' />
              Duplicate
            </Button>
            <Button variant='outline' size='sm' className='h-9'>
              <Printer className='mr-2 h-4 w-4' />
              Print
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant='outline' size='sm' className='h-9 w-9 p-0'>
                  <MoreHorizontal className='h-4 w-4' />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end'>
                <DropdownMenuItem>Archive Lesson</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className='text-red-600'>
                  Delete Lesson
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </div>
  );
};
