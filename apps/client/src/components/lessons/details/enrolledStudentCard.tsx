import {
  Alert<PERSON>riangle,
  CheckCircle,
  ChevronDown,
  Plus,
  Users,
  X,
} from 'lucide-react';
import { type FC } from 'react';

import { Horse } from '@/components/icons/horse';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Switch } from '@/components/ui/switch';

interface Student {
  id: string;
  name: string;
  age: number;
  level: string;
  avatar: string;
  attendance: boolean;
  feeStatus: string;
  assignedHorse?: string;
}

interface EnrolledStudentsCardProps {
  students: Array<Student>;
  lessonMaxStudents: number;
  setStudents: React.Dispatch<React.SetStateAction<Array<Student>>>;
  toggleAttendance: (studentId: string) => void;
}
export const EnrolledStudentsCard: FC<EnrolledStudentsCardProps> = ({
  students,
  lessonMaxStudents,
  setStudents,
  toggleAttendance,
}) => {
  const getFeeStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Scheduled':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'In-Progress':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };
  return (
    <Card className='relative rounded-xl border-gray-200 bg-white shadow-sm'>
      <CardHeader className='pb-4'>
        <CardTitle className='flex items-center gap-2 text-lg font-semibold text-gray-900'>
          <Users className='h-5 w-5 text-blue-600' />
          Enrolled Students ({students.length}/{lessonMaxStudents})
        </CardTitle>
      </CardHeader>
      <CardContent className='max-h-[600px] space-y-3 overflow-y-auto'>
        {students.map((student) => (
          <div
            key={student.id}
            className='space-y-3 rounded-lg border border-gray-200 p-4 transition-colors hover:border-gray-300'
          >
            {/* Header */}
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-3'>
                <Avatar className='h-10 w-10'>
                  <AvatarImage
                    src={student.avatar || '/placeholder.svg'}
                    alt={student.name}
                  />
                  <AvatarFallback className='bg-blue-100 text-sm font-medium text-blue-700'>
                    {student.name
                      .split(' ')
                      .map((n) => n[0])
                      .join('')}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className='text-sm font-medium text-gray-900'>
                    {student.name}
                  </p>
                  <div className='flex items-center gap-2 text-xs text-gray-500'>
                    <span>Age {student.age}</span>
                    <span>•</span>
                    <Badge
                      variant='outline'
                      className='h-5 px-2 py-0.5 text-xs'
                    >
                      {student.level}
                    </Badge>
                  </div>
                </div>
              </div>
              <Badge
                className={`${getFeeStatusColor(student.feeStatus)} border px-2 py-1 text-xs font-medium`}
              >
                {student.feeStatus}
              </Badge>
            </div>

            {/* Horse Assignment */}
            <div className='space-y-2'>
              <label className='text-xs font-medium text-gray-700'>
                Horse Assignment
              </label>
              <div className='flex items-center gap-2'>
                <div className='flex-1'>
                  {student.feeStatus === 'Paid' ? (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant='outline'
                          className='h-8 w-full justify-between bg-gray-50 text-sm hover:bg-gray-100'
                        >
                          <span className='flex items-center gap-2'>
                            <Horse className='h-3 w-3' />
                            {student.assignedHorse ?? 'No horse assigned'}
                          </span>
                          <ChevronDown className='h-3 w-3' />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className='w-48'>
                        <DropdownMenuItem
                          onClick={() => {
                            setStudents((prev) =>
                              prev.map((s) =>
                                s.id === student.id
                                  ? { ...s, assignedHorse: undefined }
                                  : s,
                              ),
                            );
                          }}
                        >
                          <X className='mr-2 h-3 w-3' /> No horse assigned
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        {[
                          'Thunder',
                          'Spirit',
                          'Grace',
                          'Hunter',
                          'Midnight',
                          'Star',
                        ].map((horse) => (
                          <DropdownMenuItem
                            key={horse}
                            onClick={() => {
                              setStudents((prev) =>
                                prev.map((s) =>
                                  s.id === student.id
                                    ? { ...s, assignedHorse: horse }
                                    : s,
                                ),
                              );
                            }}
                          >
                            <Horse className='mr-2 h-3 w-3' /> {horse}
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  ) : (
                    <div className='flex w-full items-center gap-2 rounded-md border border-gray-200 bg-gray-100 p-2 text-sm text-gray-500'>
                      <AlertTriangle className='h-3 w-3' />
                      Payment required for horse assignment
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Attendance */}
            <div className='flex items-center justify-between border-t border-gray-100 pt-2'>
              <div className='flex items-center gap-2'>
                <Switch
                  checked={student.attendance}
                  onCheckedChange={() => {
                    toggleAttendance(student.id);
                  }}
                  className='scale-75 data-[state=checked]:bg-orange-600'
                />
                <span className='text-xs text-gray-600'>
                  {student.attendance ? 'Paused' : 'Mark Paused'}
                </span>
              </div>
              {student.attendance && (
                <Badge
                  variant='outline'
                  className='border-orange-200 bg-orange-50 text-xs text-orange-700'
                >
                  <CheckCircle className='mr-1 h-3 w-3' /> Paused
                </Badge>
              )}
            </div>
          </div>
        ))}
        <div className='absolute bottom-4 left-0 w-full'>
          <div className='px-6'>
            <Button variant='outline' className='h-10 w-full border-dashed'>
              <Plus className='mr-2 h-4 w-4' /> Add Student
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
