// InstructorsAndScheduleCard.tsx
import { User } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

interface Instructor {
  id: string;
  name: string;
  avatar: string;
  timeBlock: string;
}

interface Schedule {
  isRecurring: boolean;
  recurrencePattern: string;
  type: string;
  dayOfWeek: string;
  completedSessions: number;
  totalSessions: number;
  startDate: string;
  endDate: string;
  nextOccurrence: string;
}

interface InstructorsAndScheduleCardProps {
  instructors: Array<Instructor>;
  schedule: Schedule;
}

export const InstructorsAndScheduleCard = ({
  instructors,
  schedule,
}: InstructorsAndScheduleCardProps) => {
  return (
    <Card className='rounded-xl border-gray-200 bg-white shadow-sm'>
      <CardHeader className='pb-4'>
        <CardTitle className='flex items-center gap-2 text-lg font-semibold text-gray-900'>
          <User className='h-5 w-5 text-purple-600' />
          Instructors & Schedule
        </CardTitle>
      </CardHeader>

      <CardContent className='space-y-6'>
        {/* Instructors Section */}
        <div className='space-y-4'>
          <h3 className='text-sm font-medium text-gray-700'>
            Assigned Instructors
          </h3>
          {instructors.map((instructor) => (
            <div
              key={instructor.id}
              className='flex items-center gap-3 rounded-lg bg-purple-50 p-3'
            >
              <Avatar className='h-10 w-10'>
                <AvatarImage
                  src={instructor.avatar || '/placeholder.svg'}
                  alt={instructor.name}
                />
                <AvatarFallback className='bg-purple-100 font-medium text-purple-700'>
                  {instructor.name
                    .split(' ')
                    .map((n) => n[0])
                    .join('')}
                </AvatarFallback>
              </Avatar>
              <div className='flex-1'>
                <p className='font-medium text-gray-900'>{instructor.name}</p>
                <p className='text-sm text-gray-600'>{instructor.timeBlock}</p>
              </div>
            </div>
          ))}
        </div>

        <Separator />

        {/* Recurring Lesson Info */}
        {schedule.isRecurring && (
          <div className='space-y-3 rounded-lg border border-purple-200 bg-purple-50 p-4'>
            <div className='flex items-center gap-2'>
              <Badge
                variant='outline'
                className='border-purple-200 bg-purple-100 text-purple-700'
              >
                Recurring Lesson
              </Badge>
              <span className='text-sm font-medium text-purple-700'>
                {schedule.recurrencePattern === 'weekly'
                  ? 'Every Week'
                  : schedule.recurrencePattern === 'biweekly'
                    ? 'Every 2 Weeks'
                    : 'Monthly'}
              </span>
            </div>

            <div className='grid grid-cols-2 gap-3 text-sm'>
              <div>
                <span className='font-medium text-purple-600'>Pattern:</span>
                <p className='text-purple-800'>
                  {schedule.type} on {schedule.dayOfWeek}s
                </p>
              </div>
              <div>
                <span className='font-medium text-purple-600'>Progress:</span>
                <p className='text-purple-800'>
                  {schedule.completedSessions} of {schedule.totalSessions}{' '}
                  sessions
                </p>
              </div>
            </div>

            {/* Progress Bar */}
            <div className='h-2 w-full rounded-full bg-purple-200'>
              <div
                className='h-2 rounded-full bg-purple-600 transition-all duration-300'
                style={{
                  width: `${(schedule.completedSessions / schedule.totalSessions) * 100}%`,
                }}
              />
            </div>
          </div>
        )}

        {/* Schedule Details */}
        <div className='space-y-3'>
          <div className='flex items-center justify-between'>
            <span className='text-sm text-gray-500'>Lesson Type</span>
            <Badge
              variant='outline'
              className='border-purple-200 bg-purple-50 text-purple-700'
            >
              {schedule.type}
            </Badge>
          </div>

          <div className='flex items-center justify-between'>
            <span className='text-sm text-gray-500'>Day of Week</span>
            <div className='flex items-center gap-2'>
              <div className='flex h-8 w-8 items-center justify-center rounded-full bg-purple-100'>
                <span className='text-xs font-bold text-purple-700'>
                  {schedule.dayOfWeek.substring(0, 3).toUpperCase()}
                </span>
              </div>
              <span className='text-sm font-medium text-gray-900'>
                {schedule.dayOfWeek}
              </span>
            </div>
          </div>

          <div className='flex items-center justify-between'>
            <span className='text-sm text-gray-500'>Start Date</span>
            <span className='text-sm font-medium text-gray-900'>
              {schedule.startDate}
            </span>
          </div>
          <div className='flex items-center justify-between'>
            <span className='text-sm text-gray-500'>End Date</span>
            <span className='text-sm font-medium text-gray-900'>
              {schedule.endDate}
            </span>
          </div>
          <div className='flex items-center justify-between'>
            <span className='text-sm text-gray-500'>Next Occurrence</span>
            <Button
              variant='link'
              className='h-auto p-0 text-sm font-medium text-blue-600'
            >
              {schedule.nextOccurrence}
            </Button>
          </div>
        </div>

        {/* Quick Actions */}
        {schedule.isRecurring && (
          <div className='border-t border-gray-200 pt-2'>
            <div className='flex gap-2'>
              <Button
                variant='outline'
                size='sm'
                className='h-8 flex-1 text-xs'
              >
                Edit Series
              </Button>
              <Button
                variant='outline'
                size='sm'
                className='h-8 flex-1 text-xs'
              >
                Skip Session
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
