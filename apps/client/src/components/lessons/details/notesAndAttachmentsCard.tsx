// LessonNotesAndAttachmentsCard.tsx
import { Paperclip, Upload, X } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';

interface Attachment {
  id: string;
  name: string;
  size: string;
}

interface NotesAndAttachmentsCardProps {
  notes: string;
  setNotes: (value: string) => void;
  attachments: Array<Attachment>;
}

export const NotesAndAttachmentsCard = ({
  notes,
  setNotes,
  attachments,
}: NotesAndAttachmentsCardProps) => {
  return (
    <div className='space-y-6'>
      {/* Lesson Notes Card */}
      <Card className='rounded-xl border-gray-200 bg-white shadow-sm'>
        <CardHeader className='pb-4'>
          <CardTitle className='text-lg font-semibold text-gray-900'>
            Lesson Notes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            value={notes}
            onChange={(e) => {
              setNotes(e.target.value);
            }}
            placeholder='Add lesson notes and special instructions...'
            className='min-h-[120px] resize-none border-gray-200 bg-gray-50 focus:border-blue-500 focus:ring-blue-500'
          />
        </CardContent>
      </Card>

      {/* Attachments Card */}
      <Card className='rounded-xl border-gray-200 bg-white shadow-sm'>
        <CardHeader className='pb-4'>
          <CardTitle className='text-lg font-semibold text-gray-900'>
            Attachments
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            {/* Existing Attachments */}
            {attachments.map((attachment) => (
              <div
                key={attachment.id}
                className='flex items-center gap-3 rounded-lg bg-gray-50 p-3'
              >
                <Paperclip className='h-4 w-4 text-gray-400' />
                <div className='flex-1'>
                  <p className='text-sm font-medium text-gray-900'>
                    {attachment.name}
                  </p>
                  <p className='text-xs text-gray-500'>{attachment.size}</p>
                </div>
                <Button variant='ghost' size='sm' className='h-8 w-8 p-0'>
                  <X className='h-4 w-4' />
                </Button>
              </div>
            ))}

            {/* Upload Zone */}
            <div className='cursor-pointer rounded-lg border-2 border-dashed border-gray-300 p-6 text-center transition-colors hover:border-gray-400'>
              <Upload className='mx-auto mb-2 h-8 w-8 text-gray-400' />
              <p className='text-sm text-gray-600'>
                Drop files here or click to upload
              </p>
              <p className='mt-1 text-xs text-gray-500'>
                PDF, JPG, PNG up to 10MB
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
