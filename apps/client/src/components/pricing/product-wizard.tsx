'use client';

import {
  <PERSON><PERSON><PERSON><PERSON>,
  ArrowRight,
  BookOpen,
  Calendar,
  Check,
  FileText,
  Package,
  Search,
  Tent,
  User,
} from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

import { useCreateInvoice } from '@/hooks/useInvoices';
import { useParents } from '@/hooks/useParents';

type ProductType = 'subscription' | 'package' | 'camp' | 'lesson' | 'invoice';

interface Parent {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string | null;
}

const productTypes = [
  {
    type: 'subscription' as ProductType,
    title: 'Subscription',
    description: 'Recurring monthly or 4-week plans with lesson allocations',
    icon: Calendar,
    color: 'bg-blue-50 text-blue-700 border-blue-200',
  },
  {
    type: 'package' as ProductType,
    title: 'Package',
    description: 'Fixed number of lessons with expiration date',
    icon: Package,
    color: 'bg-green-50 text-green-700 border-green-200',
  },
  {
    type: 'camp' as ProductType,
    title: 'Camp',
    description: 'Multi-day programs with capacity management',
    icon: Tent,
    color: 'bg-purple-50 text-purple-700 border-purple-200',
  },
  {
    type: 'lesson' as ProductType,
    title: 'Single Lesson',
    description: 'Individual lessons with progress tracking',
    icon: BookOpen,
    color: 'bg-orange-50 text-orange-700 border-orange-200',
  },
  {
    type: 'invoice' as ProductType,
    title: 'Invoice',
    description: 'Custom billing for horses or clients',
    icon: FileText,
    color: 'bg-gray-50 text-gray-700 border-gray-200',
  },
];

const calendarOptions = [
  'Arena 1',
  'Arena 2',
  'Trail Rides',
  'Jumping Course',
  'Dressage Ring',
  'Round Pen',
];

interface ProductWizardProps {
  onClose: () => void;
}

export function ProductWizard({ onClose }: ProductWizardProps) {
  const [step, setStep] = useState(1);
  const [productType, setProductType] = useState<ProductType>('invoice');
  const [formData, setFormData] = useState({
    name: '',
    price: '',
    description: '',
    cycle: '',
    lessonsPerCycle: '',
    expirationDate: '',
    dateRange: '',
    dailyTimes: '',
    capacity: '',
    calendarAccess: [] as Array<string>,
    makeupPolicy: '',
    cancellationPolicy: '',
  });
  const [selectedParent, setSelectedParent] = useState<Parent | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState<string | null>(null);

  const {
    data: parentsData,
    isLoading: isLoadingParents,
    error: parentsError,
  } = useParents({ page: 1, limit: 100 });
  const parents = parentsData?.parents || [];
  const createInvoiceMutation = useCreateInvoice();

  // Filter parents based on search term
  const filteredParents = parents.filter((parent) => {
    const searchLower = searchTerm.toLowerCase().trim();
    const fullName = `${parent.first_name} ${parent.last_name}`.toLowerCase();
    const firstName = parent.first_name.toLowerCase();
    const lastName = parent.last_name.toLowerCase();
    const email = parent.email.toLowerCase();

    return (
      fullName.includes(searchLower) ||
      firstName.includes(searchLower) ||
      lastName.includes(searchLower) ||
      email.includes(searchLower)
    );
  });

  // Debug logging
  console.log('Parents data:', parentsData);
  console.log('Parents array:', parents);
  console.log('Search term:', searchTerm);
  console.log('Filtered parents:', filteredParents);

  const handleNext = () => {
    if (step === 1 && productType) {
      setStep(2);
    } else if (step === 2) {
      // For invoice, require parent selection
      if (productType === 'invoice' && !selectedParent) {
        return;
      }
      setStep(3);
    }
  };

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const handleSave = async () => {
    if (productType !== 'invoice' || !selectedParent) {
      return;
    }

    setError(null);

    try {
      await createInvoiceMutation.mutateAsync({
        userId: selectedParent.id,
        lineItems: [
          {
            name: formData.name,
            amount: parseFloat(formData.price),
            type: 'invoice',
            quantity: 1,
            total: parseFloat(formData.price),
          },
        ],
        type: 'manual',
        paymentMethod: 'card',
      });

      // Success! Close the wizard
      console.log('Invoice created successfully');
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const renderStepContent = () => {
    if (step === 1) {
      return (
        <div className='space-y-4'>
          <div>
            <h3 className='mb-2 text-lg font-semibold text-gray-900'>
              Choose Product Type
            </h3>
            <p className='mb-6 text-gray-600'>
              Select the type of product or service you want to create
            </p>
          </div>

          <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
            {productTypes.map((type) => {
              const Icon = type.icon;
              return (
                <Card
                  key={type.type}
                  className={`cursor-pointer rounded-xl border-2 transition-all hover:shadow-md ${
                    productType === type.type
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => {
                    setProductType(type.type);
                  }}
                >
                  <CardHeader className='pb-3'>
                    <CardTitle className='flex items-center gap-3'>
                      <div className={`rounded-lg p-2 ${type.color}`}>
                        <Icon className='h-5 w-5' />
                      </div>
                      {type.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className='text-sm text-gray-600'>{type.description}</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      );
    }

    if (step === 2) {
      return (
        <div className='space-y-6'>
          <div>
            <h3 className='mb-2 text-lg font-semibold text-gray-900'>
              {productTypes.find((t) => t.type === productType)?.title} Details
            </h3>
            <p className='text-gray-600'>
              Configure your {productType} settings
            </p>
          </div>

          <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
            <div className='space-y-4'>
              <div>
                <Label htmlFor='name'>Name</Label>
                <Input
                  id='name'
                  value={formData.name}
                  onChange={(e) => {
                    setFormData({ ...formData, name: e.target.value });
                  }}
                  placeholder='Enter product name'
                  className='rounded-xl'
                />
              </div>

              <div>
                <Label htmlFor='price'>Price ($)</Label>
                <Input
                  id='price'
                  type='number'
                  value={formData.price}
                  onChange={(e) => {
                    setFormData({ ...formData, price: e.target.value });
                  }}
                  placeholder='0.00'
                  className='rounded-xl'
                />
              </div>

              {/* Parent Selector for Invoice */}
              {productType === 'invoice' && (
                <div className='space-y-3'>
                  <Label>Select Parent</Label>
                  <div className='relative'>
                    <Search className='absolute top-3 left-3 h-4 w-4 text-gray-400' />
                    <Input
                      placeholder='Search parents...'
                      value={searchTerm}
                      onChange={(e) => {
                        setSearchTerm(e.target.value);
                      }}
                      className='rounded-xl pl-10'
                    />
                  </div>

                  <div className='max-h-48 space-y-2 overflow-y-auto rounded-xl border p-3'>
                    {isLoadingParents ? (
                      <div className='py-4 text-center text-gray-500'>
                        Loading parents...
                      </div>
                    ) : parentsError ? (
                      <div className='py-4 text-center text-red-500'>
                        Error loading parents: {parentsError.message}
                      </div>
                    ) : filteredParents.length === 0 ? (
                      <div className='py-4 text-center text-gray-500'>
                        {searchTerm
                          ? `No parents found matching "${searchTerm}" (${parents.length} total parents)`
                          : `No parents found (${parents.length} total parents)`}
                      </div>
                    ) : (
                      <>
                        <div className='mb-2 text-xs text-gray-500'>
                          Showing {filteredParents.length} of {parents.length}{' '}
                          parents
                        </div>
                        {filteredParents.map((parent) => (
                          <div
                            key={parent.id}
                            className={`flex cursor-pointer items-center gap-3 rounded-lg p-3 transition-colors ${
                              selectedParent?.id === parent.id
                                ? 'border border-blue-200 bg-blue-50'
                                : 'border border-transparent hover:bg-gray-50'
                            }`}
                            onClick={() => {
                              setSelectedParent(parent);
                            }}
                          >
                            <div className='flex-shrink-0'>
                              <div className='flex h-8 w-8 items-center justify-center rounded-full bg-blue-100'>
                                <User className='h-4 w-4 text-blue-600' />
                              </div>
                            </div>
                            <div className='min-w-0 flex-1'>
                              <div className='font-medium text-gray-900'>
                                {parent.first_name} {parent.last_name}
                              </div>
                              <div className='truncate text-sm text-gray-500'>
                                {parent.email}
                              </div>
                              {parent.phone && (
                                <div className='text-sm text-gray-500'>
                                  {parent.phone}
                                </div>
                              )}
                            </div>
                            {selectedParent?.id === parent.id && (
                              <Check className='h-5 w-5 text-blue-600' />
                            )}
                          </div>
                        ))}
                      </>
                    )}
                  </div>
                </div>
              )}

              {productType === 'subscription' && (
                <>
                  <div>
                    <Label htmlFor='cycle'>Billing Cycle</Label>
                    <Select
                      value={formData.cycle}
                      onValueChange={(value) => {
                        setFormData({ ...formData, cycle: value });
                      }}
                    >
                      <SelectTrigger className='rounded-xl'>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='monthly'>Monthly</SelectItem>
                        <SelectItem value='4-week'>4-Week</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor='lessons'>Lessons per Cycle</Label>
                    <Input
                      id='lessons'
                      type='number'
                      value={formData.lessonsPerCycle}
                      onChange={(e) => {
                        setFormData({
                          ...formData,
                          lessonsPerCycle: e.target.value,
                        });
                      }}
                      placeholder='4'
                      className='rounded-xl'
                    />
                  </div>
                </>
              )}

              {productType === 'package' && (
                <div>
                  <Label htmlFor='expiration'>Expiration Date</Label>
                  <Input
                    id='expiration'
                    type='date'
                    value={formData.expirationDate}
                    onChange={(e) => {
                      setFormData({
                        ...formData,
                        expirationDate: e.target.value,
                      });
                    }}
                    className='rounded-xl'
                  />
                </div>
              )}

              {productType === 'camp' && (
                <>
                  <div>
                    <Label htmlFor='dateRange'>Date Range</Label>
                    <Input
                      id='dateRange'
                      value={formData.dateRange}
                      onChange={(e) => {
                        setFormData({ ...formData, dateRange: e.target.value });
                      }}
                      placeholder='June 15-19, 2024'
                      className='rounded-xl'
                    />
                  </div>

                  <div>
                    <Label htmlFor='times'>Daily Times</Label>
                    <Input
                      id='times'
                      value={formData.dailyTimes}
                      onChange={(e) => {
                        setFormData({
                          ...formData,
                          dailyTimes: e.target.value,
                        });
                      }}
                      placeholder='9:00 AM - 3:00 PM'
                      className='rounded-xl'
                    />
                  </div>

                  <div>
                    <Label htmlFor='capacity'>Capacity</Label>
                    <Input
                      id='capacity'
                      type='number'
                      value={formData.capacity}
                      onChange={(e) => {
                        setFormData({ ...formData, capacity: e.target.value });
                      }}
                      placeholder='20'
                      className='rounded-xl'
                    />
                  </div>
                </>
              )}
            </div>

            <div className='space-y-4'>
              {(productType === 'subscription' ||
                productType === 'package') && (
                <>
                  <div>
                    <Label>Calendar Access</Label>
                    <div className='mt-2 space-y-2'>
                      {calendarOptions.map((option) => (
                        <div
                          key={option}
                          className='flex items-center space-x-2'
                        >
                          <Checkbox
                            id={option}
                            checked={formData.calendarAccess.includes(option)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setFormData({
                                  ...formData,
                                  calendarAccess: [
                                    ...formData.calendarAccess,
                                    option,
                                  ],
                                });
                              } else {
                                setFormData({
                                  ...formData,
                                  calendarAccess:
                                    formData.calendarAccess.filter(
                                      (item) => item !== option,
                                    ),
                                });
                              }
                            }}
                          />
                          <Label htmlFor={option} className='text-sm'>
                            {option}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor='makeup'>Makeup Policy</Label>
                    <Textarea
                      id='makeup'
                      value={formData.makeupPolicy}
                      onChange={(e) => {
                        setFormData({
                          ...formData,
                          makeupPolicy: e.target.value,
                        });
                      }}
                      placeholder='Describe makeup lesson policy...'
                      className='rounded-xl'
                      rows={3}
                    />
                  </div>

                  <div>
                    <Label htmlFor='cancellation'>Cancellation Policy</Label>
                    <Textarea
                      id='cancellation'
                      value={formData.cancellationPolicy}
                      onChange={(e) => {
                        setFormData({
                          ...formData,
                          cancellationPolicy: e.target.value,
                        });
                      }}
                      placeholder='Describe cancellation policy...'
                      className='rounded-xl'
                      rows={3}
                    />
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      );
    }

    if (step === 3) {
      return (
        <div className='space-y-6'>
          <div className='text-center'>
            <div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100'>
              <Check className='h-6 w-6 text-green-600' />
            </div>
            <h3 className='mb-2 text-lg font-semibold text-gray-900'>
              Product Created Successfully!
            </h3>
            <p className='text-gray-600'>
              Your {productType} has been created and is ready to use.
            </p>
          </div>

          <Card className='rounded-xl bg-gray-50'>
            <CardHeader>
              <CardTitle className='text-base'>Summary</CardTitle>
            </CardHeader>
            <CardContent className='space-y-2'>
              <div className='flex justify-between'>
                <span className='text-gray-600'>Name:</span>
                <span className='font-medium'>{formData.name}</span>
              </div>
              <div className='flex justify-between'>
                <span className='text-gray-600'>Price:</span>
                <span className='font-medium'>${formData.price}</span>
              </div>
              {productType === 'invoice' && selectedParent && (
                <div className='flex justify-between'>
                  <span className='text-gray-600'>Parent:</span>
                  <span className='font-medium'>
                    {selectedParent.first_name} {selectedParent.last_name}
                  </span>
                </div>
              )}
              {productType === 'subscription' && (
                <>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>Cycle:</span>
                    <span className='font-medium'>{formData.cycle}</span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>Lessons:</span>
                    <span className='font-medium'>
                      {formData.lessonsPerCycle} per cycle
                    </span>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {productType === 'subscription' && (
            <div className='text-center'>
              <Button className='rounded-xl bg-blue-600 hover:bg-blue-700'>
                Enroll Client
              </Button>
            </div>
          )}
        </div>
      );
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className='max-h-[90vh] max-w-4xl overflow-y-auto rounded-xl'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <div className='flex items-center gap-2'>
              {step > 1 && (
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={handleBack}
                  className='rounded-lg'
                >
                  <ArrowLeft className='h-4 w-4' />
                </Button>
              )}
              Create New Product
            </div>
            <div className='ml-auto flex items-center gap-2'>
              {[1, 2, 3].map((stepNum) => (
                <div
                  key={stepNum}
                  className={`flex h-8 w-8 items-center justify-center rounded-full text-sm ${
                    stepNum === step
                      ? 'bg-blue-600 text-white'
                      : stepNum < step
                        ? 'bg-green-600 text-white'
                        : 'bg-gray-200 text-gray-600'
                  }`}
                >
                  {stepNum < step ? <Check className='h-4 w-4' /> : stepNum}
                </div>
              ))}
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className='py-6'>
          {error && (
            <div className='mb-4 rounded-lg bg-red-50 p-4 text-red-700'>
              {error}
            </div>
          )}
          {renderStepContent()}
        </div>

        <div className='flex justify-between border-t pt-6'>
          <Button
            variant='outline'
            onClick={onClose}
            disabled={
              createInvoiceMutation.isPending ||
              productType !== 'invoice' ||
              !selectedParent
            }
            className='rounded-xl bg-transparent'
          >
            Cancel
          </Button>
          <div className='flex gap-2'>
            {step < 3 ? (
              <Button
                onClick={handleNext}
                disabled={
                  (step === 1 && !productType) ||
                  (step === 2 && productType === 'invoice' && !selectedParent)
                }
                className='rounded-xl bg-blue-600 hover:bg-blue-700'
              >
                Next
                <ArrowRight className='ml-2 h-4 w-4' />
              </Button>
            ) : (
              <Button
                onClick={handleSave}
                disabled={
                  createInvoiceMutation.isPending ||
                  productType !== 'invoice' ||
                  !selectedParent
                }
                className='rounded-xl bg-green-600 hover:bg-green-700'
              >
                {createInvoiceMutation.isPending
                  ? 'Creating Invoice...'
                  : 'Create Invoice'}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
