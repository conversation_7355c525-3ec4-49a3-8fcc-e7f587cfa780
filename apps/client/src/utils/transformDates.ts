// Helper function to transform dates from strings to Date objects
const transformDates = (data: any) => {
  if (data && typeof data === 'object') {
    const transformed = { ...data };
    if (transformed.createdAt && typeof transformed.createdAt === 'string') {
      transformed.createdAt = new Date(transformed.createdAt);
    }
    if (transformed.updatedAt && typeof transformed.updatedAt === 'string') {
      transformed.updatedAt = new Date(transformed.updatedAt);
    }
    // Handle arrays (for list operations)
    if (transformed.items && Array.isArray(transformed.items)) {
      transformed.items = transformed.items.map(transformDates);
    }
    return transformed;
  }
  return data;
};

export default transformDates;
