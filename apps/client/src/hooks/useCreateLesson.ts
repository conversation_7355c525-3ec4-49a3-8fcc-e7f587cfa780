import { useMutation } from '@tanstack/react-query';
import { type CreateLessonPayload } from 'shared/src/types/lessons';

import { hcWithType } from '../../../server/src/client';
import { useAuth } from './useAuthStore';

const SERVER_URL = import.meta.env.VITE_SERVER_URL || 'http://localhost:3000';
const client = hcWithType(SERVER_URL);

export const useCreateLesson = () => {
  const { accessToken } = useAuth();

  return useMutation({
    mutationFn: async (lesson: CreateLessonPayload) => {
      console.log('🚀 ~ useCreateLesson ~ lesson:', lesson);
      const res = await client.api.v1.lessons.$post({
        body: lesson,
        header: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const rawData = await res.json();

      if (!res.ok) {
        throw new Error(rawData.error?.message);
      }

      return rawData;
    },
    onSuccess: (data) => {
      console.log('🚀 ~ useCreateLesson ~ data:', data);
    },
    onError: (error) => {
      console.log('🚀 ~ useCreateLesson ~ error:', error);
    },
  });
};
