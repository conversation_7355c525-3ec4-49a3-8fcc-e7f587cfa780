import { useQuery, type UseQueryOptions } from '@tanstack/react-query';

import { useAuth } from '@/hooks/useAuthStore';

import { hcWithType } from '../../../server/src/client';
import type { HorseGetHorse } from '../../../shared/src/types';

// Extended Error type to include server error details
interface ServerError extends Error {
  code?: string;
  statusCode?: number;
}

const SERVER_URL = import.meta.env.VITE_SERVER_URL || 'http://localhost:3000';
const client = hcWithType(SERVER_URL);

// Query Keys
export const horsesQueryKeys = {
  all: ['horses'] as const,
  lists: () => [...horsesQueryKeys.all, 'list'] as const,
  list: (filters: HorseGetHorse.typeQueryParams) =>
    [...horsesQueryKeys.lists(), { filters }] as const,
} as const;

export const useHorses = (
  queryParams: HorseGetHorse.typeQueryParams = {},
  options?: UseQueryOptions<
    HorseGetHorse.typeResultData,
    ServerError,
    HorseGetHorse.typeResultData,
    ReturnType<typeof horsesQueryKeys.list>
  >,
) => {
  const { page = '1', limit = '10' } = queryParams;
  const { accessToken, studioId } = useAuth();

  return useQuery({
    queryKey: horsesQueryKeys.list(queryParams),
    queryFn: async (): Promise<HorseGetHorse.typeResultData> => {
      if (!accessToken) {
        throw new Error('No access token available');
      }

      const res = await client.api.v1.horses.$get({
        query: {
          page,
          limit,
          studio_id: studioId ?? '',
        },
        header: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const rawData = await res.json();

      if (!res.ok) {
        // If there's an error in the response, check if it has the expected structure
        const errorData = rawData as HorseGetHorse.typeResult;
        if (errorData.error) {
          const error: ServerError = new Error(errorData.error.message);
          // Attach additional error details
          error.code = errorData.error.code;
          error.statusCode = errorData.error.statusCode;
          throw error;
        }
        // Fallback error if the structure is unexpected
        throw new Error(`Failed to fetch horses with status ${res.status}`);
      }

      const responseData = rawData as HorseGetHorse.typeResult;

      if (!responseData.data) {
        throw new Error('No data received from server');
      }

      return responseData.data;
    },
    enabled: !!accessToken && !!studioId, // Only run query when we have an access token and studio_id
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    ...options,
  });
};
