import {
  useMutation,
  useQuery,
  useQueryClient,
  type UseQueryOptions,
} from '@tanstack/react-query';

import { useAuth } from '@/hooks/useAuthStore';

import { hcWithType } from '../../../server/src/client';

interface ServerError extends Error {
  code?: string;
  statusCode?: number;
}

const SERVER_URL = import.meta.env.VITE_SERVER_URL || 'http://localhost:3000';
const client = hcWithType(SERVER_URL);

export const invoicesQueryKeys = {
  all: ['invoices'] as const,
  lists: () => [...invoicesQueryKeys.all, 'list'] as const,
  list: (filters: { page?: number; limit?: number; status?: string }) =>
    [...invoicesQueryKeys.lists(), { filters }] as const,
};

export const useInvoices = (
  queryParams: { page?: number; limit?: number; status?: string } = {},
  options?: UseQueryOptions<
    any,
    ServerError,
    any,
    ReturnType<typeof invoicesQueryKeys.list>
  >,
) => {
  const { page = 1, limit = 10, status } = queryParams;
  const { accessToken } = useAuth();

  return useQuery({
    queryKey: invoicesQueryKeys.list(queryParams),
    queryFn: async (): Promise<any> => {
      if (!accessToken) {
        throw new Error('No access token available');
      }

      const query: any = {
        page: page.toString(),
        limit: limit.toString(),
      };

      if (status) {
        query.status = status;
      }

      const res = await client.api.v1.payments.invoices.$get({
        query,
        header: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const rawData = await res.json();

      if (!res.ok) {
        if (rawData.error) {
          const error: ServerError = new Error(rawData.error.message);
          if ((rawData.error as any).code)
            error.code = (rawData.error as any).code;
          if ((rawData.error as any).statusCode)
            error.statusCode = (rawData.error as any).statusCode;
          throw error;
        }
        throw new Error(`Failed to fetch invoices with status ${res.status}`);
      }

      if (!rawData.data) {
        throw new Error('No data received from server');
      }

      return rawData.data;
    },
    enabled: !!accessToken,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  });
};

interface CreateInvoiceData {
  userId: string;
  lineItems: Array<{
    name: string;
    amount: number;
    type: string;
    quantity: number;
    total: number;
  }>;
  type: string;
  paymentMethod: string;
}

export const useCreateInvoice = () => {
  const { accessToken } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateInvoiceData): Promise<any> => {
      if (!accessToken) {
        throw new Error('No access token available');
      }

      const res = await client.api.v1.payments['create-invoice'].$post({
        json: data,
        header: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const result = await res.json();

      if (!res.ok) {
        if (result.error) {
          const error: ServerError = new Error(result.error.message);
          if ((result.error as any).code)
            error.code = (result.error as any).code;
          if ((result.error as any).statusCode)
            error.statusCode = (result.error as any).statusCode;
          throw error;
        }
        throw new Error(`Failed to create invoice with status ${res.status}`);
      }

      return result.data;
    },
    onSuccess: () => {
      // Invalidate and refetch invoices list
      queryClient.invalidateQueries({ queryKey: invoicesQueryKeys.all });
    },
  });
};
