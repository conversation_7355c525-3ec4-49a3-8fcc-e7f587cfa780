import { useMutation, useQueryClient } from '@tanstack/react-query';

import { useAuth } from '@/hooks/useAuthStore';

import { hcWithType } from '../../../server/src/client';
import type { StudentCreateStudent } from '../../../shared/src/types';

interface ServerError extends Error {
  code?: string;
  statusCode?: number;
}

const SERVER_URL = import.meta.env.VITE_SERVER_URL || 'http://localhost:3000';
const client = hcWithType(SERVER_URL);

export const useCreateStudentMutation = () => {
  const queryClient = useQueryClient();
  const { accessToken } = useAuth();

  return useMutation({
    mutationFn: async (data: StudentCreateStudent.typePayloadInput) => {
      if (!accessToken) {
        throw new Error('No access token available');
      }

      const res = await client.api.v1.students.$post({
        json: data,
        header: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const rawData = await res.json();

      if (!res.ok) {
        // If there's an error in the response, check if it has the expected structure
        const errorData = rawData as StudentCreateStudent.typeResult;
        if (errorData.error) {
          const error: ServerError = new Error(errorData.error.message);
          // Attach additional error details
          error.code = errorData.error.code;
          error.statusCode = errorData.error.statusCode;
          throw error;
        }
        // Fallback error if the structure is unexpected
        throw new Error(`Create student failed with status ${res.status}`);
      }

      return rawData as StudentCreateStudent.typeResult;
    },
    onSuccess: () => {
      // Invalidate and refetch students queries when a new student is created
      void queryClient.invalidateQueries({ queryKey: ['students'] });
    },
  });
};
