import {
  useMutation,
  type UseMutationOptions,
  useQueryClient,
} from '@tanstack/react-query';

import transformDates from '@/utils/transformDates';

import { hcWithType } from '../../../server/src/client';
import type { AuthSignIn } from '../../../shared/src/types';

// Extended Error type to include server error details
interface ServerError extends Error {
  code?: string;
  statusCode?: number;
}

const SERVER_URL =
  import.meta.env.VITE_API_URL ||
  import.meta.env.VITE_SERVER_URL ||
  'http://localhost:3000';
const client = hcWithType(SERVER_URL);

// Query Keys
export const authQueryKeys = {
  all: ['auth'] as const,
  lists: () => [...authQueryKeys.all, 'list'] as const,
  list: (filters: Record<string, unknown>) =>
    [...authQueryKeys.lists(), { filters }] as const,
  details: () => [...authQueryKeys.all, 'detail'] as const,
  detail: (id: string | number) => [...authQueryKeys.details(), id] as const,
} as const;

// SignIn Auth Hook
export const useSignInAuth = (
  options?: UseMutationOptions<
    AuthSignIn.typeResult,
    ServerError,
    AuthSignIn.typePayload & { studio_id?: string }
  >,
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      data: AuthSignIn.typePayload & { studio_id?: string },
    ): Promise<AuthSignIn.typeResult> => {
      const { studio_id, ...payloadData } = data;

      // Get studio_id from URL search params if not provided directly
      const urlParams = new URLSearchParams(window.location.search);
      const studioIdParam = studio_id ?? urlParams.get('studio_id');

      if (!studioIdParam) {
        throw new Error('Studio ID is required');
      }

      const res = await client.api.v1.auth.signin.$post({
        json: payloadData,
        query: { studio_id: studioIdParam },
      });

      const rawData = await res.json();

      if (!res.ok) {
        // If there's an error in the response, check if it has the expected structure
        const errorData = rawData as AuthSignIn.typeResult;
        if (errorData.error) {
          const error: ServerError = new Error(errorData.error.message);
          // Attach additional error details
          error.code = errorData.error.code;
          error.statusCode = errorData.error.statusCode;
          throw error;
        }
        // Fallback error if the structure is unexpected
        throw new Error(`Sign in failed with status ${res.status}`);
      }

      // Transform dates from strings to Date objects
      if (rawData.data) {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        rawData.data = transformDates(rawData.data);
      }
      return rawData as AuthSignIn.typeResult;
    },
    onSuccess: (_data, _variables) => {
      // Invalidate and refetch relevant queries
      void queryClient.invalidateQueries({ queryKey: authQueryKeys.all });

      // Refresh relevant data
      void queryClient.invalidateQueries({ queryKey: authQueryKeys.lists() });
    },
    ...options,
  });
};
