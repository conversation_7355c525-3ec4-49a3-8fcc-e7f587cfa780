import { useMutation, useQueryClient } from '@tanstack/react-query';

import { hcWithType } from '../../../server/src/client';
import type { AuthSignUp } from '../../../shared/src/types';
import { parentsQueryKeys } from './useParents';

interface ServerError extends Error {
  code?: string;
  statusCode?: number;
}

const SERVER_URL = import.meta.env.VITE_SERVER_URL || 'http://localhost:3000';
const client = hcWithType(SERVER_URL);

export const useParentMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      data,
      studio_id,
    }: {
      data: AuthSignUp.typePayload;
      studio_id: string;
    }) => {
      const res = await client.api.v1.auth.signup.$post({
        json: data,
        query: { studio_id },
      });
      const rawData = await res.json();

      if (!res.ok) {
        // If there's an error in the response, check if it has the expected structure
        const errorData = rawData as AuthSignUp.typeResult;
        if (errorData.error) {
          const error: ServerError = new Error(errorData.error.message);
          // Attach additional error details
          error.code = errorData.error.code;
          error.statusCode = errorData.error.statusCode;
          throw error;
        }
        // Fallback error if the structure is unexpected
        throw new Error(`Sign in failed with status ${res.status}`);
      }

      return rawData as AuthSignUp.typeResult;
    },
    onSuccess: () => {
      // Invalidate and refetch parents queries when a new parent is created
      void queryClient.invalidateQueries({ queryKey: parentsQueryKeys.all });
    },
  });
};
