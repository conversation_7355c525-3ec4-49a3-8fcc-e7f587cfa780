import { useQuery, type UseQueryOptions } from '@tanstack/react-query';

import { useAuth } from '@/hooks/useAuthStore';

import { hcWithType } from '../../../server/src/client';
import type { AdminGetParents } from '../../../shared/src/types';

// Extended Error type to include server error details
interface ServerError extends Error {
  code?: string;
  statusCode?: number;
}

const SERVER_URL = import.meta.env.VITE_SERVER_URL || 'http://localhost:3000';
const client = hcWithType(SERVER_URL);

// Query Keys
export const parentsQueryKeys = {
  all: ['parents'] as const,
  lists: () => [...parentsQueryKeys.all, 'list'] as const,
  list: (filters: AdminGetParents.GetParents.QueryParams) =>
    [...parentsQueryKeys.lists(), { filters }] as const,
} as const;

export const useParents = (
  queryParams: AdminGetParents.GetParents.QueryParams = {},
  options?: UseQueryOptions<
    AdminGetParents.GetParents.ResponseData,
    ServerError,
    AdminGetParents.GetParents.ResponseData,
    ReturnType<typeof parentsQueryKeys.list>
  >,
) => {
  const { page = 1, limit = 10 } = queryParams;
  const { accessToken } = useAuth();

  return useQuery({
    queryKey: parentsQueryKeys.list(queryParams),
    queryFn: async (): Promise<AdminGetParents.GetParents.ResponseData> => {
      if (!accessToken) {
        throw new Error('No access token available');
      }

      const res = await client.api.v1.admin.parents.$get({
        query: {
          page: page.toString(),
          limit: limit.toString(),
        },
        header: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const rawData = await res.json();

      if (!res.ok) {
        // If there's an error in the response, check if it has the expected structure
        const errorData = rawData as AdminGetParents.GetParents.ApiResponse;
        if (errorData.error) {
          const error: ServerError = new Error(errorData.error.message);
          // Attach additional error details
          error.code = errorData.error.code;
          error.statusCode = errorData.error.statusCode;
          throw error;
        }
        // Fallback error if the structure is unexpected
        throw new Error(`Failed to fetch parents with status ${res.status}`);
      }

      const responseData = rawData as AdminGetParents.GetParents.ApiResponse;

      if (!responseData.data) {
        throw new Error('No data received from server');
      }

      return responseData.data;
    },
    enabled: !!accessToken, // Only run query when we have an access token
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    ...options,
  });
};
