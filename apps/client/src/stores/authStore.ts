import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import transformDates from '@/utils/transformDates';

import type { AuthVerify } from '../../../shared/src/types';

interface User {
  email: string;
  first_name: string;
  last_name: string;
  role: 'admin' | 'instructor' | 'student' | 'parent' | 'super-admin' | 'owner';
  studio_id: string;
  phone?: string;
  address?: string;
  date_of_birth?: string;
  profile_image?: string;
  emergency_contact?: string;
  country_code?: string;
  created_at: Date;
  deleted_at?: Date;
  updated_at?: Date;
}

interface AuthState {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  isVerifying: boolean;
  studioId: string | null; // Store studio_id for redirects
}

interface AuthActions {
  login: (
    accessToken: string,
    refreshToken: string,
    user: User,
    studioId?: string,
  ) => void;
  logout: () => void;
  updateUser: (user: User) => void;
  setLoading: (loading: boolean) => void;
  setVerifying: (verifying: boolean) => void;
  verifyToken: () => Promise<boolean>;
  clearAuth: () => void;
  setStudioId: (studioId: string) => void;
  getRedirectUrl: () => string;
}

type AuthStore = AuthState & AuthActions;

const SERVER_URL = import.meta.env.VITE_SERVER_URL || 'http://localhost:3000';

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      accessToken: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: true,
      isVerifying: false,
      studioId: null,

      // Actions
      login: (
        accessToken: string,
        refreshToken: string,
        user: User,
        studioId?: string,
      ) => {
        const currentState = get();
        const finalStudioId =
          studioId ?? currentState.studioId ?? user.studio_id;

        set({
          accessToken,
          refreshToken,
          user,
          isAuthenticated: true,
          isLoading: false,
          studioId: finalStudioId,
        });
      },

      logout: () => {
        const currentState = get();
        set({
          user: null,
          accessToken: null,
          refreshToken: null,
          isAuthenticated: false,
          isLoading: false,
          // Preserve studioId for redirect purposes
          studioId: currentState.studioId,
        });
      },

      updateUser: (user: User) => {
        set({ user });
      },

      setLoading: (isLoading: boolean) => {
        set({ isLoading });
      },

      setVerifying: (isVerifying: boolean) => {
        set({ isVerifying });
      },

      clearAuth: () => {
        set({
          user: null,
          accessToken: null,
          refreshToken: null,
          isAuthenticated: false,
          isLoading: false,
          studioId: null, // Clear studioId on complete auth clear
        });
      },

      // Verify token with server using /verify endpoint
      verifyToken: async (): Promise<boolean> => {
        const { accessToken } = get();

        if (!accessToken) {
          set({ isAuthenticated: false, isLoading: false });
          return false;
        }

        set({ isVerifying: true });

        try {
          const response = await fetch(`${SERVER_URL}/api/v1/auth/verify`, {
            method: 'GET',
            headers: {
              Authorization: `Bearer ${accessToken}`,
              'Content-Type': 'application/json',
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const rawData = (await response.json()) as AuthVerify.typeResult;

          if (rawData.error) {
            throw new Error(rawData.error.message || 'Verification failed');
          }

          // Transform dates from strings to Date objects and update user data from server response
          if (rawData.data) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            const transformedData = transformDates(rawData.data);

            set({
              user: transformedData as User,
              isAuthenticated: true,
              isLoading: false,
              isVerifying: false,
            });
            return true;
          } else {
            throw new Error('No user data in response');
          }
        } catch (error) {
          console.error('Token verification failed:', error);
          // Clear invalid auth data but preserve studioId for redirect
          const currentState = get();
          set({
            user: null,
            accessToken: null,
            refreshToken: null,
            isAuthenticated: false,
            isLoading: false,
            isVerifying: false,
            studioId: currentState.studioId, // Preserve studioId for redirect
          });
          return false;
        }
      },

      setStudioId: (studioId: string) => {
        set({ studioId });
      },

      getRedirectUrl: (): string => {
        const currentState = get();
        const studioId = currentState.studioId ?? 'required';
        return `/login?studio_id=${studioId}`;
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        accessToken: state.accessToken,
        refreshToken: state.refreshToken,
        studioId: state.studioId, // Persist studioId
      }),
    },
  ),
);

// Initialize auth verification on app load
export const initializeAuth = async () => {
  const store = useAuthStore.getState();

  if (store.accessToken) {
    await store.verifyToken();
  } else {
    store.setLoading(false);
  }
};
