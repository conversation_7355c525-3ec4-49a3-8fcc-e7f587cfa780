import { DollarSign, Package, Plus, Settings, Users } from 'lucide-react';
import { useState } from 'react';

import { InvoiceList } from '@/components/invoices/InvoiceList';
import { ProductWizard } from '@/components/pricing/product-wizard';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const products = [
  {
    id: 1,
    name: 'Bronze Subscription',
    type: 'Subscription',
    price: 350,
    status: 'Active',
    students: 24,
  },
  {
    id: 2,
    name: 'Silver Package',
    type: 'Package',
    price: 400,
    status: 'Active',
    students: 12,
  },
  {
    id: 3,
    name: 'Summer Camp 2024',
    type: 'Camp',
    price: 800,
    status: 'Active',
    students: 15,
  },
  {
    id: 4,
    name: 'Private Lesson',
    type: 'Single Lesson',
    price: 75,
    status: 'Active',
    students: 8,
  },
];

const subscriptions = [
  {
    id: 1,
    student: 'Emma Johnson',
    plan: 'Bronze',
    status: 'Active',
    nextBilling: '2024-02-15',
    amount: 350,
  },
  {
    id: 2,
    student: 'Michael Chen',
    plan: 'Silver',
    status: 'Active',
    nextBilling: '2024-02-20',
    amount: 450,
  },
  {
    id: 3,
    student: 'Sarah Williams',
    plan: 'Gold',
    status: 'Paused',
    nextBilling: '2024-03-01',
    amount: 550,
  },
];

export const Pricing = () => {
  const [showWizard, setShowWizard] = useState(false);

  return (
    <div className='min-h-screen bg-gray-50'>
      <div className='container mx-auto p-6'>
        <div className='space-y-6'>
          <div className='flex items-center justify-between'>
            <div>
              <h1 className='text-3xl font-bold text-gray-900'>
                Pricing & Packages
              </h1>
              <p className='text-gray-600'>
                Manage your academy&apos;s products and services
              </p>
            </div>
            <Button
              onClick={() => {
                setShowWizard(true);
              }}
              className='rounded-xl bg-blue-600 text-white shadow-lg transition-all hover:bg-blue-700 hover:shadow-xl'
            >
              <Plus className='mr-2 h-4 w-4' />
              New Product
            </Button>
          </div>

          <div className='grid grid-cols-1 gap-6 md:grid-cols-4'>
            <Card className='rounded-xl border-0 bg-white shadow-sm'>
              <CardHeader className='pb-3'>
                <CardTitle className='flex items-center gap-2 text-sm font-medium text-gray-600'>
                  <Package className='h-4 w-4' />
                  Total Products
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold text-gray-900'>24</div>
                <p className='text-xs text-green-600'>+2 this month</p>
              </CardContent>
            </Card>

            <Card className='rounded-xl border-0 bg-white shadow-sm'>
              <CardHeader className='pb-3'>
                <CardTitle className='flex items-center gap-2 text-sm font-medium text-gray-600'>
                  <Users className='h-4 w-4' />
                  Active Students
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold text-gray-900'>156</div>
                <p className='text-xs text-green-600'>+12 this month</p>
              </CardContent>
            </Card>

            <Card className='rounded-xl border-0 bg-white shadow-sm'>
              <CardHeader className='pb-3'>
                <CardTitle className='flex items-center gap-2 text-sm font-medium text-gray-600'>
                  <DollarSign className='h-4 w-4' />
                  Monthly Revenue
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold text-gray-900'>$24,580</div>
                <p className='text-xs text-green-600'>+8% from last month</p>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue='products' className='space-y-6'>
            <TabsList className='rounded-xl border-0 bg-white p-1 shadow-sm'>
              <TabsTrigger
                value='products'
                className='rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700'
              >
                Products
              </TabsTrigger>
              <TabsTrigger
                value='subscriptions'
                className='rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700'
              >
                Active Subs
              </TabsTrigger>
              <TabsTrigger
                value='invoices'
                className='rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700'
              >
                Invoices
              </TabsTrigger>
            </TabsList>

            <TabsContent value='products'>
              <Card className='rounded-xl border-0 bg-white shadow-sm'>
                <CardHeader>
                  <CardTitle>Products & Services</CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Price</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Students</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {products.map((product) => (
                        <TableRow key={product.id}>
                          <TableCell className='font-medium'>
                            {product.name}
                          </TableCell>
                          <TableCell>
                            <Badge variant='outline' className='rounded-lg'>
                              {product.type}
                            </Badge>
                          </TableCell>
                          <TableCell>${product.price}</TableCell>
                          <TableCell>
                            <Badge className='rounded-lg bg-green-100 text-green-800'>
                              {product.status}
                            </Badge>
                          </TableCell>
                          <TableCell>{product.students}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value='subscriptions'>
              <Card className='rounded-xl border-0 bg-white shadow-sm'>
                <CardHeader>
                  <CardTitle>Active Subscriptions</CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Student</TableHead>
                        <TableHead>Plan</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Next Billing</TableHead>
                        <TableHead>Amount</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {subscriptions.map((sub) => (
                        <TableRow key={sub.id}>
                          <TableCell className='font-medium'>
                            {sub.student}
                          </TableCell>
                          <TableCell>{sub.plan}</TableCell>
                          <TableCell>
                            <Badge
                              className={`rounded-lg ${
                                sub.status === 'Active'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-yellow-100 text-yellow-800'
                              }`}
                            >
                              {sub.status}
                            </Badge>
                          </TableCell>
                          <TableCell>{sub.nextBilling}</TableCell>
                          <TableCell>${sub.amount}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value='invoices'>
              <Card className='rounded-xl border-0 bg-white shadow-sm'>
                <CardHeader>
                  <CardTitle>Recent Invoices</CardTitle>
                </CardHeader>
                <CardContent>
                  <InvoiceList />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value='settings'>
              <Card className='rounded-xl border-0 bg-white shadow-sm'>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <Settings className='h-5 w-5' />
                    Academy Settings
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className='space-y-4'>
                    <div>
                      <h3 className='mb-2 font-medium text-gray-900'>
                        General Settings
                      </h3>
                      <p className='text-sm text-gray-600'>
                        Configure academy policies, payment methods, and
                        notification preferences.
                      </p>
                    </div>
                    <Button
                      variant='outline'
                      className='rounded-xl bg-transparent'
                    >
                      Configure Settings
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {showWizard && (
            <ProductWizard
              onClose={() => {
                setShowWizard(false);
              }}
            />
          )}
        </div>
      </div>
    </div>
  );
};
