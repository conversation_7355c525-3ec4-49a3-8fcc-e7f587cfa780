import { Link } from '@tanstack/react-router';

import { LessonCalendar } from '@/components/lessons/calendar/lesson-calendar';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { useInstructor } from '@/hooks/useInstructor';

export const CalendarViewPage = () => {
  const { data: instructors } = useInstructor();
  console.log('🚀 ~ CalendarViewPage ~ instructors:', instructors);

  return (
    <div className='flex flex-col py-4'>
      <div className='mx-4 flex items-center justify-end gap-2'>
        <div className='mr-auto flex gap-4'>
          <div className='flex flex-col gap-2'>
            <Label htmlFor='arena-filter'>Filter by Arena</Label>
            <Select>
              <SelectTrigger id='arena-filter' className='w-[180px]'>
                <SelectValue placeholder='Select arena' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='indoor'>Indoor Arena</SelectItem>
                <SelectItem value='outdoor'>Outdoor Arena</SelectItem>
                <SelectItem value='round-pen'>Round Pen</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className='flex flex-col gap-2'>
            <Label htmlFor='instructor-filter'>Filter by Instructor</Label>
            <Select>
              <SelectTrigger id='instructor-filter' className='w-[180px]'>
                <SelectValue placeholder='Select instructor' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='john'>John Doe</SelectItem>
                <SelectItem value='sarah'>Sarah Smith</SelectItem>
                <SelectItem value='mike'>Mike Johnson</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className='flex gap-2'>
          <Button asChild variant='outline'>
            <Link to='/lessons/create'>Lesson Scheduling</Link>
          </Button>
          <Button asChild variant='outline'>
            <Link to='/lessons/history'>Lesson History</Link>
          </Button>
        </div>
      </div>
      <LessonCalendar />
    </div>
  );
};
