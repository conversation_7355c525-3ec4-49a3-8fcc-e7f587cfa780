import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useNavigate } from '@tanstack/react-router';
import { Loader2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

import { useHorseMutation } from '@/hooks/useHorseMutation';

// Validation schema based on the horse API types
const createHorseSchema = z.object({
  name: z
    .string()
    .min(1, 'Horse name is required')
    .max(100, 'Name must be less than 100 characters'),
  breed: z
    .string()
    .min(1, 'Breed is required')
    .max(100, 'Breed must be less than 100 characters'),
  age: z
    .number()
    .min(1, 'Age must be at least 1')
    .max(50, 'Age must be less than 50'),
  training_level: z
    .string()
    .min(1, 'Training level is required')
    .max(50, 'Training level must be less than 50 characters'),
  status: z.enum(['available', 'resting', 'injured']).optional(),
  specialties: z.string().optional(),
  suitable_for: z.string().optional(),
  notes: z.string().max(1000, 'Notes must be less than 1000 characters'),
});

type CreateHorseFormData = z.infer<typeof createHorseSchema>;

export const CreateHorsePage = () => {
  const navigate = useNavigate();
  const horseMutation = useHorseMutation();

  const form = useForm<CreateHorseFormData>({
    resolver: zodResolver(createHorseSchema),
    defaultValues: {
      name: '',
      breed: '',
      age: 1,
      training_level: '',
      status: 'available',
      specialties: '',
      suitable_for: '',
      notes: '',
    },
  });

  const onSubmit = async (data: CreateHorseFormData) => {
    try {
      const submitData = {
        ...data,
        specialties: data.specialties
          ? data.specialties
              .split(',')
              .map((s) => s.trim())
              .filter((s) => s.length > 0)
          : undefined,
        suitable_for: data.suitable_for
          ? data.suitable_for
              .split(',')
              .map((s) => s.trim())
              .filter((s) => s.length > 0)
          : undefined,
      };

      await horseMutation.mutateAsync(submitData);

      toast.success('Horse added successfully!', {
        description: `${data.name} has been added to your stable.`,
        duration: 4000,
      });

      // Clear the form for adding another horse
      form.reset({
        name: '',
        breed: '',
        age: 1,
        training_level: '',
        status: 'available',
        specialties: '',
        suitable_for: '',
        notes: '',
      });
    } catch (error) {
      console.error('Error creating horse:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to create horse';
      toast.error('Failed to add horse', {
        description: errorMessage,
        duration: 5000,
      });
    }
  };

  return (
    <div className='min-h-screen space-y-6 bg-gray-50 p-6'>
      {/* Header */}
      {/* <div className='space-y-2'>
        <h1 className='text-2xl font-bold text-gray-900'>Add New Horse</h1>
        <p className='text-sm text-gray-600'>
          Create a new horse profile for your stable
        </p>
      </div> */}

      {/* Form */}
      <div className='w-full'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
            {/* Basic Information Card */}
            <Card className='rounded-2xl border-0 bg-white shadow-sm'>
              <CardHeader>
                <CardTitle>Horse Information</CardTitle>
                <CardDescription>
                  Fill in the details below to add a new horse to your stable.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-6'>
                  {/* Basic Information Grid - 3 columns */}
                  <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
                    <FormField
                      control={form.control}
                      name='name'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Horse Name</FormLabel>
                          <FormControl>
                            <Input placeholder='Enter horse name' {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name='breed'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Breed</FormLabel>
                          <FormControl>
                            <Input
                              placeholder='e.g., Arabian, Thoroughbred'
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name='age'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Age</FormLabel>
                          <FormControl>
                            <Input
                              type='number'
                              placeholder='Enter age'
                              {...field}
                              onChange={(e) => {
                                field.onChange(parseInt(e.target.value) || 0);
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Status and Training Level - 2 columns */}
                  <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
                    <FormField
                      control={form.control}
                      name='status'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Status</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger className='w-full'>
                                <SelectValue placeholder='Select status' />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value='available'>
                                Available
                              </SelectItem>
                              <SelectItem value='resting'>Resting</SelectItem>
                              <SelectItem value='injured'>Injured</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name='training_level'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Training Level</FormLabel>
                          <FormControl>
                            <Input
                              placeholder='e.g., Beginner, Intermediate, Advanced'
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Additional Information */}
                  <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
                    <FormField
                      control={form.control}
                      name='specialties'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Specialties</FormLabel>
                          <FormControl>
                            <Input
                              placeholder='e.g., Jumping, Dressage, Trail Riding (comma separated)'
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name='suitable_for'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Suitable For</FormLabel>
                          <FormControl>
                            <Input
                              placeholder='e.g., Beginners, Advanced Riders (comma separated)'
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Notes Section */}
                  <FormField
                    control={form.control}
                    name='notes'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Notes</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder='Additional notes about the horse...'
                            className='min-h-[100px]'
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Submit Button */}
                  <div className='flex gap-4 pt-6'>
                    <Button
                      type='button'
                      variant='outline'
                      onClick={() => navigate({ to: '/resources/horses' })}
                      className='flex-1 border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                    >
                      Cancel
                    </Button>
                    <Button
                      type='submit'
                      disabled={horseMutation.isPending}
                      className='flex-1 bg-blue-600 text-white hover:bg-blue-700'
                    >
                      {horseMutation.isPending ? (
                        <>
                          <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                          Creating...
                        </>
                      ) : (
                        'Create Horse'
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </form>
        </Form>
      </div>
    </div>
  );
};
