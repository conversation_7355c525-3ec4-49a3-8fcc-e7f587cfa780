import { useState } from 'react';

import { type Horse } from '@/components/resources/horses';
import { HorseDetails } from '@/components/resources/horses/horse-details';
import { Horses } from '@/components/resources/horses/index';

export const HorsesPage = () => {
  const [selectedHorse, setSelectedHorse] = useState<Horse | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedHorse(null);
  };
  return (
    <>
      <Horses
        setSelectedHorse={setSelectedHorse}
        setIsModalOpen={setIsModalOpen}
      />
      <HorseDetails
        horse={selectedHorse}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </>
  );
};
