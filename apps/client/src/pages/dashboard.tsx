import { ArrowUpDown, BarChart3, TrendingUp, Users } from 'lucide-react';
import { useState } from 'react';

import { AdvertisingMetrics } from '@/components/dashboard/advertising-metrics';
import { Churn } from '@/components/dashboard/churn';
import { RecurringRevenue } from '@/components/dashboard/recurring-revenue';
import { SummaryCards } from '@/components/dashboard/summary-cards';
import { Upgrades } from '@/components/dashboard/upgrades';

const navItems = [
  {
    title: 'Recurring Revenue',
    icon: BarChart3,
    id: 'recurring-revenue',
  },
  {
    title: 'Advertising Metrics',
    icon: TrendingUp,
    id: 'advertising-metrics',
  },
  {
    title: 'Churn',
    icon: Users,
    id: 'churn',
  },
  {
    title: 'Upgrades',
    icon: ArrowUpDown,
    id: 'upgrades',
  },
];

export const DashboardPage = () => {
  const [activeTab, setActiveTab] = useState('recurring-revenue');

  const renderActiveTab = () => {
    switch (activeTab) {
      case 'recurring-revenue':
        return <RecurringRevenue />;
      case 'advertising-metrics':
        return <AdvertisingMetrics />;
      case 'churn':
        return <Churn />;
      case 'upgrades':
        return <Upgrades />;
      default:
        return <RecurringRevenue />;
    }
  };

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Top Navigation */}
      <nav className='border-b border-gray-200 bg-white px-4 py-3'>
        <div className='flex flex-wrap gap-2'>
          {navItems.map((item) => (
            <button
              key={item.id}
              onClick={() => {
                setActiveTab(item.id);
              }}
              className={`flex items-center gap-1 rounded-full px-3 py-1 text-sm font-medium transition ${
                activeTab === item.id
                  ? 'bg-indigo-600 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              <item.icon className='h-4 w-4' />
              <span className='hidden sm:inline'>{item.title}</span>
            </button>
          ))}
        </div>
      </nav>

      {/* Main Content */}
      <main className='flex flex-1 flex-col gap-4 p-4'>
        <SummaryCards />
        {renderActiveTab()}
      </main>
    </div>
  );
};
