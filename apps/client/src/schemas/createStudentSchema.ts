import { z } from 'zod';

export const createStudentFormSchema = z.object({
  // Personal Information
  firstName: z
    .string()
    .min(1, 'First name is required')
    .max(30, 'First name must be 30 characters or less'),
  lastName: z
    .string()
    .min(1, 'Last name is required')
    .max(30, 'Last name must be 30 characters or less'),
  email: z
    .string()
    .email('Invalid email format')
    .max(100, 'Email must be 100 characters or less')
    .optional()
    .or(z.literal('')),
  phone: z
    .string()
    .min(7, 'Phone must be at least 7 characters')
    .max(15, 'Phone must be 15 characters or less')
    .optional()
    .or(z.literal('')),
  dateOfBirth: z.string().min(1, 'Date of birth is required'),
  gender: z.enum(['male', 'female', 'other'], {
    required_error: 'Gender is required',
  }),

  // Riding Information
  ridingLevel: z.enum(['beginner', 'intermediate', 'advanced'], {
    required_error: 'Riding level is required',
  }),
  previousExperience: z.string().optional(),
  goals: z.string().optional(),

  // Medical Information
  medicalConditions: z.string().optional(),
  allergies: z.string().optional(),

  // Profile Image
  profileImage: z.string().optional(),

  // Agreements
  waiverSigned: z.boolean().refine((val) => val, 'Waiver must be signed'),
  photoConsent: z.boolean().optional(),
});

export type CreateStudentFormData = z.infer<typeof createStudentFormSchema>;
