import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { AlertTriangle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

import { useAuth } from '@/hooks/useAuthStore';

export const Route = createFileRoute('/unauthorized')({
  component: UnauthorizedPage,
});

function UnauthorizedPage() {
  const navigate = useNavigate();
  const { user, logout, getRedirectUrl } = useAuth();

  const handleLogout = () => {
    logout();
    const redirectUrl = getRedirectUrl();
    void navigate({ to: redirectUrl });
  };

  return (
    <div className='flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8'>
      <div className='w-full max-w-md space-y-8'>
        <Card className='w-full'>
          <CardHeader className='text-center'>
            <div className='mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100'>
              <AlertTriangle className='h-6 w-6 text-red-600' />
            </div>
            <CardTitle className='mt-4 text-xl font-semibold text-gray-900'>
              Access Denied
            </CardTitle>
            <CardDescription>
              You don&apos;t have permission to access this resource.
            </CardDescription>
          </CardHeader>

          <CardContent className='space-y-4'>
            {user && (
              <div className='text-center text-sm text-gray-600'>
                <p>
                  Current role:{' '}
                  <span className='font-semibold'>{user.role}</span>
                </p>
                <p>
                  Studio:{' '}
                  <span className='font-semibold'>{user.studio_id}</span>
                </p>
              </div>
            )}

            <div className='flex flex-col space-y-3'>
              <Button
                onClick={handleLogout}
                variant='default'
                className='w-full'
              >
                Sign Out
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
