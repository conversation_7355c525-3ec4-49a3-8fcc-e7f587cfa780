import { createFileRoute } from '@tanstack/react-router';
import { AlertTriangle } from 'lucide-react';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

const NotFoundPage = () => {
  return (
    <div className='flex min-h-screen items-center justify-center bg-gray-50 px-4'>
      <Card className='w-full max-w-md'>
        <CardHeader className='text-center'>
          <div className='mb-4 flex justify-center'>
            <AlertTriangle className='h-16 w-16 text-red-500' />
          </div>
          <CardTitle className='text-2xl font-bold text-gray-900'>
            404 - Page Not Found
          </CardTitle>
          <CardDescription className='text-gray-600'>
            The page you&apos;re looking for doesn&apos;t exist or the studio ID
            is missing.
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4 text-center'>
          <p className='text-sm text-gray-500'>
            Please check the URL and make sure you have a valid studio ID
            parameter.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export const Route = createFileRoute('/404')({
  component: NotFoundPage,
});
