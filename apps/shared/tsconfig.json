{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    // Environment setup
    "lib": ["ESNext"],
    "target": "ESNext",
    "module": "CommonJS",
    "moduleResolution": "node",

    // Output configuration
    "declaration": true,
    "outDir": "./dist",
    "noEmit": false,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "verbatimModuleSyntax": false,

    // Type checking
    "strict": true,
    "skipLibCheck": true,

    // Additional checks
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "**/*.test.ts", "dist"]
}
