export type typeResultData = {
  studios: {
    id: string;
    name: string | null;
    email: string | null;
    phone: string | null;
    address: string | null;
    logo_url: string | null;
    timezone: string | null;
    currency: string | null;
    created_at: Date;
    updated_at: Date | null;
  }[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
};

export type typeResultError = {
  code: string;
  message: string;
  statusCode: number;
};

export type typeResult = {
  data: null | typeResultData;
  error: null | typeResultError;
};
