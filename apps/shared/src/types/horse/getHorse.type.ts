export type typeQueryParams = {
  page?: string;
  limit?: string;
  studio_id?: string;
  status?: 'available' | 'resting' | 'injured';
  training_level?: string;
};

export type typeHorse = {
  id: string;
  name: string;
  breed: string;
  status: 'available' | 'resting' | 'injured';
  age: number;
  training_level: string;
  specialties?: string[] | null;
  suitable_for?: string[] | null;
  notes_id?: string | null;
  studio_id: string;
  created_at: Date;
  updated_at: Date | null;
};

export type typeResultData = {
  message: string;
  horses: typeHorse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
};

export type typeResultError = {
  code: string;
  message: string;
  statusCode: number;
};

export type typeResult = {
  data: null | typeResultData;
  error: null | typeResultError;
};
