export type typePayloadInput = {
  name: string;
  breed: string;
  status?: 'available' | 'resting' | 'injured';
  age: number;
  training_level: string;
  specialties?: string[];
  suitable_for?: string[];
  notes: string;
  // studio_id is automatically added by the controller
};

export type typePayload = {
  name: string;
  breed: string;
  status: 'available' | 'resting' | 'injured';
  age: number;
  training_level: string;
  specialties?: string[];
  suitable_for?: string[];
  notes: string;
  studio_id: string;
};

export type typeResultData = {
  message: string;
  horse: {
    id: string;
    name: string;
    breed: string;
    status: 'available' | 'resting' | 'injured';
    age: number;
    training_level: string;
    specialties?: string[] | null;
    suitable_for?: string[] | null;
    notes: string | null;
    studio_id: string;
    created_at: Date;
    updated_at: Date | null;
  };
};

export type typeResultError = {
  code: string;
  message: string;
  statusCode: number;
};

export type typeResult = {
  data: null | typeResultData;
  error: null | typeResultError;
};
