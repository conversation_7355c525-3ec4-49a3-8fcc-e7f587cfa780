export type typeResultData = {
  username: string;
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone?: string;
  address?: string;
  role: 'admin' | 'instructor' | 'student' | 'parent';
  date_of_birth?: string;
  profile_image?: string;
  emergency_contact?: string;
  isPasswordSet: boolean;
};

export type typeResultError = {
  code: string;
  message: string;
  statusCode: number;
};

export type typeResult = {
  data: null | typeResultData;
  error: null | typeResultError;
};
