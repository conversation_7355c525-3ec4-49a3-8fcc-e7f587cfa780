export type typePayload = {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone?: string;
  address?: string;
  date_of_birth?: string;
  profile_image?: string;
  emergency_contact_phone?: string;
  emergency_contact_name?: string;
};

export type typeResultData = {
  message: string;
};

export type typeResultError = {
  code: string;
  message: string;
  statusCode: number;
};

export type typeResult = {
  data: null | typeResultData;
  error: null | typeResultError;
};
