export type typePayload = {
  email: string;
  current_password: string;
  new_password: string;
};

export type typeResultData = {
  user: {
    email: string;
    first_name: string;
    last_name: string;
    role:
      | 'admin'
      | 'instructor'
      | 'student'
      | 'parent'
      | 'super-admin'
      | 'owner';
    isPasswordSet: boolean;
    studio_id: string;
    phone?: string;
    address?: string;
    date_of_birth?: string;
    profile_image?: string;
    emergency_contact?: string;
    country_code?: string;
    created_at: Date;
    deleted_at?: Date;
    updated_at?: Date;
  };
  message: string;
};

export type typeResultError = {
  code:
    | 'USER_NOT_FOUND'
    | 'INVALID_CURRENT_PASSWORD'
    | 'DB_ERROR'
    | 'VALIDATION_ERROR'
    | 'UNEXPECTED_ERROR'
    | 'STUDIO_NOT_FOUND'
    | 'ACCOUNT_DISABLED';
  message: string;
  statusCode: number;
};

export type typeResult = {
  data: typeResultData | null;
  error: typeResultError | null;
};
