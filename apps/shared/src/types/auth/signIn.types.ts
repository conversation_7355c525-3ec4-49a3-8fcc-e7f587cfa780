export type typePayload = {
  email: string;
  password: string;
};

export type typeResultData = {
  user: {
    email: string;
    first_name: string;
    last_name: string;
    role:
      | 'admin'
      | 'instructor'
      | 'student'
      | 'parent'
      | 'super-admin'
      | 'owner';
    isPasswordSet: boolean;
    studio_id: string;
    phone?: string;
    address?: string;
    date_of_birth?: string;
    profile_image?: string;
    emergency_contact?: string;
    country_code?: string;
    created_at: Date;
    deleted_at?: Date;
    updated_at?: Date;
  };
  accessToken: string;
  refreshToken: string;
};

export type typeResultError = {
  code: string;
  message: string;
  statusCode: number;
};

export type typeResult = {
  data: null | typeResultData;
  error: null | typeResultError;
};
