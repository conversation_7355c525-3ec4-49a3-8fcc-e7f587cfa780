export namespace GetParents {
  export interface QueryParams {
    page?: number;
    limit?: number;
  }

  export interface Parent {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
    studio_id: string;
    role: string;
    phone: string | null;
    country_code: string | null;
    address: string | null;
    date_of_birth: string | null;
    profile_image: string | null;
    emergency_contact_phone: string | null;
    emergency_contact_name: string | null;
    created_at: string;
    updated_at: string | null;
  }

  export interface Pagination {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  }

  export interface ResponseData {
    parents: Parent[];
    pagination: Pagination;
  }

  export interface ResponseError {
    code: string;
    message: string;
    statusCode: number;
  }

  export interface ApiResponse {
    data: ResponseData | null;
    error: ResponseError | null;
  }
}
