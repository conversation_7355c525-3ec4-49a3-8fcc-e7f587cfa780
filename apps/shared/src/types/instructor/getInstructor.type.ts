export type typeQueryParams = {
  page?: string;
  limit?: string;
  studio_id?: string;
  specialization?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  experience?: string;
  availability?: string;
};

export type typeInstructor = {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  profile_image?: string | null;
  specialization: string[]; // array of strings
  qualification?: string | null;
  teaching_philosophy?: string | null;
  experience: number; // years of experience
  availability: string[]; // array of days
  bio_notes?: string | null;
  studio_id: string;
  created_at: Date;
  updated_at: Date | null;
};

export type typeResultData = {
  message: string;
  instructors: typeInstructor[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
};

export type typeResultError = {
  code: string;
  message: string;
  statusCode: number;
};

export type typeResult = {
  data: null | typeResultData;
  error: null | typeResultError;
};
