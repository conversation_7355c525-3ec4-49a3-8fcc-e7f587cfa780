export type typePayload = {
  id: string;
  studio_id: string;
};

export type typeResultData = {
  message: string;
  instructor: {
    id: string;
    first_name: string;
    last_name: string;
    deleted_at: Date;
  };
};

export type typeResultError = {
  code: string;
  message: string;
  statusCode: number;
};

export type typeResult = {
  data: null | typeResultData;
  error: null | typeResultError;
};
