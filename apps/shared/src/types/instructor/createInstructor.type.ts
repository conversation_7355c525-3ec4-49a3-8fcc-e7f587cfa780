export type typePayloadInput = {
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  profile_image?: string;
  specialization: string[]; // array of strings
  qualification?: string;
  teaching_philosophy?: string;
  experience: number; // years of experience
  availability: string[]; // array of days: ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun']
  bio_notes?: string;
  // studio_id is automatically added by the controller
};

export type typePayload = {
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  profile_image?: string;
  specialization: string[]; // array of strings
  qualification?: string;
  teaching_philosophy?: string;
  experience: number; // years of experience
  availability: string[]; // array of days: ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun']
  bio_notes?: string;
  studio_id: string;
};

export type typeResultData = {
  message: string;
  instructor: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    phone_number: string;
    profile_image?: string | null;
    specialization: string[]; // array of strings
    qualification?: string | null;
    teaching_philosophy?: string | null;
    experience: number; // years of experience
    availability: string[]; // array of days
    bio_notes?: string | null;
    studio_id: string;
    created_at: Date;
    updated_at: Date | null;
  };
};

export type typeResultError = {
  code: string;
  message: string;
  statusCode: number;
};

export type typeResult = {
  data: null | typeResultData;
  error: null | typeResultError;
};
