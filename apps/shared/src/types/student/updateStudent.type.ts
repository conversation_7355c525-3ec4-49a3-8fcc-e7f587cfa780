export type typePayloadInput = {
  first_name?: string;
  last_name?: string;
  riding_level?: 'beginner' | 'intermediate' | 'advanced';
  date_of_birth?: string; // ISO date string
  gender?: 'male' | 'female' | 'other';
  skill_id?: string; // UUID, optional
  profile_image?: string;
  email?: string;
  phone?: string;
  country_code?: string;
  previous_experience?: string;
  riding_goals?: string;
  medical_conditions?: string;
  allergies?: string;
};

export type typePayload = typePayloadInput & {
  parent_id: string; // Added by handler
};

export type typeResultData = {
  message: string;
  student: {
    id: string;
    first_name: string;
    last_name: string;
    riding_level: 'beginner' | 'intermediate' | 'advanced';
    date_of_birth: string;
    gender: 'male' | 'female' | 'other';
    skill_id: string | null;
    parent_id: string;
    profile_image: string | null;
    email: string | null;
    phone: string | null;
    country_code: string | null;
    previous_experience: string | null;
    riding_goals: string | null;
    medical_conditions: string | null;
    allergies: string | null;
    created_at: Date;
    updated_at: Date | null;
  };
};

export type typeResultError = {
  code: string;
  message: string;
  statusCode: number;
};

export type typeResult = {
  data: null | typeResultData;
  error: null | typeResultError;
};
