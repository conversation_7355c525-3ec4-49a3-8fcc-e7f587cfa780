export type typePayload = {
  first_name: string;
  last_name: string;
  date_of_birth: string;
  gender: 'male' | 'female' | 'other';
  skill_id?: string;
  parent_id: string;
  profile_image?: string;
  email?: string;
  phone?: string;
  country_code?: string;
};

export type typeResultData = {
  message: string;
  student?: {
    id: string;
    first_name: string;
    last_name: string;
    date_of_birth: string;
    gender: 'male' | 'female' | 'other';
    skill_id: string | null;
    parent_id: string;
    profile_image: string | null;
    email: string | null;
    phone: string | null;
    country_code: string | null;
  };
};

export type typeResultError = {
  code: string;
  message: string;
  statusCode: number;
};

export type typeResult = {
  data: null | typeResultData;
  error: null | typeResultError;
};
