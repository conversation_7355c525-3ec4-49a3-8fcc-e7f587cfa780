export type typeResultData = {
  students: {
    id: string;
    first_name: string;
    last_name: string;
    riding_level: 'beginner' | 'intermediate' | 'advanced';
    date_of_birth: string;
    gender: 'male' | 'female' | 'other';
    skill_id: string | null;
    parent_id: string;
    profile_image: string | null;
    email: string | null;
    phone: string | null;
    country_code: string | null;
    previous_experience: string | null;
    riding_goals: string | null;
    medical_conditions: string | null;
    allergies: string | null;
    created_at: Date;
    updated_at: Date | null;
  }[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
};

export type typeResultError = {
  code: string;
  message: string;
  statusCode: number;
};

export type typeResult = {
  data: null | typeResultData;
  error: null | typeResultError;
};
