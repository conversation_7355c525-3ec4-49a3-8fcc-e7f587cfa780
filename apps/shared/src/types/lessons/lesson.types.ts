// Base lesson types
export type LessonType = 'single-lesson' | 'recurring-lesson' | 'camp';
export type LessonStatus =
  | 'scheduled'
  | 'completed'
  | 'cancelled'
  | 'in_progress';
export type RecurrenceType = 'weekly' | 'biweekly' | 'monthly';

// Recurrence pattern structure
export interface RecurrencePattern {
  type: RecurrenceType;
  interval: number; // every N weeks/months
  daysOfWeek: number[]; // 0-6, Sunday = 0
  endDate?: string;
  occurrences?: number; // alternative to endDate
}

// Curriculum item reference
export interface CurriculumItem {
  id: string;
  name: string;
  skill_level: string;
}

// Attachment structure
export interface LessonAttachment {
  id: string;
  name: string;
  url: string;
  type: string; // file type
  size: number;
  uploaded_at: string;
}

// Core lesson data structure
export interface Lesson {
  id: string;
  title: string;
  lesson_type: LessonType;
  arena_id?: string;
  arena?: {
    id: string;
    name: string;
    location?: string;
  };
  date: string;
  start_time: string;
  end_time: string;
  duration_minutes: number;
  max_students: number;
  current_students: number;
  status: LessonStatus;
  notes?: string;
  require_form: boolean;
  require_payment: boolean;
  price?: number;
  created_by: number;
  parent_lesson_id?: number;
  recurrence_pattern?: RecurrencePattern;
  curriculum_items?: CurriculumItem[];
  attachments?: LessonAttachment[];
  created_at: string;
  updated_at?: string;
}

// Lesson creation payload
export interface CreateLessonPayload {
  title: string;
  lesson_type: LessonType;
  arena_id?: number;
  date: string;
  start_time: string;
  end_time: string;
  duration_minutes: number;
  max_students: number;
  notes?: string;
  require_form?: boolean;
  require_payment?: boolean;
  price?: number;
  recurrence_pattern?: RecurrencePattern;
  curriculum_items?: string[]; // curriculum IDs
  instructor_ids: string[];
}

// Lesson update payload
export interface UpdateLessonPayload {
  title?: string;
  arena_id?: string;
  date?: string;
  start_time?: string;
  end_time?: string;
  duration_minutes?: number;
  max_students?: number;
  status?: LessonStatus;
  notes?: string;
  require_form?: boolean;
  require_payment?: boolean;
  price?: number;
  curriculum_items?: string[];
}

// Lesson list filters
export interface LessonFilters {
  instructor_id?: string;
  arena_id?: string;
  status?: LessonStatus;
  lesson_type?: LessonType;
  date_from?: string;
  date_to?: string;
  student_id?: string; // for getting lessons a student is enrolled in
  page?: number;
  limit?: number;
}

// API response types
export interface LessonListResponse {
  lessons: Lesson[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface LessonResponse {
  lesson: Lesson;
}

// Standard API result types
export interface LessonResultError {
  code: string;
  message: string;
  statusCode: number;
}

export interface CreateLessonResult {
  data: LessonResponse | null;
  error: LessonResultError | null;
}

export interface GetLessonResult {
  data: LessonResponse | null;
  error: LessonResultError | null;
}

export interface GetLessonsResult {
  data: LessonListResponse | null;
  error: LessonResultError | null;
}

export interface UpdateLessonResult {
  data: LessonResponse | null;
  error: LessonResultError | null;
}

export interface DeleteLessonResult {
  data: { message: string } | null;
  error: LessonResultError | null;
}
