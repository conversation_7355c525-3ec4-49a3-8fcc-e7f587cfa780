// Arena types
export interface Arena {
  id: number;
  name: string;
  location?: string;
  description?: string;
  capacity: number;
  equipment?: string[]; // parsed from JSON
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

export interface ArenaListResponse {
  arenas: Arena[];
  total: number;
}

// Curriculum types
export interface Curriculum {
  id: number;
  name: string;
  description?: string;
  skill_level: string;
  duration_minutes?: number;
  prerequisites?: number[]; // parsed from JSON
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

export interface CurriculumListResponse {
  curriculum: Curriculum[];
  total: number;
}

// Instructor types
export interface Instructor {
  id: number;
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
  specialties?: string[];
  experience_years?: number;
  is_active: boolean;
}

export interface InstructorListResponse {
  instructors: Instructor[];
  total: number;
}

// Lesson instructor assignment
export interface LessonInstructor {
  id: number;
  lesson_id: number;
  instructor_id: number;
  instructor?: Instructor;
  time_block_start?: string;
  time_block_end?: string;
  assigned_horses?: string[];
  role: 'primary' | 'assistant' | 'substitute';
  notes?: string;
  created_at: string;
  updated_at?: string;
}

// API result types
export interface ResourceResultError {
  code: string;
  message: string;
  statusCode: number;
}

export interface GetArenasResult {
  data: ArenaListResponse | null;
  error: ResourceResultError | null;
}

export interface GetCurriculumResult {
  data: CurriculumListResponse | null;
  error: ResourceResultError | null;
}

export interface GetInstructorsResult {
  data: InstructorListResponse | null;
  error: ResourceResultError | null;
}
