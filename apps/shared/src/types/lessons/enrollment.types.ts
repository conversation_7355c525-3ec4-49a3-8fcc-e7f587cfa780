// Enrollment status types
export type AttendanceStatus = 'pending' | 'present' | 'absent' | 'excused';
export type PaymentStatus = 'paid' | 'unpaid' | 'partial' | 'refunded';

// Student information for enrollment
export interface EnrolledStudent {
  id: string;
  name: string;
  email: string;
  age?: number;
  level?: string;
  avatar?: string;
}

// Lesson enrollment data structure
export interface LessonEnrollment {
  id: string;
  lesson_id: string;
  student_id: string;
  student?: EnrolledStudent;
  enrollment_date: string;
  attendance_status: AttendanceStatus;
  payment_status: PaymentStatus;
  payment_amount?: number;
  assigned_horse?: string;
  special_requirements?: string;
  notes?: string;
  created_at: string;
  updated_at?: string;
}

// Enrollment creation payload
export interface CreateEnrollmentPayload {
  student_id: string;
  payment_amount?: number;
  assigned_horse?: string;
  special_requirements?: string;
  notes?: string;
}

// Enrollment update payload
export interface UpdateEnrollmentPayload {
  attendance_status?: AttendanceStatus;
  payment_status?: PaymentStatus;
  payment_amount?: number;
  assigned_horse?: string;
  special_requirements?: string;
  notes?: string;
}

// Enrollment list response
export interface EnrollmentListResponse {
  enrollments: LessonEnrollment[];
  total: number;
  lesson_id: number;
}

// API result types
export interface EnrollmentResultError {
  code: string;
  message: string;
  statusCode: number;
}

export interface CreateEnrollmentResult {
  data: { enrollment: LessonEnrollment } | null;
  error: EnrollmentResultError | null;
}

export interface GetEnrollmentsResult {
  data: EnrollmentListResponse | null;
  error: EnrollmentResultError | null;
}

export interface UpdateEnrollmentResult {
  data: { enrollment: LessonEnrollment } | null;
  error: EnrollmentResultError | null;
}

export interface DeleteEnrollmentResult {
  data: { message: string } | null;
  error: EnrollmentResultError | null;
}
