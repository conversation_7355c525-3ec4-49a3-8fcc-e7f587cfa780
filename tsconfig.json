{
  "compilerOptions": {
    // Environment setup & latest features
    "lib": ["ESNext", "DOM", "DOM.Iterable"],
    "target": "ESNext",
    "outDir": "./dist",
    "module": "ESNext",
    "moduleDetection": "force",
    "jsx": "react-jsx",
    "allowJs": true,

    // Path resolution
    "baseUrl": "./",
    "paths": {
      "@server/*": ["./apps/server/src/*"],
      "@parent/*": ["./apps/parent/src/*"],
      "@client/*": ["./apps/client/src/*"],
      "@shared/*": ["./apps/shared/src/*"]
    },

    // Module resolution
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "verbatimModuleSyntax": true,

    // Strictness and best practices
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "experimentalDecorators": true,

    // Output control
    "skipLibCheck": true,

    // Optional strict flags (disabled by default)
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noPropertyAccessFromIndexSignature": false
  }
}
