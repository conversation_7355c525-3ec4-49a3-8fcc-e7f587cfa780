name: Monorepo CI

on:
  push:
    branches: [main, staging]
  pull_request:
    branches: [main, staging]

# Add permissions block
permissions:
  contents: read
  pull-requests: read

jobs:
  changes:
    runs-on: ubuntu-latest
    outputs:
      client: ${{ steps.filter.outputs.client }}
      server: ${{ steps.filter.outputs.server }}
      parent: ${{ steps.filter.outputs.parent }}
      shared: ${{ steps.filter.outputs.shared }}
      apps_changed: ${{ steps.filter.outputs.apps_changed }}
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v2
        id: filter
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          filters: |
            client:
              - 'apps/client/**'
              - 'apps/shared/**'
              - 'package.json'
              - 'bun.lockb'
            server:
              - 'apps/server/**'
              - 'apps/shared/**'
              - 'package.json'
              - 'bun.lockb'
            parent:
              - 'apps/parent/**'
              - 'apps/shared/**'
              - 'package.json'
              - 'bun.lockb'
            shared:
              - 'apps/shared/**'
              - 'package.json'
              - 'bun.lockb'
            apps_changed:
              - 'apps/**'
              - 'package.json'
              - 'bun.lockb'

  setup:
    needs: changes
    if: needs.changes.outputs.apps_changed == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest
      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.bun/install/cache
            node_modules
          key: ${{ runner.os }}-bun-${{ hashFiles('**/bun.lockb') }}
          restore-keys: |
            ${{ runner.os }}-bun-
      - run: bun install

  build-shared:
    needs: [changes, setup]
    if: needs.changes.outputs.shared == 'true' || needs.changes.outputs.server == 'true' || needs.changes.outputs.client == 'true' || needs.changes.outputs.parent == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest
      - name: Restore cache
        uses: actions/cache/restore@v3
        with:
          path: |
            ~/.bun/install/cache
            node_modules
          key: ${{ runner.os }}-bun-${{ hashFiles('**/bun.lockb') }}
      - run: bun install
      - run: bun run build:shared
      - name: Upload shared build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: shared-dist
          path: apps/shared/dist/
          retention-days: 1

  build-server:
    needs: [changes, setup, build-shared]
    if: needs.changes.outputs.server == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest
      - name: Download shared build artifacts
        uses: actions/download-artifact@v4
        with:
          name: shared-dist
          path: apps/shared/dist/
      - name: Restore cache
        uses: actions/cache/restore@v3
        with:
          path: |
            ~/.bun/install/cache
            node_modules
          key: ${{ runner.os }}-bun-${{ hashFiles('**/bun.lockb') }}
      - run: bun install
      - run: bun run build:server

  build-client:
    needs: [changes, setup, build-shared]
    if: needs.changes.outputs.client == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest
      - name: Download shared build artifacts
        uses: actions/download-artifact@v4
        with:
          name: shared-dist
          path: apps/shared/dist/
      - name: Restore cache
        uses: actions/cache/restore@v3
        with:
          path: |
            ~/.bun/install/cache
            node_modules
          key: ${{ runner.os }}-bun-${{ hashFiles('**/bun.lockb') }}
      - run: bun install
      - run: bun run build:client

  build-parent:
    needs: [changes, setup, build-shared]
    if: needs.changes.outputs.parent == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest
      - name: Download shared build artifacts
        uses: actions/download-artifact@v4
        with:
          name: shared-dist
          path: apps/shared/dist/
      - name: Restore cache
        uses: actions/cache/restore@v3
        with:
          path: |
            ~/.bun/install/cache
            node_modules
          key: ${{ runner.os }}-bun-${{ hashFiles('**/bun.lockb') }}
      - run: bun install
      - run: bun run build:parent
